const glob = require('glob');
const path = require('path');
const webpack = require('webpack');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');
// const HappyPack = require('happypack');
// const os = require('os');
// const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });
// const th = require('thread-loader');

const cacheLoader = {
  loader: 'cache-loader',
};

const cores = require('os').cpus().length;
console.log('%c [ cores ]-17', 'font-size:13px; background:pink; color:#bf2c9f;', cores);

// const threadLoader = {
//   loader: 'thread-loader',
//   options: {
//     workers: 2,
//   },
// };
// th.warmup(
//   {
//     // pool options, like passed to loader options
//     // must match loader options to boot the correct pool
//   },
//   [
//     // modules to load
//     // can be any module, i. e.
//     'vue-loader',
//     'babel-loader',
//     'eslint-loader',
//   ],
// );

const stats = require('./webpack-stats');
const pkg = require('./package.json');
const versionConfig = require('../version');

if (pkg.version !== versionConfig.pkgVersion) {
  throw 'Error: package version mismatch!';
}

// 加个key，方便在build里找到这个插件
const cssPlugin = new MiniCssExtractPlugin({
  filename: 'tcic-ui-[name].css',
});
cssPlugin.key = 'MiniCssExtractPlugin';

module.exports = {
  entry: {
    main: glob.sync('./src/component/vue-component/**/*.js').concat('./src/pages/class/class.js'),
    login: glob.sync('./src/pages/login/login.js'),
    mainIndex: glob.sync('./src/pages/main/index.js'),
    externalCourseware: glob.sync('./src/pages/externalCourseware/externalCourseware.js'),
  },

  stats,

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'tcic-ui-[name].js',
  },

  resolve: {
    alias: {
      '@vue': 'vue/dist/vue.esm.js',
      '@assets': path.join(__dirname, 'src/assets'),
      '@config': path.join(__dirname, 'src/config'),
      '@util': path.join(__dirname, 'src/util'),
      '@core': path.join(__dirname, 'src/component/core'),
      '@component': path.join(__dirname, 'src/component'),
      '@commonVueComponent': path.join(__dirname, 'src/component/common-vue-component'),
      '@vueComponent': path.join(__dirname, 'src/component/vue-component'),
      '@CoTeachingComponent': path.join(__dirname, 'src/component/vue-component/co-teaching-component'),
      '@OneOnOneComponent': path.join(__dirname, 'src/component/vue-component/one-on-one-component'),
      '@': path.join(__dirname, 'src'),
    },
    extensions: ['.ts', '.js', '.vue'],
  },

  module: {
    noParse: [/.*\.min\.js$/],
    rules: [{
      test: /\.vue$/,
      use: [
        // cacheLoader,
        // threadLoader,
        {
          loader: 'vue-loader',
          // loader: 'happypack/loader?id=vue',
          // options: {
          //   cacheDirectory
          // },
        },
      ],
      exclude: /node_modules/,
    }, {
      test: /\.js$/,
      use: [
        // cacheLoader,
        // threadLoader,
        // {
        //   loader: 'eslint-loader',
        // },
        {
          loader: 'babel-loader',
          options: {
            // cacheDirectory: true, // 'xxx'
            configFile: path.resolve(__dirname, '.babelrc'),
          },
        },
      ],
      exclude: (filePath) => {
        if (filePath.indexOf('node_modules') > -1) {
          const includeModule = ['pretty-bytes',
            // 'moment',
            'screenfull'];
          const reg = new RegExp(includeModule.join('|'));
          const result = filePath.match(reg);
          if (result && result.length) {
            return false;
          }
          return true;
        }
        return false;
      },
      // loader: 'happypack/loader?id=babel',

    },
    // TODO: eslint-loader顺序,是否重复耗时..
    {
      test: /\.js$/,
      exclude: /node_modules/,
      use: [
        // cacheLoader,
        // threadLoader,
        {
          loader: 'eslint-loader',
        },
      ],
    },
    // https://github.com/vueuse/vueuse/issues/718
    {
      test: /\.mjs$/,
      include: /node_modules/,
      type: "javascript/auto"
    },
    {
      test: /\.css$/,
      // loader: 'happypack/loader?id=css',
      use: [

        MiniCssExtractPlugin.loader,
        'css-loader',
      ],
    }, {
      test: /\.less$/,
      // loader: 'happypack/loader?id=less',
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        'less-loader',
      ],
    }, {
      test: /\.(woff|woff2|eot|ttf)$/,
      use: [{
        loader: 'url-loader',
        options: {
          esModule: false,
          name: '[path][name].[ext]',
        },
      }],
    }, {
      test: /\.(png|svg|gif)$/,
      use: [{
        loader: 'url-loader',
        options: {
          esModule: false,
          limit: 20480, // 小于20k base64
          name: '[path][name].[ext]',
        },
      }],
    }, {
      test: /\.(otf|mp3)$/,
      use: [
        'file-loader',
      ],
    }],
  },


  plugins: [

    // new webpack.ProvidePlugin({
    //   // global: 'window',
    //   'aegis': 'window.aegis',
    // }),

    new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
    new ESLintPlugin({
      extensions: ['vue', 'js'],
      fix: false,
      emitError: true,
      emitWarning: true,
      failOnError: true,
    }),
    new webpack.DefinePlugin({
      VERSION_CONFIG: JSON.stringify(versionConfig),
    }),
    new VueLoaderPlugin(),
    cssPlugin,
  ],

  // externals: {
  //   // 'aegis': 'Aegis',
  // }
};
