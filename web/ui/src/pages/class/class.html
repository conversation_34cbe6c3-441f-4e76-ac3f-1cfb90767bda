<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <link rel="dns-prefetch" href="https://res.qcloudtiw.com" />
    <link rel="dns-prefetch" href="https://log.qcloudtiw.com" />
    <style>
        body {
            background-color: #14181d;
        }

        .a-loading {
            position: fixed;
            top: 50%;
            margin-top: -21px;
            width: 100%;
            text-align: center;
            position: absolute;
        }

        .a-loading-text {
            padding-top: 50px;
            color: #006eff;
            margin: 3px 0px;
            font-size: 14px;
        }
    </style>
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover" />
    <title>Loading...</title>
    <link id="favicon" rel="icon" href="static/livelogo.png" type="image/x-icon" />
    <style type="text/css">
        .msg-box-component .el-dialog .el-dialog__body .message {
            white-space: pre-line;
        }

        .invalid-browser {
            padding: 20px;
            background-color: #fff;
            color: #000;
        }
    </style>
</head>

<body>
<div id="app">
    <div id="temp-loading-cover" class="a-loading">
        <div class="a-loading-text" id="loadText">Loading...</div>
    </div>
</div>
<script>
    /***
     * 如果取不到webrtc接口说明浏览器版本较老，执行后续脚本会语法异常.停留在当前加载页.
     * 如果能执行后续脚本.这段逻辑不会产生太大影响.仅仅是出现提示语.
     **/
    if (!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia)) {
        let cnTip = '浏览器不支持，请切换浏览器后重试。推荐使用最新版本的谷歌浏览器。';
        let enTip = 'Unsupported Browser. Please switch to other brower and try again. The latest version of Google Chrome is recommended.';
        if (/(\?|&)platform=(ios|android)/.test(location.search)) {
            cnTip = '设备不支持，请更换设备后重试。';
            enTip = 'Unsupported device, please switch to other device and try again.';
        }
        if ((navigator.language || navigator.browserLanguage).toLowerCase() === 'zh-cn') {
            document.getElementById('loadText').innerHTML = cnTip;
        } else {
            document.getElementById('loadText').innerHTML = enTip;
        }
    }
    // user performance logs
    window.__loadSucceededResources__ = [];
    window.__loadFailedResources__ = [];
    window.ResourcesLoadTime = {};
    window.ResourcesLoadNotSuccess = [];
    window.LCPData = [];
    window.ResourcesLoadSuccess = [];
    window.addEventListener('load', () => {
        window.LoadAllTime = Date.now() - window.LoadStartTime;
        const performanceEntries = performance.getEntriesByType('resource');
        performanceEntries.forEach((entry) => {
            if (entry.responseStatus !== 200 && entry.responseStatus !== 204) {
                window.ResourcesLoadNotSuccess.push({
                    name: entry.name,
                    status: entry.responseStatus,
                })
            } else {
                if (entry.initiatorType != 'xmlhttprequest') {
                    window.ResourcesLoadSuccess.push({
                        name: entry.name,
                        status: entry.responseStatus,
                        startTime: entry.startTime, // 加载开始时间（毫秒）
                        duration: entry.duration, // 加载持续时间（毫秒）
                        transferSize: entry.transferSize,  // 传输的字节数
                        encodedBodySize: entry.encodedBodySize,// 编码后的主体大小（字节）
                        decodedBodySize: entry.decodedBodySize,// 解码后的主体大小（字节）
                    })
                }
                window.ResourcesLoadTime[`${entry.name}`] = `${entry.duration}ms`
            }
        });
    });

    //  place holder for native sdk. 防止因class.js异步加载,调用前方法undefined.
    window.manualJoinClass = function (paramsString) {
        window.manualJoinClass.paramsString = paramsString;
    }
    window.LoadStartTime = Date.now();
    if ('PerformanceObserver' in window) {
        // 创建 PerformanceObserver 实例
        const observer = new PerformanceObserver((list) => {
            // 获取所有 LCP 条目
            const entries = list.getEntries();
            // 处理每个 LCP 条目
            entries.forEach((entry) => {
                window.LCPData.push(entry)
            });
        });


        observer.observe({ type: 'largest-contentful-paint', buffered: true });
    }
</script>
<style>
    * {
        user-select: none;
        padding: 0;
        margin: 0;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        /* prevent callout to copy image, etc when tap to hold */
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        /* prevent tap highlight color / shadow */
    }

    html,
    body {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    /*
      * 不知道为啥这里要弄个黑色，桌面共享时候透明的背景会因为这个不生效
      body {
        background-color: var(--primary-color, #14181d);
      } */

    #app {
        height: 100%;
        width: 100%;
        position: relative;
        overflow: hidden;
        touch-action: none;
    }

    input {
        user-select: auto;
        -webkit-user-select: auto;
    }

    /* 定义TCIC组件的默认样式 */
    .tcic-component-container {
        overflow: hidden;
        /* 最外层默认忽略鼠标事件 */
        pointer-events: none;
    }

    * {
        /* 所有元素的pointer-events属性取初始值，而不是从父元素继承 */
        pointer-events: initial;
    }

    .tcic-component-container:not(board-component):not(layout-component)>* * {
        /* 所有元素的pointer-events属性取初始值，而不是从父元素继承 */
        pointer-events: initial;
    }

    textarea {
        cursor: auto;
    }

    /*通用布局 end*/
    /*滚动条 start*/
    ::-webkit-scrollbar,
    body::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background-color: transparent;
        border-radius: 8px;
    }

    ::-webkit-scrollbar-thumb,
    body::-webkit-scrollbar-thumb {
        background: rgba(138, 144, 153, 1);
        border-radius: 8px;
    }

    ::-webkit-scrollbar-track,
    body::-webkit-scrollbar-track {
        border-radius: 8px;
    }

    ::-webkit-scrollbar-corner,
    body::-webkit-scrollbar-corner {
        background-color: #1D2330;
    }

    .small-screen ::-webkit-scrollbar {
        width: 3px;
        height: 3px;
        background-color: transparent;
        border-radius: 3px;
    }

    .small-screen ::-webkit-scrollbar-thumb {
        background: rgba(138, 144, 153, 0.3);
        border-radius: 3px;
    }

    .small-screen ::-webkit-scrollbar-track {
        border-radius: 3px;
    }

    /*Toast样式*/
    .middle-center {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .toasted-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /* 提高toast层级 */
        z-index: 40000;
    }

    .toasted.toasted-primary.stage-toast {
        border-radius: 4px;
        background-color: #000000;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        line-height: 16px;
        padding: 16px 16px;
        max-width: 300px;
        width: auto;
    }

    .toasted-container.bottom-center,
    .toasted-container.top-center {
        align-items: center !important;
        top: 10%;
    }
</style>
<% htmlWebpackPlugin.files.js.forEach(function(js) {
const filename = js.split('/').pop();
%>
<script>
    window.__expectedResources__ = window.__expectedResources__ || [];
    window.__expectedResources__.push("<%= filename %>");
</script>
<% }); %>

<!---注入css--->
<% htmlWebpackPlugin.files.css.forEach(function(css){ %>
<%=`<link href="${css}" rel="stylesheet">`%>
<%})%>
<script type="text/javascript" src="static/libs/aegis.min.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('aegis.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('aegis.min.js')"
></script>

<!-- 引入webview -->
<script type="text/javascript" src="static/libs/jweixin/jweixin_1.6.0.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('jweixin_1.6.0.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('jweixin_1.6.0.js')"
></script>

<script>
    const expectedResources = [
        '{TCIC_SDK_FILENAME}',
        'cos.min.js',
        'video.min.js',
        'video-js.min.css',
        'axios.min.js',
        'tcplayer.v4.9.0.min.css',
        'tcplayer.v4.9.0.min.js',
    ].concat(window.__expectedResources__);

    window.ResourceMonitor = {
        resources: [],
        failures: [],
        successes: [],
        totalCount: 0,
        successCount: 0,
        failureCount: 0,
        allSuccess: true,
    };

    function getQueryParam(key) {
        const params = new URLSearchParams(window.location.search);
        return params.get(key) || '';
    }

    function getFileNameFromURL(url) {
        try {
            return url.split('/').pop().split('?')[0];
        } catch (e) {
            return url;
        }
    }

    window.addEventListener('load', async () => {
        const userId = getQueryParam('userid');
        const classId = getQueryParam('classid');
        const schoolId = getQueryParam('schoolid');

        window.ResourceMonitor = {
            resources: [],
            successes: [],
            failures: [],
            allSuccess: true,
            totalCount: 0,
        };

        const succeeded = new Set(window.__loadSucceededResources__ || []);
        const failed = new Set(window.__loadFailedResources__ || []);
        const entries = performance.getEntriesByType('resource');
        const entryMap = new Map();

        for (const entry of entries) {
            const fileName = getFileNameFromURL(entry.name);
            if (!entryMap.has(fileName)) {
                entryMap.set(fileName, []);
            }
            entryMap.get(fileName).push(entry);
        }

        const resourceData = [];
        const startTimes = [];
        const endTimes = [];

        for (const fileName of expectedResources) {
            const isSuccess = succeeded.has(fileName);
            const isFailure = failed.has(fileName);
            const status = isSuccess ? 'success' : isFailure ? 'error' : 'missing';

            const entryList = entryMap.get(fileName) || [];
            const chosen = entryList.length === 1 ? entryList[0] : entryList.find(e => e.transferSize > 0) || entryList[0];

            const item = {
                name: chosen?.name || fileName,
                fileName,
                type: fileName.split('.').pop().toLowerCase(),
                initiatorType: chosen?.initiatorType || 'unknown',
                duration: chosen?.duration || 0,
                transferSize: chosen?.transferSize || 0,
                encodedBodySize: chosen?.encodedBodySize || 0,
                decodedBodySize: chosen?.decodedBodySize || 0,
                deliveryType: chosen?.deliveryType || 'unknown',
                hasPerformanceData: !!chosen,
                status,
                success: isSuccess,
            };

            resourceData.push(item);
            window.ResourceMonitor.resources.push(item);

            if (isSuccess) {
                window.ResourceMonitor.successes.push(item);
            } else {
                window.ResourceMonitor.failures.push(item);
                window.ResourceMonitor.allSuccess = false;
            }

            window.ResourceMonitor.totalCount++;

            if (chosen) {
                startTimes.push(chosen.startTime);
                endTimes.push(chosen.responseEnd);
            }
        }

        window.ResourceMonitor.successCount = window.ResourceMonitor.successes.length;
        window.ResourceMonitor.failureCount = window.ResourceMonitor.failures.length;

        const totalDuration =
            startTimes.length > 0 && endTimes.length > 0
                ? Math.max(...endTimes) - Math.min(...startTimes)
                : 0;

        const reportData = {
            timestamp: Date.now(),
            allSuccess: window.ResourceMonitor.allSuccess,
            total: window.ResourceMonitor.totalCount,
            success: window.ResourceMonitor.successCount,
            failure: window.ResourceMonitor.failureCount,
            totalDuration,
            userId,
            classId,
            schoolId,
            failedResources: window.ResourceMonitor.failures,
            successResources: window.ResourceMonitor.successes,
        };

        console.log('[TCICLoadWebStaticResourcesFinished Report Data]', reportData);
        sendCLSReport(reportData);
    });


    function sendCLSReport(data) {
        const {
            userId = '',
            classId = '',
            schoolId = '',
            timestamp = Date.now(),
            ...rest
        } = data;

        const payload = {
            items: [
                {
                    dev_platform: 'web',
                    dev_user_agent: navigator.userAgent,
                    app_module: 'TCICWebLoad',
                    report_module: 'TCICWebLoad',
                    app_business: 'tcic_sdk',
                    app_new_enter_id: parseInt(schoolId, 10) || 0,
                    app_room_id: parseInt(classId, 10) || 0,
                    app_user_id: String(userId),
                    action_name: 'TCICLoadWebStaticResourcesFinished',
                    action_param: JSON.stringify(rest),
                    report_level: data.allSuccess ? 'info' : 'error',
                    report_time: timestamp,
                    report_global_random: `${timestamp}${Math.floor(Math.random() * 1000000)}` // 拼接时间戳和随机数
                }
            ]
        };

        fetch('https://report-log-lv1.api.qcloud.com/nv.cgi', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json, text/plain, */*'
            },
            body: JSON.stringify(payload)
        }).then(res => {
            if (!res.ok) {
                console.error('[TCICLoadWebStaticResourcesFinished Report Failed]', res.status, res.statusText);
            } else {
                console.log('[TCICLoadWebStaticResourcesFinished Report Sent]');
            }
        }).catch(err => {
            console.error('[TCICLoadWebStaticResourcesFinished Report Error]', err);
        });
    }
</script>
<script>
    try {
        // 信任父域
        document.domain = 'qcloudclass.com';
    } catch (error) {
        console.error('%c [ error ]-217', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    if (!/zh(-\w+)?/g.test(window.navigator.language)) {
        document.title = 'loading...';
    }

    /**
     * js里为了不影响js的hash去掉了 BUILD_VERSION，这里放一个兜底，以免后面哪里要用
     **/
    window.__TCIC_UI_BUILD_VERSION = '{TCIC_UI_BUILD_VERSION}';
    console.log('TCIC_UI_BUILD_VERSION', window.__TCIC_UI_BUILD_VERSION);
    window.__TCIC_NODE_SERVER_HTML_VERSION = 'class-{TCIC_NODE_SERVER_HTML_VERSION}.html';
    console.log('TCIC_NODE_SERVER_HTML_VERSION', window.__TCIC_NODE_SERVER_HTML_VERSION);
    /**
     * 从第三方进入页面如果携带用户鉴权信息，则清除缓存，防止历史鉴权信息引起异常
     **/
    let userid = window.location.href.split('&').find(item => /^userid=/.test(item));
    const token = window.location.href.split('&').find(item => /^token=/.test(item));
    if (token && userid) {
        localStorage.removeItem(userid.replace('userid=', ''));
        localStorage.removeItem('token');
    }
    if (!userid) {
        userid = localStorage.getItem('userid')
    }
    if (typeof userid !== 'string') {
        userid = '';
    }
    userid = userid.replace('userid=', '');
    /**
     * 补充上报日志
     * */
    if (window.Aegis) {
        const isTest = [/localhost/, /dev-*/, /test-*/].find(item => window.location.host.match(item));
        const ignoreDomains = [/alicdn\.com/,
            /aliyuncs\.com/, /tailwindcss\.com/,
            /amazon\.com/, /google\.com/, /baidu\.com/, /aliexpress\.com/,
            /lifeiapp\.com/, /kolify\.cn/,/zhixinoj\.com/,
            /vectorstock\.com/, /pingjs\.qq\.com/, /51\.la/, /pingtas\.qq\.com/,
            /liveplay\.myqcloud\.com/,
            /^script load fail.*\.html$/i];
        const aegis = new Aegis({
            env: isTest ? Aegis.environment.test : Aegis.environment.production,
            id: isTest ? 'oEOmJCiujdSbpMkdBg' : 'xE3XvHQvdEzwQYOnQz', // 项目ID，即上报id
            uin: userid, // 用户唯一 ID（可选）
            reportApiSpeed: true, // 接口测速
            reportAssetSpeed: true, // 静态资源测速
            spa: true, // spa 页面开启
            beforeRequest: function (data) {
                if (data && data.logs && data.logType === 'log') {
                    const msg = data.logs.msg;
                    if (msg && ignoreDomains.find(item => item.test(msg))) {
                        return false;
                    }
                }
                if (data && data.logs && data.logType === 'speed') {
                    let url = data.logs.url || '';
                    let status = data.logs.status;
                    let duration = data.logs.duration;
                    const type = data.logs.type;
                    if (ignoreDomains.find(item => item.test(url))) {
                        return false;
                    }
                    // aegis 还不支持iframe html加载的检测 课件异常在白板侧监听上报处理
                    if (url.indexOf('.html') > -1 && type === 'static') {
                        return false;
                    }

                    // 下面几个域名的状态正常上报日志过滤掉
                    const innerDomains = [/report-log-lv1\.api\.qcloud\.com/,
                        /api\.my-imcloud\.com/, /member\/heartbeat/];
                    if ((status === 200 || status === 201) && innerDomains.find(item => item.test(url))) {
                        return false;
                    }
                    // 正常加载的图先过滤掉
                    if (status === 200 && /\.(png|jpg|svg|ico|gif)$/i.test(url) && duration < 3000) {
                        return false;
                    }
                    // console.log('aegis log:', data.logs.url, data);
                }
                return data
            },
            hostUrl: 'https://rumt-zh.com'
        })
        window._aegis_ = aegis
        // 新加坡地区可以选择 https://rumt-sg.com 作为上报域名。
        // 硅谷地区可以选择 https://rumt-us.com 作作为上报域名。
    }
    console.log('::::schoolInfo: enty main');
    var makeSureReloadOnce = true;
    window.addEventListener(
        'error',
        function (e) {
            if (!makeSureReloadOnce) return;
            var el = e.target; // 出现重要资源加载失败的错误，需要重新初始化
            if (el && el.tagName && ['SCRIPT', 'LINK'].includes(el.tagName.toUpperCase())) {
                var url = el.src || el.href;
                var importantResources = ['TEduBoard.min.js', 'axios.min.js', 'tcic-sdk.min.js', 'tcic-ui-main'];
                for (var i = 0; i < importantResources.length; ++i) {
                    if (url.includes(importantResources[i])) {
                        setTimeout(function () {
                            if (window.Electron && window.Electron.reloadWindow) {
                                // electron端重新加载窗口
                                window.Electron.reloadWindow('resource-load-failed', url);
                            }
                        }, 1000); // 延迟1秒重试
                        makeSureReloadOnce = false; // 确保html生命周期内只重试一次
                        break;
                    }
                }
            }
        },
        true
    );
    //  place holder for native sdk. 防止因class.js异步加载,调用前方法undefined.
    window.manualJoinClass = function (paramsString) {
        window.manualJoinClass.paramsString = paramsString;
    }
    // adaptor webrtc Not-support-env
    if("RTCPeerConnection" in window && !window.RTCPeerConnection) {
        delete window.RTCPeerConnection;
    }
    if("webkitRTCPeerConnection" in window && !window.webkitRTCPeerConnection) {
        delete window.webkitRTCPeerConnection;
    }
    if("RTCIceGatherer" in window && !window.RTCIceGatherer) {
        delete window.RTCIceGatherer;
    }
    window.addEventListener('DOMContentLoaded', () => {
    });
</script>
<script src="static/libs/cos/5.1.0/cos.min.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('cos.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('cos.min.js')"
></script>
<script src="static/libs/board/2.9.7-bugfix/TEduBoard.min.js?t=20250521"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('TEduBoard.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('TEduBoard.min.js')"
></script>
<script src="static/libs/board/third/videojs/1.0.0/video.min.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('video.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('video.min.js')"
></script>
<link href="static/libs/board/third/videojs/1.0.0/video-js.min.css" rel="stylesheet"
      onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('video-js.min.css')"
      onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('video-js.min.css')"
>
<script src="static/libs/axios/axios.min.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('axios.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('axios.min.js')"
></script>
<script src="static/tcic/{TCIC_MAIN_VERSION}/sdk/{TCIC_SDK_FILENAME}"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('{TCIC_SDK_FILENAME}')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('{TCIC_SDK_FILENAME}')"
></script>
<link href="static/libs/tcplayer/tcplayer.v4.9.0.min.css" rel="stylesheet"
      onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('tcplayer.v4.9.0.min.css')"
      onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('tcplayer.v4.9.0.min.css')"
/>
<script src="static/libs/tcplayer/tcplayer.v4.9.0.min.js"
        onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('tcplayer.v4.9.0.min.js')"
        onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('tcplayer.v4.9.0.min.js')"
></script>
<!---注入js--->
<% htmlWebpackPlugin.files.js.forEach(function(js){ %>
<%=`<script src="${js}"
            onerror="window.__loadFailedResources__ && window.__loadFailedResources__.push('${js}')"
            onload="window.__loadSucceededResources__ && window.__loadSucceededResources__.push('${js}')"
>
</script>`%>
<%})%>

</body>

</html>