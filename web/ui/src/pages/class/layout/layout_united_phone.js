import Constant from '@/util/Constant';
import { LayoutBase } from '@/pages/class/layout/layout_base';
import Util from '@/util/Util';
import { getBoardInnerRect, getBoardToolLayoutProps } from './util';
import { getStartedStatus } from '@/util/class';

const HeaderHeight = 45;
const DocFooterHeight = 40;
const DocFooterBottom = 10;

export class LayoutUnitedPhone extends LayoutBase {
  constructor() {
    super('LayoutUnitedPhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutUnitedPhone();
    }
    return this.instance;
  }
  initLayout() {
    // 老师可以课前预览，其他人非1v0才需要课前预览
    const classInfo = this.sdk.getClassInfo();
    this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, !!this.sdk.isTeacher() || classInfo.maxRtcMember !== 0);
    // 初始化机顶盒按键模块 btn-margin
    const isTv = (this.sdk.isAndroid() && this.sdk.isMobileNative()) || this.sdk.isPad();
    if (isTv) {
      window.tbm.init();
      const wid = window.tbm.pushWindow('App');
      window.tbm.pushTarget('header', ['back', 'info', 'menu', 'button', 'subMenu', 'layout'], wid);
      window.tbm.pushTarget('setting', ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'micSelect', 'micVolume', 'speaker', 'speakerVolumes', 'button'], wid);
      window.tbm.pushTarget('tips', ['default'], wid);
      window.tbm.pushTarget('notice', ['title', 'button'], wid);
      window.tbm.pushTarget('handup', ['default'], wid);
    }

    const isAIClass = this.sdk.isAiRoom();

    // 课堂类型和角色不会变，记下来不用每次都判断
    let layoutFuncName = '';

    // 新布局的实现在 layout-component 组件里分发
    const isLandscapeVideoSmallClass = this.sdk.getClassInfo().maxRtcMember <= 1
      && this.sdk.isVideoOnlyClass()
      && this.sdk.getParams('defaultDeviceOrientation') === '0';

    if (this.sdk.isMobile() && (
      this.sdk.isBigRoom()
      || isLandscapeVideoSmallClass)
    ) {
      layoutFuncName = 'updateNewLayout';
    } else if (isAIClass) {
      layoutFuncName = 'updateAILayout';
    } else if (this.sdk.isCoTeachingClass()) {   // 双师课堂
      layoutFuncName = 'updateCoteachingLayout';
    } else if (this.sdk.isOneOnOneClass()) {   // 1v1课堂
      layoutFuncName = 'updateOneOnOneLayout';
    } else if (this.sdk.isVideoOnlyClass()) {   // 纯视频课堂
      layoutFuncName = 'updateVideoOnlyLayout';
    } else if (this.sdk.isUnitedRTCClass() || this.sdk.isTeacherOrAssistant()) {
      // RTC课堂
      layoutFuncName = 'updateRtcLayout';
    } else {
      // 旧布局的实现
      if (this.sdk.isCoTeachingClass()) {   // 双师课堂
        layoutFuncName = 'updateCoteachingLayout';
      } else if (this.sdk.isOneOnOneClass()) {   // 1v1课堂
        layoutFuncName = 'updateOneOnOneLayout';
      } else if (this.sdk.isVideoOnlyClass()) {   // 纯视频课堂
        layoutFuncName = 'updateVideoOnlyLayout';
      } else if (this.sdk.isUnitedRTCClass() || this.sdk.isTeacherOrAssistant()) {
        // RTC课堂
        layoutFuncName = 'updateRtcLayout';
      } else {
        // 标准课堂
        layoutFuncName = 'updateLiveLayout';
      }
    }
    TCIC.SDK.instance.reportLog('LayoutUnitedPhone-initLayout', layoutFuncName);
    this.updateLayoutInner = this[layoutFuncName].bind(this);
  }
  updateLayout() {
    return this.updateLayoutInner();
  }

  updateAILayout() {
    const promiseArray = [];
    const headerHeight = HeaderHeight;
    const { safeTop } = this.getSafeArea();
    const headerHeightCss =  `calc(${safeTop} + ${headerHeight}px)`;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: `${safeTop}`,
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ai-audio-component', {
      top: `${headerHeightCss}`,
      height: `calc(100% - ${headerHeightCss})`,
      display: 'block',
    }));
    return Promise.all(promiseArray);
  }

  /**
   * 双师课堂
   */
  updateCoteachingLayout() {
    const promiseArray = [];
    const headerHeight = HeaderHeight;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-toast-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: 'auto',
      height: 'auto',
      display: 'block',
    }));
    const gridTop = headerHeight;
    promiseArray.push(this.sdk.updateComponent('ct-video-component', {
      top: `${gridTop}px`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${gridTop}px)`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-component', {
    }));
    return Promise.all(promiseArray);
  }

  updateNewLayout() {
    const promiseArray = [];
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const classLayout = this.sdk.getClassLayout();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, false);

    const safeTop = 'env(safe-area-inset-top)';

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(false);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !this.sdk.getState(Constant.TStateRecordMode);
    const headerHeight = headerVisible ? HeaderHeight : 0;
    const headerHeightCss = `calc(${safeTop} + ${headerHeight}px)`;
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: safeTop,
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: headerVisible ? 'block' : 'none',
      }));
    }

    promiseArray.push(this.sdk.updateComponent('layout-component', {
      top: `calc(${headerHeightCss})`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${headerHeightCss})`,
      display: 'block',
    }));

    // 更新白板工具栏布局


    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isPortrait = deviceOrientation === TCIC.TDeviceOrientation.Portrait;
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const singleVideoWidth = isPortrait ? 115 : 160;
    const videoWrapWidth = isFullscreen ? 0 : singleVideoWidth;

    const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
    const DocThumbnailHeight = 129;
    const isClassStarted = getStartedStatus();
    let thumbnailVisible = !deviceDetecting
        && showThumbnail
        && (isShowBoardToolComponent || !isClassStarted)
        && !showVodPlayer;
    if (!TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
      thumbnailVisible = false;
    }
    const thumbnailHeight = thumbnailVisible ? DocThumbnailHeight : 0;
    const element = document.createElement('div');
    element.style.position = 'absolute';
    element.style.top = headerHeightCss;
    document.body.appendChild(element);
    const computedHeaderHeight = parseFloat(window.getComputedStyle(element).top);
    document.body.removeChild(element);
    const mainContentTop = computedHeaderHeight;
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
    const isTeacher = this.sdk.isTeacher();
    const isThreeLayout = (classLayout === TCIC.TClassLayout.Three);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isShowBoardToolComponent = this.sdk.getState(Constant.TStateIsShowBoardToolComponent, false);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const boardParentRect = {
      top: mainContentTop,
      left: 0,
      width: document.body.clientWidth - videoWrapWidth,
      height: document.body.clientHeight - mainContentTop - thumbnailHeight,
    };
    const boardInnerRect = getBoardInnerRect(boardParentRect);
    const boardToolVisible = !deviceDetecting
        && !isRecordMode
        && (isTeacher || !showScreenPlayer)
        && isShowBoardToolComponent
        && !(isThreeLayout && isBigVideoMode) // 三分布局把Video切换到主视图，白板在右上角时，不显示白板工具
        && !showVodPlayer;
    const { layoutProps: boardToolLayoutProps, ...boardToolOtherProps } = getBoardToolLayoutProps({
      visible: boardToolVisible,
      isScreenShareOnElectron: false,
      isScreenShareAdvanceMode,
      boardParentRect,
      boardInnerRect,
      isSmallScreen: true,
    });
    const { boardToolHeight, boardToolTopInCSS, boardToolLeftInCSS } = boardToolOtherProps;
    // console.log('update BoardTool in Rtc', boardToolLayoutProps, boardToolOtherProps);
    promiseArray.push(this.sdk.updateComponent('board-tool-component', boardToolLayoutProps));

    return Promise.all(promiseArray);
  }

  /**
   * 1v1课堂
   */
  updateOneOnOneLayout() {
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isPortraitBigRoom = deviceOrientation === TCIC.TDeviceOrientation.Portrait && TCIC.SDK.instance.isBigRoom();
    const promiseArray = [];
    const deviceDetecting = false;
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const isJoined = this.sdk.getState(TCIC.TMainState.Joined_Class);
    const classLayout = this.sdk.getClassLayout();
    const isVideoDocClass = this.sdk.isVideoDocClass();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, false);

    const { safeTop } = this.getSafeArea();

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(false);

    // 更新导航栏布局
    const headerVisible = isJoined;
    const headerHeight = headerVisible || TCIC.SDK.instance.isPortraitClass() ? HeaderHeight : 0;
    const headerHeightCss = `calc(${safeTop} + ${headerHeight}px)`;
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: headerVisible ? 'block' : 'none',
      }));
    }

    if (TCIC.SDK.instance.isPortraitClass()) {
      const clientHeight = Math.max(document.documentElement.clientHeight, document.documentElement.clientWidth);
      const { safeTop } = this.getSafeArea();
      promiseArray.push(this.sdk.updateComponent('tabbar-component', {
        display: 'block',
        height: '90px',
        top: `calc(${clientHeight}px - 90px)`,
      }), this.sdk.updateComponent('header-component', {
        top: safeTop,
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: 'block',
      }));
    }

    const hasSideArea = isVideoDocClass;
    let rightSideW = 0;

    // TODO: 判断下面这个判断是否需要删除
    if (isVideoDocClass && isPortraitBigRoom) {
      // 有白板，三分布局，右侧是用户列表
      const oooVideoWrapWidth = isPortraitBigRoom ? 0 : 286;
      const oooVideoWrapHeight = 160;
      rightSideW = !isFullscreen ? oooVideoWrapWidth : 0;  // 三分屏右侧宽度.
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: headerHeightCss,
        left: '0',
        width: '100%',
        height: `${oooVideoWrapHeight}px`,
        display: 'block',
      }));
    } else if (isVideoDocClass) {
      // 有白板，三分布局，右侧是用户列表
      const oooVideoWrapWidth = 286;
      rightSideW = !isFullscreen ? oooVideoWrapWidth : 0;  // 三分屏右侧宽度
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: headerHeightCss,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${oooVideoWrapWidth}px`,
        height: `calc(100% - ${headerHeightCss})`,
        display: 'block',
      }));
    } else {
      // 没有白板，主体是 oooVideoWrap
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: `${headerHeight}px`,
        left: '0',
        width: `calc(100% - ${rightSideW}px)`,
        height: '100%',
        display: 'block',
        zIndex: 1,
      }));
    }

    // 更新边栏切换按钮布局
    const sideToggleButtonVisible = !deviceDetecting && hasSideArea && !isPortraitBigRoom;
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: '80%',
      left: `calc(100% - ${rightSideW}px - 40px)`,
      width: '40px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    // 更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
    this.updateDocArea(promiseArray, {
      headerHeightCss,
      contentRight: rightSideW,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeight,
      autoFitScale,
    });

    return Promise.all(promiseArray);
  }

  /**
   * 标准课堂
   */
  updateLiveLayout() {
    document.body.style.backgroundColor = this.sdk.isUnitedClass() ? 'black' : 'white';
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    if (deviceOrientation === TCIC.TDeviceOrientation.Portrait) {
      return this.updatePortraitLiveLayout();
    }
    return this.updateLandscapeLiveLayout();
  }
  /** 竖屏直播 */
  updatePortraitLiveLayout() {
    const promiseArray = [];
    this.sdk.setState(Constant.TStateHeaderVisible, true);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const curLayout = this.sdk.getClassLayout();
    const bodyBounds = document.body.getBoundingClientRect();
    const headerHeight = HeaderHeight;
    let safeTop = '0px';
    let safeBottom = '0px';
    if (this.sdk.isIOS()) {
      safeTop = 'env(safe-area-inset-top)';
      safeBottom = 'env(safe-area-inset-bottom)';
      const versions = this.sdk.getIOSVersion();
      // evn在ios11.2以下的兼容性
      if (versions.length === 2
          && parseInt(versions[0], 10) <= 11
          && parseInt(versions[1], 10) <= 2) {
        safeTop = 'constant(safe-area-inset-top)';
        safeBottom = 'constant(safe-area-inset-bottom)';
      }
    }
    // header
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: safeTop,
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    // 课堂状态
    const boardHeight = bodyBounds.width * 9 / 16;
    promiseArray.push(this.sdk.updateComponent('live-status-component', {
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0',
      width: '100%',
      height: `${boardHeight}px`,
      display: 'block',
    }));

    // 在线人数
    promiseArray.push(this.sdk.updateComponent('live-numbers-component', {
      // width: '40px',
      // height: '40px',
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0px',
      display: 'block',
    }));
    // 旋转按钮
    promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
      width: '40px',
      height: '40px',
      top: `calc(${safeTop} + ${headerHeight + boardHeight - 40 - 9}px)`,
      left: `calc(${bodyBounds.width - 40 - 16}px)`,
      display: 'block',
    }));

    // 主显示窗
    const mainLayout = {
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0',
      width: '100%',
      height: `${boardHeight}px`,
      display: 'none',
      zIndex: 1,
    };
    // 画中画时， 浮窗位于左下角
    const width = 128; const height = 72;
    const top = bodyBounds.width * 9 / 16 - height + headerHeight;
    let floatLayout = {
      top: `calc(${safeTop} + ${top}px)`,
      left: '0px',
      width: `${width}px`,
      height: `${height}px`,
      zIndex: 300, // 视频全屏时需要调整zIndex
      display: 'block',
    };
    let teacherVideoLayout = floatLayout;
    let boardLayout = mainLayout;
    if (curLayout === TCIC.TClassLayout.VideoDoc || curLayout === TCIC.TClassLayout.Video) {
      // 手机文档+视频或纯视频
      floatLayout = {  display: 'none' };
      teacherVideoLayout = mainLayout;
      boardLayout = floatLayout;
    } else {
      if (isBigVideoMode) {
        teacherVideoLayout = mainLayout;
        boardLayout = floatLayout;
      } else {
        teacherVideoLayout = floatLayout;
        boardLayout = mainLayout;
      }
    }
    const classState = this.sdk.getState(window.TCIC.TMainState.Class_Status);
    if (TCIC.TClassStatus.Already_Start !== classState) {
      teacherVideoLayout.display = 'none';
    }
    promiseArray.push(this.sdk.updateComponent('teacher-component', teacherVideoLayout));
    if (curLayout !== TCIC.TClassLayout.VideoDoc) {
      // 手机竖屏时文档+视频布局，白板在tab栏里，所以不需要updateComponent('board-component'）
      promiseArray.push(this.sdk.updateComponent('board-component', boardLayout));
    }

    floatLayout.zIndex = 310;
    floatLayout.style = 'pointer-events: auto;';
    promiseArray.push(this.sdk.updateComponent('big-video-switch-component', floatLayout));

    // 讨论区
    const discussLayout = {
      top: `calc(${safeTop} + ${headerHeight + boardHeight}px)`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${headerHeight + boardHeight}px - ${safeBottom} - ${safeTop})`,
      display: 'block',
    };
    if (this.sdk.isUnitedClass()) {
      promiseArray.push(this.sdk.updateComponent('portrait-im-component', discussLayout));
    } else {
      promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', discussLayout));
    }
    // 屏幕分享观看布局和白板一致
    if (curLayout !== TCIC.TClassLayout.VideoDoc) {
      promiseArray.push(this.sdk.updateComponent('screen-player-component', {
        top: `calc(${safeTop} + ${headerHeight}px)`,
        left: '0',
        width: '100%',
        height: `${boardHeight}px`,
      }));
    }

    // promiseArray.push(this.sdk.updateComponent('footer-component', {
    //   display: 'none',
    // }));
    promiseArray.push(this.sdk.updateComponent('live-component', {
      top: `calc(${safeTop} + ${headerHeight}px)`,
      right: '0',
      width: '100%',
      height: `${boardHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 是否正在邀请我
    const isInvitingMe = this.sdk.getState(Constant.TStateLiveTeacherInvitingMe, false);
    promiseArray.push(this.sdk.updateComponent('invite-stage-dialog-component', {
      display: isInvitingMe ? 'block' : 'none',
    }));

    // 滚动消息组建
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      display: 'none',
    }));
    return Promise.all(promiseArray);
  }
  /** 横屏直播 */
  updateLandscapeLiveLayout() {
    const promiseArray = [];
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const curLayout = this.sdk.getClassLayout();
    // header
    promiseArray.push(this.sdk.updateComponent('header-component', {
      display: 'block',
    }));
    // 课堂状态
    promiseArray.push(this.sdk.updateComponent('live-status-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 在线人数
    promiseArray.push(this.sdk.updateComponent('live-numbers-component', {
      // width: '40px',
      // height: '40px',
      top: '45px',
      left: '0px',
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
      display: 'none',
    }));
    // 讨论区
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
      display: 'none',
    }));
    // 屏幕分享观看布局和白板一致
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    }));

    // footer
    const footerHeight = DocFooterHeight;
    const footerBottom = DocFooterBottom;
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      left: '0px',
      top: `calc(100% - ${footerHeight + footerBottom}px)`,
      width: '100%',
      display: 'block',
      height: `${footerHeight}px`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    const quickMsgBottom = footerHeight + footerBottom + 10;
    // 滚动消息组建
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      left: '0px',
      top: '0px',
      width: '100%',
      height: `calc(100% - ${quickMsgBottom}px)`,
      display: 'block',
    }));

    // 主显示窗
    const mainLayout = {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'none',
      zIndex: 1,
    };
    const width = 128; const height = 72; const margin = 14;
    const bodyBounds = document.body.getBoundingClientRect();
    const enableStage = this.sdk.getState(TCIC.TMainState.Enable_Stage, false);
    const onStaging = this.sdk.getState(TCIC.TMainState.Stage_Status, false);
    const isLiveClass = this.sdk.isLiveClass();
    const left = 198 + (isLiveClass && (enableStage || onStaging) ? (94 + 10) : 0);
    const top = bodyBounds.height - margin - height;
    let floatLayout = {
      top: `${top}px`,
      left: `${left}px`,
      width: `${width}px`,
      height: `${height}px`,
      zIndex: 300,
      display: 'block',
    };
    if (curLayout === TCIC.TClassLayout.VideoDoc) {
      // App在文档+视频模式下会将board-component移到introduction-discuss-component中，这里需要移回来
      const boardDom = this.sdk.getComponent('board-component');
      const root = document.getElementById('app');
      if (boardDom.parentNode !== root) {
        root.appendChild(boardDom);
      }
      const screenPlayerDom = this.sdk.getComponent('screen-player-component');
      if (screenPlayerDom.parentNode !== root) {
        root.appendChild(screenPlayerDom);
      }
    }
    let teacherVideoLayout = floatLayout;
    let boardLayout = Object.assign({}, mainLayout, { transform: 'scale(1)', transformOrigin: 'center' });
    if (curLayout === TCIC.TClassLayout.Video) {
      // 纯视频
      floatLayout = {  display: 'none' };
      floatLayout.display = 'none';
      teacherVideoLayout = mainLayout;
      boardLayout = floatLayout;
    } else {
      if (isBigVideoMode) {
        teacherVideoLayout = mainLayout;
        boardLayout = Object.assign({}, floatLayout);;
        const scale = width / document.body.clientWidth;
        boardLayout.width = '100%';
        boardLayout.height = '100%';
        // 用scale的方式，解决白板在切换时出现闪烁的问题
        boardLayout.transform = `scale(${scale})`;
        boardLayout.transformOrigin = 'top left';
      } else {
        teacherVideoLayout = floatLayout;
      }
    }
    const classState = this.sdk.getState(window.TCIC.TMainState.Class_Status);
    if (TCIC.TClassStatus.Already_Start !== classState) {
      teacherVideoLayout.display = 'none';
    }
    promiseArray.push(this.sdk.updateComponent('teacher-component', teacherVideoLayout));
    promiseArray.push(this.sdk.updateComponent('board-component', boardLayout));

    floatLayout.zIndex = 310;
    promiseArray.push(this.sdk.updateComponent('big-video-switch-component', floatLayout));

    promiseArray.push(this.sdk.updateComponent('live-component', {
      top: '0',
      right: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 是否正在邀请我
    const isInvitingMe = this.sdk.getState(Constant.TStateLiveTeacherInvitingMe, false);
    promiseArray.push(this.sdk.updateComponent('invite-stage-dialog-component', {
      display: isInvitingMe ? 'block' : 'none',
    }));

    return Promise.all(promiseArray);
  }

  /**
   * 更新纯视频课堂布局
   */
  updateVideoOnlyLayout() {
    const promiseArray = [];
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const classLayout = this.sdk.getClassLayout();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, false);

    const { safeTop } = this.getSafeArea();

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(false);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !this.sdk.getState(Constant.TStateRecordMode);
    const headerHeight = headerVisible ? HeaderHeight : 0;
    const headerHeightCss = `calc(${safeTop} + ${headerHeight}px)`;
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: safeTop,
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: headerVisible ? 'block' : 'none',
      }));
    }

    if (TCIC.SDK.instance.isPortraitClass()) {
      const clientHeight = Math.max(document.documentElement.clientHeight, document.documentElement.clientWidth);
      // header
      promiseArray.push(this.sdk.updateComponent('tabbar-component', {
          display: 'block',
          height: '90px',
          top: `calc(${clientHeight}px - 90px)`,
        }));
    }

    // 更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
    this.updateDocArea(promiseArray, {
      headerHeightCss,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeightCss,
      autoFitScale,
    });

    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isPortraitClass = this.sdk.isPortraitClass();
    const isTeacher = this.sdk.isTeacher();
    const isPortraitClassTeacher = isPortraitClass && isTeacher;
    const isLandscapClassStudent = !isPortraitClass && !isTeacher;
    if ((TCIC.SDK.instance.isMobile() && deviceOrientation === TCIC.TDeviceOrientation.Portrait) && (isPortraitClassTeacher || isLandscapClassStudent)) {
        const bodyBounds = document.body.getBoundingClientRect();
        const boardHeight = bodyBounds.width * 9 / 16;
        // 旋转按钮
        promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
          width: '40px',
          height: '40px',
          top: `calc(${safeTop} + ${headerHeight + boardHeight - 40 - 9}px)`,
          left: `calc(${bodyBounds.width - 40 - 16}px)`,
          display: 'block',
        }));
    } else {
      promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
        display: 'none',
      }));
    }


    return Promise.all(promiseArray);
  }

  /**
   * 互动课堂 ( - 同原互动课布局)
   */
  updateRtcLayout() {
    const promiseArray = [];
    const deviceDetecting = false;
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isJoined = this.sdk.getState(TCIC.TMainState.Joined_Class);
    const isPortrait = deviceOrientation === TCIC.TDeviceOrientation.Portrait;
    const classLayout = this.sdk.getClassLayout();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, false);

    const { safeTop } = this.getSafeArea();

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(false);

    // 更新导航栏布局
    const headerVisible = isJoined;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    const headerHeightCss = `calc(${safeTop} + ${headerHeight}px)`;
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: safeTop,
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: headerVisible ? 'block' : 'none',
      }));
    }

    // 更新视频列表布局
    const videoVisible = !deviceDetecting;
    const singleVideoWidth = isPortrait ? 115 : 160;
    const sideToggleButtonWidth = 15;
    const videoWrapWidth = isFullscreen ? 0 : singleVideoWidth;  // 视频占用空间
    const videoWrapWidthWithToggleInCSS = `${singleVideoWidth + sideToggleButtonWidth}px`; // 视频展示大小
    const videoWrapHeightInCSS = isPortrait ? '30%' : `calc(100% - ${headerHeightCss})`;
    promiseArray.push(this.sdk.updateComponent('videowrap-component', {
      top: headerHeightCss,
      left: `calc(100% - ${videoWrapWidthWithToggleInCSS})`,
      width: videoWrapWidthWithToggleInCSS,
      height: videoWrapHeightInCSS,
      display: videoVisible ? 'block' : 'none',
      style: 'overflow: visible;',
    })
      .then((success) => {
        if (success) {
          const comp = this.sdk.getComponent('videowrap-component');
          if (comp) {
            comp.getVueInstance().setWrapLayout('right');   // 固定为右侧布局
            comp.getVueInstance().setWrapMode('full');   // 屏幕分享结束后需恢复视频模式
          }
        }
      }));

    promiseArray.push(this.sdk.updateComponent('portrait-im-component', {
      top: `calc(30% + ${headerHeightCss})`,
      left: '0',
      width: '100%',
      height: `calc(70% - ${headerHeightCss} + 5px)`,
      display: isPortrait ? 'block' : 'none',
    }));

    // 更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
    this.updateDocArea(promiseArray, {
      headerHeightCss,
      contentHeightCss: videoWrapHeightInCSS,
      contentRight: videoWrapWidth,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeightCss,
      autoFitScale,
    });

    return Promise.all(promiseArray);
  }

  getSafeArea() {
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isPortrait = deviceOrientation === TCIC.TDeviceOrientation.Portrait;
    let safeTop = '0px';
    let safeBottom = '0px';
    // 不要SafeArea
    const noSafeArea = this.sdk.getParams('nosafearea', false);
    if (!noSafeArea && isPortrait && this.sdk.isMobileNative()) {
      if (this.sdk.isIOS()) {
        safeTop = 'env(safe-area-inset-top)';
        safeBottom = 'env(safe-area-inset-bottom)';
        const versions = this.sdk.getIOSVersion();
        // env 在ios11.2以下的兼容性
        if (versions.length === 2
            && parseInt(versions[0], 10) <= 11
            && parseInt(versions[1], 10) <= 2) {
          safeTop = 'constant(safe-area-inset-top)';
          safeBottom = 'constant(safe-area-inset-bottom)';
        }
      } else if (this.sdk.isAndroid()) {
        safeTop = '45px';
      }
    }
    return { safeTop, safeBottom };
  }

  // 1v1/videoOnly/Rtc 共用，更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
  updateDocArea(promiseArray, { headerHeightCss, contentHeightCss = '', contentTop = 0, contentLeft = 0, contentRight = 0, autoFitScale }) {
    const deviceDetecting = false;
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    const isPortrait = deviceOrientation === TCIC.TDeviceOrientation.Portrait;
    const isVideoDocClass = this.sdk.isVideoDocClass();
    const isPortraitBigOOORoom = deviceOrientation === TCIC.TDeviceOrientation.Portrait && TCIC.SDK.instance.isBigRoom() && this.sdk.isOneOnOneClass();

    // 主体内容区域位置
    const mainContentCss = {
      top: `calc(${headerHeightCss} + ${contentTop}px)`,
      left: `${contentLeft}px`,
      width: `calc(100% - ${contentLeft}px - ${contentRight}px)`,
      height: contentHeightCss || `calc(100% - ${headerHeightCss} - ${contentTop}px)`,
    };

    if (isVideoDocClass) {
      // 更新缩略图布局
      promiseArray.push(this.sdk.updateComponent('thumbnail-component', {
        display: 'none',
      }));

      // 计算白板内部区域位置，给后面的白板工具用
      const element = document.createElement('div');
      element.style.position = 'absolute';
      element.style.top = headerHeightCss;
      document.body.appendChild(element);
      const computedHeaderHeight = parseFloat(window.getComputedStyle(element).top);
      document.body.removeChild(element);
      const mainContentTop = computedHeaderHeight + contentTop;
      const DocThumbnailHeight = 129;
      const isClassStarted = getStartedStatus();
      const isShowBoardToolComponent = this.sdk.getState(Constant.TStateIsShowBoardToolComponent, false);
      const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
      // let thumbnailVisible = !deviceDetecting
      //     && showThumbnail
      //     && (isShowBoardToolComponent || !isClassStarted)
      //     && !showVodPlayer;
      // if (!TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
      //   thumbnailVisible = false;
      // }
      // const thumbnailHeight = thumbnailVisible ? DocThumbnailHeight : 0;
      const thumbnailHeight = 0;
      const boardParentRect = {
        top: mainContentTop,
        left: contentLeft,
        width: document.body.clientWidth - contentLeft - contentRight,
        height: document.body.clientHeight - mainContentTop - thumbnailHeight,
      };
      const boardInnerRect = getBoardInnerRect(boardParentRect);

      // 更新白板布局
      const boardVisible = !deviceDetecting;
      promiseArray.push(this.sdk.updateComponent('board-component', {
        ...mainContentCss,
        display: boardVisible ? 'block' : 'none',
      }));

      // 更新白板工具栏布局
      const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
      const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
      const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
      const isTeacher = this.sdk.isTeacher();
      const classLayout = this.sdk.getClassLayout();
      const isThreeLayout = (classLayout === TCIC.TClassLayout.Three);
      const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
      const boardToolVisible = !deviceDetecting
          && !isRecordMode
          && (isTeacher || !showScreenPlayer)
          && isShowBoardToolComponent
          && !(isThreeLayout && isBigVideoMode) // 三分布局把Video切换到主视图，白板在右上角时，不显示白板工具
          && !showVodPlayer;
      const { layoutProps: boardToolLayoutProps, ...boardToolOtherProps } = getBoardToolLayoutProps({
        visible: boardToolVisible,
        isScreenShareOnElectron: false,
        isScreenShareAdvanceMode,
        boardParentRect,
        boardInnerRect,
        isSmallScreen: true,
      });
      const { boardToolHeight, boardToolTopInCSS, boardToolLeftInCSS } = boardToolOtherProps;
      // console.log('update BoardTool in Rtc', boardToolLayoutProps, boardToolOtherProps);
      promiseArray.push(this.sdk.updateComponent('board-tool-component', boardToolLayoutProps));
      // 进入白板: calc((45px + 10px + (345px - 20px - 320px) / 2))
      // 进入课件: calc((45px + 10px + (216px - 20px - 320px) / 2))
    } else {
      // 更新白板布局
      promiseArray.push(this.sdk.updateComponent('board-component', { display: 'none' }));
    }

    // 更新提示布局（宽高和白板一致）
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      ...mainContentCss,
      display: 'block',
    }));

    // 更新视频播放器
    const vodPlayerVisible = !deviceDetecting
        && showVodPlayer;
    promiseArray.push(this.sdk.updateComponent('vod-player-component', {
      ...mainContentCss,
      display: vodPlayerVisible ? 'block' : 'none',
    }));

    // 屏幕分享观看布局
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      ...mainContentCss,
    }));

    // 更新PPT工具栏布局
    const boardFooterVisible = !deviceDetecting
      && isShowFooterComponent;
    const footerHeight = DocFooterHeight;
    const footerBottom = DocFooterBottom;
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      top: `calc(100% - ${footerHeight + footerBottom}px)`,
      left: mainContentCss.left,
      width: mainContentCss.width,
      display: (boardFooterVisible && (!isPortrait || isPortraitBigOOORoom)) ? 'block' : 'none',
      height: `${footerHeight}px`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    // 滚动消息组件
    const quickMsgBottom = isPortrait ? 90 : footerHeight + footerBottom + 10;
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      left: '0px',
      top: '0px',
      width: '100%',
      height: `calc(100% - ${quickMsgBottom}px)`,
      display: 'block',
    }));

    // 视频工具组件的宽和白板保持一致
    const vodToolVisible = !deviceDetecting
        && showVodPlayer;
    const vodToolScale = autoFitScale;
    const vodToolHeight = 72;
    const vodToolBottomMargin = 24;
    promiseArray.push(this.sdk.updateComponent('vod-tool-component', {
      top: `calc(100% - ${vodToolHeight}px - ${vodToolBottomMargin}px)`,
      left: mainContentCss.left,
      width: mainContentCss.width,
      height: `${vodToolHeight}px`,
      display: vodToolVisible ? 'block' : 'none',
      transform: `scale(${vodToolScale})`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));
  }

  // 1v1/videoOnly/Rtc 共用，更新各种和布局无关的工具组件、弹窗、tip等
  // 注意和component里的 getClassTools 对应
  updateClassTools(promiseArray, { headerHeightCss, autoFitScale }) {
    const classLayout = this.sdk.getClassLayout();

    // 更新奖励动画
    promiseArray.push(this.sdk.updateComponent('trophy-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    }));

    // 更新网络组件位置
    promiseArray.push(this.sdk.updateComponent('network-tips-component', {
      top: headerHeightCss,
      left: 'calc(50% - 208px)',
      width: '416px',
      height: 'auto',
      display: 'block',
    }));

    promiseArray.push(this.sdk.updateComponent('image-preview', {
      display: 'none',
    }));

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      transform: `scale(${clockToolScale})`,
    }));

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      transform: `scale(${timerToolScale})`,
    }));

    // 更新答题器组件
    const quizScale = this.getFitScaleBasedOnHeight(classLayout, 660);
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      transform: `scale(${quizScale})`,
    }));


    // 更新随机选人组件
    // promiseArray.push(this.sdk.updateComponent('random-choose-tool-component', {
    //   width: '96vmin',
    //   left: 'calc(50vw - 48vmin)',
    //   top: '20vh',
    // }));

    // 更新抢答器组件
    // promiseArray.push(this.sdk.updateComponent('seize-answer-component', {
    //   width: '96vmin',
    //   left: 'calc(50vw - 48vmin)',
    //   top: '15vh',
    // }));

    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
  }

  // 各课型共用，进房成功后刷新布局
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    const classLayout = this.sdk.getClassLayout();
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示
    const isSmallScreen = this.sdk.isMobile() && !this.sdk.isPad();
    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = this.sdk.isTeacher() ? 469 : 424;
    const clockHeight = 390;
    // 未避免学生端定时器和计时器两个组件重合，稍微错开位置
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(40% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 600 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(60% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));


    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('interact-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: isSmallScreen ? 'none' : 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 移动端无需加载设备检测
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    this.sdk.setState(Constant.TStateDeviceDetect, false);

    return Promise.all(promiseArray);
  }
}
