import { getBoardInnerRect, getBoardToolLayoutProps } from './util';

import Constant from '@/util/Constant';
import { LayoutBase } from '@/pages/class/layout/layout_base';
import Util from '@/util/Util';
import { getStartedStatus } from '../../../util/class';

const HeaderHeight = 64;
const RightSideWidthDefault = 286; // 三分屏右侧区域宽度
const HorizontalVideoWrapHeight = 110; // 视频列表水平排列时的高度
const VerticalVideoWrapWidth = 180; // 视频列表在垂直排列时的宽度
const DocFooterHeightPC = 60;
const DocFooterHeightPad = 45;
const DocFooterBottom = 10;
const DocThumbnailHeight = 129;

export class LayoutUnitedDesktop extends LayoutBase {
  constructor() {
    super('LayoutUnitedDesktop');
    this.videoShrinkedBeforeScreenShare = false;
    this.instance = null;
    this.isNewLayout = false;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutUnitedDesktop();
    }
    return this.instance;
  }
  getRightSideWidth() {
    return Util.getRightSideWidth();
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, !!this.sdk.isTeacher());
    let initialScale = 1.0;
    if (this.sdk.isPad() && this.sdk.isAndroid()) {
      // 如果android pad的逻辑像素小于1280，通过全局scale撑满1280像素
      if (window.screen.width < 1280) {
        initialScale = window.screen.width / 1280;
        this.sdk.setState(Constant.TStateWebScale, initialScale);
      }
    }
    document.querySelector('meta[name=viewport]').
      setAttribute(
        'content',
        `width=device-width, initial-scale=${initialScale},maximum-scale=1,user-scalable=no,viewport-fit=cover`,
      );
    // if (this.sdk.isCoTeachingClass()) {   // 双师课堂
    // 初始化机顶盒按键模块
    const isAndroidNative = (this.sdk.isAndroid() && this.sdk.isMobileNative());
    if (isAndroidNative && (this.sdk.isTeacher() || this.sdk.isStudent())) {
      window.tbm.init();
      const wid = window.tbm.pushWindow('App');
      window.tbm.pushTarget('header', ['back', 'info', 'menu', 'more', 'button', 'subMenu', 'allCtrl', 'layout'], wid);
      window.tbm.pushTarget('setting', ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'microphoneSelect', 'micVolume',
        'speaker', 'speakerSelect', 'speakerVolumes', 'button', 'input'], wid);
      window.tbm.pushTarget('tips', ['button', 'default'], wid);
      window.tbm.pushTarget('notice', ['title', 'button'], wid);
      window.tbm.pushTarget('memberlist', ['title', 'tab', 'mute', 'students'], wid);
      window.tbm.pushTarget('videowall', ['line0', 'line1', 'line2', 'line3'], wid);
      window.tbm.pushTarget('handup', ['default'], wid);
      window.tbm.pushTarget('pagination', ['default'], wid);
    }
    // }

    // 课堂类型和角色不会变，记下来不用每次都判断
    let layoutFuncName = '';

    if (this.sdk.isBigRoom() && this.sdk.isVideoDocClass()) {
      layoutFuncName = 'updateNewLayout';
      this.isNewLayout = true;
    } else {
      if (this.sdk.isCoTeachingClass()) {   // 双师课堂
        layoutFuncName = 'updateCoteachingLayout';
      } else if (this.sdk.isCollegeClass()) { // 大教学模式
        layoutFuncName = 'updateCollegeLayout';
      } else if (this.sdk.isOneOnOneClass()) {   // 1v1课堂
        layoutFuncName = 'updateOneOnOneLayout';
      } else if (this.sdk.isVideoOnlyClass()) {   // 纯视频课堂
        layoutFuncName = 'updateVideoOnlyLayout';
      } else if (this.sdk.isUnitedRTCClass() || this.sdk.isTeacherOrAssistant()) {
        // RTC课堂
        layoutFuncName = 'updateRtcLayout';
      } else {
        // 标准课堂
        layoutFuncName = 'updateLiveLayout';
      }
    }
    console.log(this.name, 'initLayout', layoutFuncName);
    this.updateLayoutInner = this[layoutFuncName].bind(this);
  }
  updateLayout() {
    if (!this.isNewLayout) {
    if (this.sdk.isElectron()) {
      document.body.style.backgroundColor = null;
    }
    }
    return this.updateLayoutInner();
  }

  updateNewLayout() {
    console.log('updateNewLayout');
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    // electron 共享
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    // 共享屏幕的高级模式
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const promiseArray = [];
    // 更新导航栏布局
    const headerVisible = showHeader
    && !isScreenShareAdvanceModeOnElectron
    && !isRecordMode;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));
    this.sdk.updateComponent('layout-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    });
    return Promise.all(promiseArray);
  }

  /**
   * 更新互动课堂布局
   */
  updateRtcLayout() {
    const promiseArray = [];
    const classInfo = this.sdk.getClassInfo();
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    // electron 共享
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    // 共享屏幕的高级模式
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;

    const classLayout = this.sdk.getClassLayout();
    const isTopLayout = (classLayout === TCIC.TClassLayout.Top);
    const isLeftLayout = (classLayout === TCIC.TClassLayout.Left);
    const isRightLayout = (classLayout === TCIC.TClassLayout.Right);
    const isDoubleLayout = (classLayout === TCIC.TClassLayout.Double);
    const isThreeLayout = (classLayout === TCIC.TClassLayout.Three);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);

    const mainFrame = this.sdk.getClassLayoutMainFrame();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, isScreenShareOnElectron);
    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(isScreenShareAdvanceModeOnElectron);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !isScreenShareAdvanceModeOnElectron
        && !isRecordMode;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));
    // 边栏聊天组件是否显示
    const hasSideIM = mainFrame.sideIM;
    const sideIMVisible = hasSideIM && !deviceDetecting && !isScreenShareOnElectron && !isFullscreen;
    const videoVisible = !deviceDetecting;    // 视频是否显示
    // 主体内容
    let contentTop = 0;
    let contentLeft = 0;
    let contentRight = 0;
    let topRightH = 160;    // 三分屏布局右上角高度
    let introductionLayout = { display: 'none' };
    let videoWrapLayout = {};     // 视频组件布局
    if (isThreeLayout) {    // 三分屏布局
      const studentWrapVisible = videoVisible && classInfo.maxRtcMember !== 0; // 三分屏+1v0时不显示视频栏
      contentRight = isFullscreen ? 0 : this.getRightSideWidth();   // 右侧边栏宽度
      topRightH = this.getTopRightHeight();
      contentTop = studentWrapVisible ? HorizontalVideoWrapHeight : 0;     // 视频栏高度
      introductionLayout = {
        top: `${headerHeight + topRightH}px`,
        left: `calc(100% - ${contentRight}px)`,
        width: `${contentRight}px`,
        height: `calc(100% - ${headerHeight + topRightH}px)`,
        display: sideIMVisible ? 'block' : 'none',
      };
      videoWrapLayout = {
        top: `${headerHeight}px`,
        left: '0px',
        width: `calc(100% - ${contentRight}px)`,
        height: `${contentTop}px`,
        display: studentWrapVisible ? 'block' : 'none',
        style: 'overflow: visible;',
      };
    } else if (isTopLayout || isDoubleLayout) { // 顶部布局或双排布局
      contentTop = isFullscreen ? 0 : HorizontalVideoWrapHeight;     // 视频栏高度
      videoWrapLayout = {
        top: `${headerHeight}px`,
        left: '0px',
        width: '100%',
        height: `${contentTop}px`,
        display: videoVisible ? 'block' : 'none',
        style: 'overflow: visible;',
      };
    } else if (isLeftLayout) { // 左侧布局
      contentLeft = isFullscreen ? 0 : VerticalVideoWrapWidth;
      videoWrapLayout = {
        top: `${headerHeight}px`,
        left: '0px',
        width: `${contentLeft}px`,
        height: `calc(100% - ${headerHeight}px)`,
        display: videoVisible ? 'block' : 'none',
        style: 'overflow: visible;',
      };
    } else if (isRightLayout) { // 右侧布局
      contentRight = isFullscreen ? 0 : VerticalVideoWrapWidth;
      videoWrapLayout = {
        top: `${headerHeight}px`,
        left: `calc(100% - ${contentRight}px)`,
        width: `${contentRight}px`,
        height: `calc(100% - ${headerHeight}px)`,
        display: videoVisible ? 'block' : 'none',
        style: 'overflow: visible;',
      };
    }
    // 边栏
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', introductionLayout));

    // 边栏切换按钮
    const footerHeight = this.sdk.isPad() ? DocFooterHeightPad : DocFooterHeightPC;
    const footerBottom = DocFooterBottom;
    const sideToggleButtonVisible = hasSideIM && !deviceDetecting && !isScreenShareOnElectron;
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: `calc(100% - ${footerHeight + footerBottom}px - 2.5px - 40px)`,
      left: `calc(100% - ${contentRight}px - 40px)`,
      width: '40px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    // 摄像头wrap
    promiseArray.push(this.sdk.updateComponent('videowrap-component', videoWrapLayout)
      .then((success) => {
        if (success) {
          const comp = this.sdk.getComponent('videowrap-component');
          if (comp) {
            comp.getVueInstance()
              .setWrapLayout(isScreenShareOnElectron ? 'screen' : classLayout);
            comp.getVueInstance()
              .setWrapMode('full');   // 屏幕分享结束后需恢复视频模式
            if (isScreenShareOnElectron) {  // 屏幕分享不隐藏视频
              if (isFullscreen) {
                comp.getVueInstance()
                  .toggleShow(true);
                this.videoShrinkedBeforeScreenShare = true;
              }
            } else {
              if (this.videoShrinkedBeforeScreenShare) {
                comp.getVueInstance()
                  .toggleShow(false);
                this.videoShrinkedBeforeScreenShare = false;
              }
            }
          }
        }
      }));

    const boardVisible = !deviceDetecting
      && !isScreenShareOnElectron;
    if (isThreeLayout) {
      // 三分屏布局
      let teacherLayout = {
        top: `${headerHeight}px`,
        left: `calc(100% - ${contentRight}px)`,
        width: `${contentRight}px`,
        height: `${topRightH}px`,
        display: videoVisible ? 'block' : 'none',
        zIndex: 1,
        style: 'overflow: visible;',
      };
      if (isScreenShareOnElectron) {
        teacherLayout = {
          top: '0',
          left: '0',
          width: '160px',
          height: '90px',
          zIndex: 1,
          display: 'block',
          position: 'relative',
        };
      } else  {
        if (isBigVideoMode) {
          const top = headerHeight + contentTop;
          teacherLayout = {
            top: `calc(${top}px + (100vh - ${top}px) / 2  - (100vw - ${contentRight}px) * (9 / 16) / 2)`,
            left: '0',
            width: `calc(100vw - ${contentRight}px)`,
            height: `calc((100vw - ${contentRight}px) * (9 / 16))`,
            zIndex: 1,
            display: boardVisible ? 'block' : 'none',
          };
        }
      }
      // console.log('updateRtcLayout teacher-component', teacherLayout);
      if (!TCIC.SDK.instance.getState(Constant.TStateSkipTeacherUpdateLayout)) {
        promiseArray.push(this.sdk.updateComponent('teacher-component', teacherLayout).then((sucess) => {
          const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
          if (sucess && teacherDom) {
            teacherDom.getVueInstance().setControlDirect('left');
          }
        }));
      }
    }
    promiseArray.push(this.sdk.updateComponent('college-video-switch-component', {
      top: `${headerHeight + topRightH - 44}px`,
      left: `calc(100% - ${44}px)`,
      width: '44px',
      height: '44px',
      display: sideIMVisible ? 'block' : 'none',
      zIndex: 310,
      style: 'overflow: visible;',
    }));

    // 更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
    this.updateDocArea(promiseArray, {
      headerHeight,
      contentTop,
      contentLeft,
      contentRight,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeight,
      autoFitScale,
    });

    return Promise.all(promiseArray);
  }

  /**
   * 更新标准课堂布局
   */
  updateLiveLayout() {
    const promiseArray = [];
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    const isBigRoom = this.sdk.isBigRoom();
    // 更新导航栏布局
    const headerVisible = showHeader
        && !isRecordMode;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));

    if (isBigRoom) {
      // 大班课，拉流组件占满
      promiseArray.push(this.sdk.updateComponent('live-component', {
        top: `${headerHeight}px`,
        left: '0',
        width: '100%',
        height: `calc(100% - ${headerHeight}px)`,
        display: 'block',
      }));
    } else {
      // 小班课逻辑不变
      const rightSideW = RightSideWidthDefault;  // 右侧边栏宽度
      // 拉流组件显示
      promiseArray.push(this.sdk.updateComponent('live-component', {
        top: `${headerHeight}px`,
        left: '0',
        width: isFullscreen ? '100%' : `calc(100% - ${rightSideW}px)`,
        height: `calc(100% - ${headerHeight}px)`,
        display: 'block',
      }));
      // 边栏聊天组件是否显示
      const sideIMVisible = !deviceDetecting && !isFullscreen;
      promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
        top: `${headerHeight}px`,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${rightSideW}px`,
        height: `calc(100% - ${headerHeight}px)`,
        display: sideIMVisible ? 'block' : 'none',
      }));
      // 更新边栏切换按钮布局
      const footerHeight = this.sdk.isPad() ? DocFooterHeightPad : DocFooterHeightPC;
      const footerBottom = DocFooterBottom;
      const sideToggleButtonVisible = !deviceDetecting;
      const sideRight = isFullscreen ? 0 : rightSideW;
      promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
        top: `calc(100% - ${footerHeight + footerBottom}px - 2.5px - 40px)`,
        left: `calc(100% - ${sideRight}px - 40px)`,
        width: '40px',
        height: '40px',
        display: sideToggleButtonVisible ? 'block' : 'none',
      }));
    }
    if (this.sdk.isPad()) {
      promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        display: 'block',
      }));
    }
    return Promise.all(promiseArray);
  }

  /**
   * 更新双师课堂布局
   */
  updateCoteachingLayout() {
    const promiseArray = [];
    const isShowDeviceDetect = this.sdk.getState(Constant.TStateDeviceDetect, false);
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const classStatus = this.sdk.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    const isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2); // 老师在electron下屏幕分享
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;
    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }
    promiseArray.push(this.sdk.updateComponent('ct-device-detect-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: isShowDeviceDetect ? 'block' : 'none',
    }));
    // 更新进房提醒组件
    const reminderVisible = classStatus !== TCIC.TClassStatus.Already_Start;
    promiseArray.push(this.sdk.updateComponent('ct-interact-reminder-component', {
      display: reminderVisible ? 'block' : 'none',
    }));
    const headerHeight = HeaderHeight;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-toast-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: 'auto',
      height: 'auto',
      display: 'block',
    }));
    const gridTop = headerHeight;
    promiseArray.push(this.sdk.updateComponent('ct-video-component', {
      top: `${gridTop}px`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${gridTop}px)`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-component', {
    }));
    // 是否显示举手列表
    const isCoShowHandupList = this.sdk.getState(Constant.TStateCoTeachingShowHandupList, false);
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-list-component', {
      display: isCoShowHandupList ? 'block' : 'none',
    }));
    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const shareToolbarVisible = isScreenShareOnElectron;
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: shareToolbarVisible ? 'block' : 'none',
      }));
    }
    // 更新屏幕分享播放组件位置
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: `${headerHeight}px`,
    }));
    return Promise.all(promiseArray);
  }

  /**
   * 更新1v1课堂布局
   */
  updateOneOnOneLayout() {
    const promiseArray = [];
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;

    const isVideoDocClass = this.sdk.isVideoDocClass();
    const classLayout = this.sdk.getClassLayout();
    const mainFrame = this.sdk.getClassLayoutMainFrame();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, isScreenShareOnElectron);

    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(isScreenShareAdvanceModeOnElectron);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !isScreenShareAdvanceModeOnElectron
        && !isRecordMode;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));

    // 侧边栏
    const hasSideIM = mainFrame.sideIM;
    // 不要判断 deviceDetecting，会导致 OVideoWrapComponent 获取视频组件位置错误，那边修复之后才能改这里
    const sideIMVisible = hasSideIM && !isScreenShareOnElectron && !isFullscreen;
    const oooVideoWrapVisible = !isScreenShareOnElectron;
    let rightSideW = 0;
    if (isVideoDocClass) {
      // 有白板，三分布局
      rightSideW = isFullscreen ? 0 : this.getRightSideWidth();  // 三分屏右侧宽度
      const topRightH = this.getTopRightHeight(); // 1v1，2个摄像头
      // 根据成员数计算布局高度，老师必须占位，所以数量最少为 1
      const memberCount = document.querySelectorAll('student-component, teacher-component').length || 1;
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: `${headerHeight}px`,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${rightSideW}px`,
        height: `${topRightH * (memberCount)}px`,
        display: sideIMVisible ? 'block' : 'none',
      }));
      promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
        top: `${headerHeight + topRightH * (memberCount)}px`,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${rightSideW}px`,
        height: `calc(100% - ${headerHeight + topRightH * (memberCount)}px)`,
        display: sideIMVisible ? 'block' : 'none',
      }));

      promiseArray.push(this.sdk.updateComponent('teacher-component', {
          left: `calc(100% - ${rightSideW}px)`,
          width: `${rightSideW}px`,
          height: `${topRightH}px`,
        }));
        // parseInt(TCIC.SDK.instance.getComponentLayout('teacher-component').top)
      promiseArray.push(this.sdk.updateComponents('student-component', {
          top: `${headerHeight + topRightH}px`,
          left: `calc(100% - ${rightSideW}px)`,
          width: `${rightSideW}px`,
          height: `${topRightH}px`,
        }));
    } else {
      // 没有白板，主体是 oooVideoWrap
      if (hasSideIM) {
        rightSideW = isFullscreen ? 0 : this.getRightSideWidth();
        promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
          top: `${headerHeight}px`,
          left: `calc(100% - ${rightSideW}px)`,
          width: `${rightSideW}px`,
          height: `calc(100% - ${headerHeight}px)`,
          display: sideIMVisible ? 'block' : 'none',
        }));
      }
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: `${headerHeight}px`,
        left: '0',
        width: `calc(100% - ${rightSideW}px)`,
        height: `calc(100% - ${headerHeight}px)`,
        display: oooVideoWrapVisible ? 'block' : 'none',
        zIndex: 1,
      }));
    }

    // 边栏切换按钮
    const footerHeight = this.sdk.isPad() ? DocFooterHeightPad : DocFooterHeightPC;
    const footerBottom = DocFooterBottom;
    const sideToggleButtonVisible = hasSideIM && !deviceDetecting && !isScreenShareOnElectron;
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: `calc(100% - ${footerHeight + footerBottom}px - 2.5px - 40px)`,
      left: `calc(100% - ${rightSideW}px - 40px)`,
      width: '40px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    // 更新文档区，里面会判断是否有白板
    this.updateDocArea(promiseArray, {
      headerHeight,
      contentRight: rightSideW,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeight,
      autoFitScale,
    });

    return Promise.all(promiseArray);
  }

  /* 大教学组件布局 */
  updateCollegeLayout() {
    // 大教学模式
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isElectron = this.sdk.isElectron();
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && isElectron
        && (vodPlay < 2);
    let isScreenShareOnElectron = isElectron
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;

    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    const promiseArray = [];
    if (isElectron) {
      promiseArray.push(this.sdk.updateComponent('loading-component', {
        display: 'none',
      }));
      // 更新屏幕分享工具栏布局
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: 'none',
      }));
      promiseArray.push(this.sdk.updateComponent('teacher-videowrap-component', {}).then((success) => {
        const comp = this.sdk.getComponent('teacher-videowrap-component');
        if (success && comp) {
          comp.getVueInstance().updateLayout();
        }
      }));
      return Promise.all(promiseArray);
    }
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isShowTeacherVideoInWhiteBoardArea = this.sdk.getState(Constant.TStateShowTeacherVideoInWhiteBoardArea, false);
    const videoVisible = !deviceDetecting;
    if (deviceDetecting) {
      return Promise.resolve();
    }

    const headerHeight = 45;  // 标题栏高度
    const rightToolWidth = 56; // 预留右侧工具栏宽度
    const teacherVideoWidth = 264;
    const padding = 8;
    const isShowStudentsWrap = this.sdk.getState(Constant.TStateShowStudentsVideoWrap, true);
    const isShowRightColumn = this.sdk.getState(Constant.TStateShowRightColumn, true);
    const rightWidth = isShowRightColumn ? (rightToolWidth + teacherVideoWidth + padding) : rightToolWidth;     // 右侧边框
    promiseArray.push(this.sdk.updateComponent('teacher-videowrap-component', {}).then((success) => {
      const comp = this.sdk.getComponent('teacher-videowrap-component');
      if (success && comp) {
        comp.getVueInstance().updateLayout();
      }
    }));
    // 更新学生视频组件位置
    const studentsWrapHeight = isShowStudentsWrap ? HorizontalVideoWrapHeight : 0;  // 视频列表高度
    promiseArray.push(this.sdk.updateComponent('students-wrap-component', {
      top: `${headerHeight}px`,
      left: '0px',
      width: `calc(100% - ${rightWidth}px)`,
      height: `${studentsWrapHeight}px`,
      display: videoVisible ? 'block' : 'none',
      style: 'overflow: visible;',
    }));
    if (this.sdk.isTeacher()) {
      return Promise.all(promiseArray);
    }
    // 更新老师视频组件位置
    let teacherLayout;
    let screenPlayerLayout;
    if (isShowTeacherVideoInWhiteBoardArea) {
      const areaWidth = document.body.clientWidth - rightWidth;
      const areaHeight = document.body.clientHeight - headerHeight - studentsWrapHeight;
      if (areaWidth / areaHeight >= 16 / 9) {   // 高度适配
        const videoWidth = Math.floor(areaHeight * 16 / 9);     // 计算视频宽度
        teacherLayout = {
          top: `${headerHeight + studentsWrapHeight}px`,
          left: `${(areaWidth - videoWidth) / 2}px`,
          width: `${videoWidth}px`,
          height: `${areaHeight}px`,
          display: 'block',
          style: 'overflow: visible;',
        };
      } else {  // 宽度适配
        const videoHeight = Math.floor(areaWidth * 9 / 16);
        teacherLayout = {
          top: `${headerHeight + studentsWrapHeight + (areaHeight - videoHeight) / 2}px`,
          left: '0px',
          width: `${areaWidth}px`,
          height: `${videoHeight}px`,
          display: 'block',
          style: 'overflow: visible;',
        };
      }
      screenPlayerLayout = {
        top: `${headerHeight + padding}px`,
        left: `calc(100% - ${rightWidth - padding}px)`,
        width: `${teacherVideoWidth}px`,
        height: `${Math.floor(teacherVideoWidth * 9 / 16)}px`,
      };
    } else {
      teacherLayout = {
        top: `${headerHeight + padding}px`,
        left: `calc(100% - ${rightWidth - padding}px)`,
        width: `${teacherVideoWidth}px`,
        height: `${Math.floor(teacherVideoWidth * 9 / 16)}px`,
        display: isShowRightColumn ? 'block' : 'none',
        style: 'overflow: visible;',
      };
      screenPlayerLayout = {
        top: `${headerHeight + studentsWrapHeight}px`,
        left: '0',
        width: `calc(100% - ${rightWidth}px)`,
        height: `calc(100% - ${headerHeight + studentsWrapHeight}px)`,
      };
    }
    // console.log('updateCollegeLayout teacher-component', teacherLayout);
    promiseArray.push(this.sdk.updateComponent('teacher-component', teacherLayout));

    // 更新屏幕分享播放组件位置
    promiseArray.push(this.sdk.updateComponent('screen-player-component', screenPlayerLayout));

    return Promise.all(promiseArray);
  }

  /**
   * 更新纯视频课堂布局
   */
  updateVideoOnlyLayout() {
    const promiseArray = [];
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;
    const classLayout = this.sdk.getClassLayout();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, isScreenShareOnElectron);

    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(isScreenShareAdvanceModeOnElectron);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !isScreenShareAdvanceModeOnElectron
        && !isRecordMode;
    const headerHeight = headerVisible ? HeaderHeight : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));
    // 边栏聊天组件是否显示
    const hasSideIM = classLayout === TCIC.TClassLayout.VideoIM;
    const sideIMVisible = hasSideIM && !deviceDetecting && !isScreenShareOnElectron && !isFullscreen;
    // 主体内容
    let contentRight = 0;
    let introductionLayout = { display: 'none' };
    if (hasSideIM) {
      contentRight = isFullscreen ? 0 : RightSideWidthDefault;   // 右侧边栏宽度
      introductionLayout = {
        top: `${headerHeight}px`,
        left: `calc(100% - ${contentRight}px)`,
        width: `${contentRight}px`,
        height: `calc(100% - ${headerHeight}px)`,
        display: sideIMVisible ? 'block' : 'none',
      };
    }
    const mainContentLayout = {
      top: `${headerHeight}px`,
      left: '0',
      width: `calc(100% - ${contentRight}px)`,
      height: `calc(100% - ${headerHeight}px)`,
    };

    // 边栏
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', introductionLayout));

    // 边栏切换按钮
    const footerHeight = this.sdk.isPad() ? DocFooterHeightPad : DocFooterHeightPC;
    const footerBottom = DocFooterBottom;
    const sideToggleButtonVisible = hasSideIM && !deviceDetecting && !isScreenShareOnElectron;
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: `calc(100% - ${footerHeight + footerBottom}px - 2.5px - 40px)`,
      left: `calc(100% - ${contentRight}px - 40px)`,
      width: '40px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    // 视频墙
    const videoWallVisible = !deviceDetecting && !isScreenShareOnElectron;
    const videoWallLayout = {
      ...mainContentLayout,
      display: videoWallVisible ? 'block' : 'none',
    };
    // TODO VideoWall里计算位置有点问题，需要以 (0,0) 才正常，后面一起改
    videoWallLayout.top = '0px';
    videoWallLayout.height = '100%';
    videoWallLayout.padding = `${headerHeight}px 0 ${footerHeight + footerBottom}px 0`;
    promiseArray.push(this.sdk.updateComponent('video-wall-component', videoWallLayout));

    // 更新文档区，里面会判断是否有白板
    this.updateDocArea(promiseArray, {
      headerHeight,
      contentRight,
      autoFitScale,
    });

    // 更新各种和布局无关的工具组件、弹窗、tip等
    this.updateClassTools(promiseArray, {
      headerHeight,
      autoFitScale,
    });

    return Promise.all(promiseArray);
  }

  // 1v1/videoOnly/Rtc 共用，更新文档区，包括：白板、缩略图、视频播放器、屏幕分享播放器、各种文档相关工具和显示在文档区的小组件
  updateDocArea(promiseArray, { headerHeight, contentTop = 0, contentLeft = 0, contentRight = 0, autoFitScale }) {
    const isVideoDocClass = this.sdk.isVideoDocClass();
    const isTeacher = this.sdk.isTeacher();
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const isShowBoardToolComponent = this.sdk.getState(Constant.TStateIsShowBoardToolComponent, false);
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
    const isFooterHide = this.sdk.getState(Constant.TStateIsHideFooterComponent, false);
    const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const isChatTips = this.sdk.getState(Constant.TStateChatTipsEnable, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode, false);
    const isClassStarted = getStartedStatus();
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    // electron 共享
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    // 共享屏幕的高级模式
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');

    const classLayout = this.sdk.getClassLayout();
    const isThreeLayout = (classLayout === TCIC.TClassLayout.Three);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);

    // 主体内容区域位置
    const mainContentTop = headerHeight + contentTop;
    let mainContentBottom = 0;
    const mainContentCss = {
      top: `${mainContentTop}px`,
      left: `${contentLeft}px`,
      width: `calc(100% - ${contentLeft}px - ${contentRight}px)`,
      height: `calc(100% - ${mainContentTop}px - ${mainContentBottom}px)`,
    };

    // Electron屏幕分享视频容器位置
    let screenVideoWrapCss;

    if (isVideoDocClass) {
      // 更新缩略图布局，在白板下方
      let thumbnailVisible = !deviceDetecting
        && showThumbnail
        && !isScreenShareOnElectron
        && (isShowBoardToolComponent || !isClassStarted)
        && !showVodPlayer;
      // 如果自定义 UI 关闭了PPT功能，则不显示缩略图
      if (!TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
        thumbnailVisible = false;
      }
      console.log('update thumbnail layout', thumbnailVisible);
      const thumbnailHeight = thumbnailVisible ? DocThumbnailHeight : 0;
      promiseArray.push(this.sdk.updateComponent('thumbnail-component', {
        top: `calc(100% - ${thumbnailHeight}px)`,
        left: `${contentLeft}px`,
        width: `calc(100% - ${contentLeft}px - ${contentRight}px)`,
        height: `${thumbnailHeight}px`,
        display: thumbnailVisible ? 'block' : 'none',
      }));

      // 调整主体内容区域
      mainContentBottom = thumbnailHeight;
      const FooterHeight = isFooterHide ? 0 : 80;
      mainContentCss.height = `calc(100% - ${mainContentTop}px - ${mainContentBottom}px - ${FooterHeight}px)`;

      // 计算白板内部区域位置，给后面的白板工具用
      const boardParentRect = {
        top: mainContentTop,
        left: contentLeft,
        width: document.body.clientWidth - contentLeft - contentRight,
        height: document.body.clientHeight - mainContentTop - thumbnailHeight,
      };
      const boardInnerRect = getBoardInnerRect(boardParentRect);

      // 更新白板布局
      const boardVisible = !deviceDetecting
        && !isScreenShareOnElectron;
      const boardLayout = {
        ...mainContentCss,
        display: boardVisible ? 'block' : 'none',
        zIndex: 1,
        transform: 'scale(1)',
        transformOrigin: 'center',
      };
      if (isThreeLayout && isBigVideoMode) {
        // 三分屏大视频模式，白板和老师视频交换，白板显示在右侧
        const width = boardParentRect.width;
        const height = width * 9.0 / 16.0;
        const scale = contentRight / width;
        boardLayout.top = `${headerHeight}px`;
        boardLayout.left = `${contentRight}px`;
        boardLayout.width = `${width}px`;
        boardLayout.height = `${height}px`;
        // 用scale的方式，解决白板在切换时出现闪烁的问题
        boardLayout.transform = `scale(${scale})`;
        boardLayout.transformOrigin = 'top right';
      }
      promiseArray.push(this.sdk.updateComponent('board-component', boardLayout));

      // 更新白板工具栏布局
      const boardToolVisible = !deviceDetecting
        && !isRecordMode
        && (isTeacher || !showScreenPlayer)
        && isShowBoardToolComponent
        && !(isThreeLayout && isBigVideoMode) // 三分布局把Video切换到主视图，白板在右上角时，不显示白板工具
        && !showVodPlayer;
      const { layoutProps: boardToolLayoutProps, ...boardToolOtherProps } = getBoardToolLayoutProps({
        visible: boardToolVisible,
        isScreenShareOnElectron,
        isScreenShareAdvanceMode,
        boardParentRect,
        boardInnerRect,
      });
      const { boardToolHeight, boardToolTopInCSS, boardToolLeftInCSS } = boardToolOtherProps;
      // console.log('update BoardTool in Rtc', boardToolLayoutProps, boardToolOtherProps);
      promiseArray.push(this.sdk.updateComponent('board-tool-component', boardToolLayoutProps));

      // Electron屏幕分享视频容器
      const screenVideoWidth = 176;
      const screenVideoHeight = boardToolHeight - 12;
      screenVideoWrapCss = {
        top: `calc(${boardToolTopInCSS} + 6px)`,
        left: `calc(${boardToolLeftInCSS} - 10px - ${screenVideoWidth}px)`,
        width: `${screenVideoWidth}px`,
        height: `${screenVideoHeight}px`,
      };
    } else {
      // 没有白板的布局
      promiseArray.push(this.sdk.updateComponent('board-component', { display: 'none' }));

      // Electron屏幕分享视频容器
      const screenVideoWidth = 176;
      const screenVideoHeight = 706;
      screenVideoWrapCss = {
        top: `calc(${headerHeight}px + 6px)`,
        left: `calc(100% - 10px - ${screenVideoWidth}px)`,
        width: `${screenVideoWidth}px`,
        height: `${screenVideoHeight}px`,
      };
    }

    // 更新视频播放器布局
    const vodPlayerVisible = !deviceDetecting
      && !isScreenShareOnElectron
      && showVodPlayer;
    promiseArray.push(this.sdk.updateComponent('vod-player-component', {
      ...mainContentCss,
      display: vodPlayerVisible ? 'block' : 'none',
    }));

    // 更新屏幕分享观看布局
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      ...mainContentCss,
    }));

    // 更新屏幕共享推流布局
    TCIC.SDK.instance.updateComponent('screen-component', {
      ...mainContentCss,
    });

    // 更新PPT工具栏布局，叠在白板区域底部
    const footerVisible = !deviceDetecting
      && !isRecordMode
      && !isScreenShareOnElectron
      && isShowFooterComponent;
    const footerHeight = this.sdk.isPad() ? DocFooterHeightPad : DocFooterHeightPC;
    const footerBottom = DocFooterBottom;
    let footerWidth;
    if (isScreenShareOnElectron) {
      footerWidth = isChatTips ? '500px' : '230px';
    } else {
      footerWidth =  `calc(100% - ${contentLeft}px - ${contentRight}px)`;
    }
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      top: `calc(100% - ${footerHeight + footerBottom + mainContentBottom}px)`,
      left: `${contentLeft}px`,
      width: footerWidth,
      height: `${footerHeight}px`,
      display: footerVisible ? 'block' : 'none',
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    // 助教连麦
    this.sdk.updateComponent('interactive-stage-access-component', {
      width: isScreenShareOnElectron ? 'calc(100% - 176px)' : footerWidth,
    }).then();

    // 滚动消息组件
    const quickMsgBottom = footerHeight + footerHeight + 10;
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      left: '0px',
      top: '0px',
      width: '100%',
      height: `calc(100% - ${quickMsgBottom}px)`,
      display: 'block',
    }));

    // 视频工具组件的宽和白板保持一致
    const vodToolVisible = !deviceDetecting
        && isVodPlayOnElectron
        && showVodPlayer;
    const vodToolScale = autoFitScale;
    const vodToolHeight = 72;
    const vodToolBottomMargin = mainContentBottom + (!!mainContentBottom ? 30 : 24);
    promiseArray.push(this.sdk.updateComponent('vod-tool-component', {
      top: `calc(100% - ${vodToolHeight}px - ${vodToolBottomMargin}px)`,
      left: mainContentCss.left,
      width: mainContentCss.width,
      height: `${vodToolHeight}px`,
      display: vodToolVisible ? 'block' : 'none',
      transform: `scale(${vodToolScale})`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const toolbarWrap = this.sdk.getComponent('share-toolbar-component').getVueInstance();
      if (!toolbarWrap.hasDraged) {
        const shareToolbarVisible = isScreenShareOnElectron;
        promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
          top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
          left: '0',
          width: '100%',
          height: '300px',
          display: shareToolbarVisible ? 'block' : 'none',
        }));
      }

      // 更新屏幕分享视频容器布局
      const screenWrap = this.sdk.getComponent('screen-videowrap-component').getVueInstance();
      if (!screenWrap.hasDraged) {
        const screenVideoWrapVisible = isScreenShareOnElectron;
        promiseArray.push(this.sdk.updateComponent('screen-videowrap-component', {
          ...screenVideoWrapCss,
          display: screenVideoWrapVisible ? 'block' : 'none',
          style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
        })
          .then((success) => {
            if (success) {
              const ele = this.sdk.getComponent('screen-videowrap-component');
              if (ele) {
                if (isScreenShareOnElectron) {
                  ele.getVueInstance().loadVideoWraper();
                } else {
                  ele.getVueInstance().unloadVideoWraper(classLayout);
                }
              }
            }
          }));
        }
    }

    // 更新平板屏幕分享组件
    if (this.sdk.isPad()) {
      if (0 === screenShare) {  // 正在屏幕分享
        promiseArray.push(this.sdk.loadComponent('share-tips-component', {
          top: `${mainContentTop}px`,
          left: '0',
          width: '100%',
          height: '64px',
          display: 'block',
          zIndex: 2,
        }));
      } else {
        promiseArray.push(this.sdk.removeComponent('share-tips-component'));
      }
    }
  }

  // 1v1/videoOnly/Rtc 共用，更新各种和布局无关的工具组件、弹窗、tip等
  // 注意和component里的 getClassTools 对应
  updateClassTools(promiseArray, { headerHeight, autoFitScale }) {
    const classLayout = this.sdk.getClassLayout();

    // 更新奖励动画
    promiseArray.push(this.sdk.updateComponent('trophy-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    }));

    // 更新网络组件位置
    promiseArray.push(this.sdk.updateComponent('network-tips-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: '416px',
      height: 'auto',
      display: 'block',
    }));

    // 更新图片预览组件
    promiseArray.push(this.sdk.updateComponent('image-preview', {
      display: 'none',
    }));

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      transform: `scale(${clockToolScale})`,
    }));

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      transform: `scale(${timerToolScale})`,
    }));

    // 更新答题器组件
    const quizScale = this.getFitScaleBasedOnHeight(classLayout, 660);
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      transform: `scale(${quizScale})`,
    }));

    // 更新随机选人组件
    // promiseArray.push(this.sdk.updateComponent('random-choose-tool-component', {
    //   width: '438px',
    //   left: 'calc(50vw - 219px)',
    //   top: '20vh',
    // }));

    // 更新抢答组件
    // promiseArray.push(this.sdk.updateComponent('seize-answer-component', {
    //   width: '438px',
    //   left: 'calc(50vw - 219px)',
    //   top: '20vh',
    // }));

    if (this.sdk.isPad()) {
      promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        display: 'block',
      }));
    }
  }

  // 各课型共用，进房成功后刷新布局
  updateLayoutAfterJoinClass() {
    const isCollegeClass = this.sdk.isCollegeClass();
    if (isCollegeClass) {
      return this.updateLayoutOfCollegeJoinClass();
    }

    const promiseArray = [];
    const classInfo = this.sdk.getClassInfo();
    const classLayout = this.sdk.getClassLayout();
    const isCoTeachingClass = this.sdk.isCoTeachingClass();
    const isUnitedLiveClass = this.sdk.isUnitedLiveClass();
    const isBigRoom = this.sdk.isBigRoom();
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示

    const videoWrap = this.sdk.getComponent('videowrap-component');
    if (videoWrap) {
      videoWrap.getVueInstance()
        .setWrapMode('full');
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 469 : 424;
    const clockHeight = 390;
    // 未避免学生端定时器和计时器两个组件重合，稍微错开位置
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(40% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 600 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(60% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent(isCoTeachingClass ? 'ct-interact-reminder-component' : 'interact-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 加载设备检测
    // 以下场景无需设备检测
    // - 直播课 classType => 'live' 并且不是老师
    // - 1v0 并且不是老师
    // - 巡课
    // - 录制模式下
    // - RTMP推流模式
    // - 移动端(包括手机和pad)
    // - 自定义js中禁用设备检测功能
    // - 自定义js中将 TStateDeviceDetect 置为false
    const noNeedDeviceDetect = (isUnitedLiveClass && !this.sdk.isTeacherOrAssistant())
      || (classInfo.maxRtcMember === 0 && !this.sdk.isTeacher())
      || this.sdk.isSupervisor()
      || this.sdk.getState(Constant.TStateRecordMode)
      || this.sdk.isRtmpMode()
      || this.sdk.isMobile()
      || !this.sdk.isFeatureAvailable('DeviceDetect')
      || !this.sdk.getState(Constant.TStateDeviceDetect);
    if (noNeedDeviceDetect) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent(isCoTeachingClass ? 'ct-device-detect-component' : 'device-detect-component', {
          width: '100%',
          height: 'calc(100% - 64px)',
          top: '64px',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            // 大屏无需检测设备
            const devices = ['screen-capture', 'browser', 'microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }

    return Promise.all(promiseArray);
  }
  updateLayoutOfCollegeJoinClass() {
    const promiseArray = [];
    if (this.sdk.isElectron()) {
      // 加载设备检测
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('device-detect-component', {
          width: '100%',
          height: '100%',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            const devices = ['microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
      return Promise.all(promiseArray);
    }
    // 加载设备检测
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    if (this.sdk.getState(Constant.TStateRecordMode)) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('device-detect-component', {
          width: '100%',
          height: 'calc(100% - 64px)',
          top: '45px',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            const devices = ['microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }
    return Promise.all(promiseArray);
  }

  getTopRightHeight() {
    // 1v1 2个摄像头
    const videoHeight = Util.getRightVideoHeight();
    return videoHeight;
  }
}
