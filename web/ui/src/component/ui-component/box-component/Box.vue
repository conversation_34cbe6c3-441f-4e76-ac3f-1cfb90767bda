<template>
  <div class="ui-box">
    <div
      v-if="header"
      :class="['ui-box-header', {move: isFloat}]"
    >
      <div class="ui-box-header-title">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
      <div class="ui-box-header-btn-group">
        <el-tooltip
          v-if="floatable"
          placement="bottom"
          :content="isFloat ? floatTip[0] : floatTip[1]"
        >
          <div
            class="ui-box-header-btn ui-box-header-float"
            @click="toggleFloat"
          >
            <i
              :class="['ui-box-icon', isFloat ? 'ui-box-icon-fixed' : 'ui-box-icon-float']"
            />
          </div>
        </el-tooltip>
        <div
          v-if="expandable && !isExpanded"
          class="ui-box-header-btn ui-box-header-expand"
          @click="expand"
        >
          <i class="ui-box-icon ui-box-icon-expand icon" />
        </div>
        <div
          v-if="expandable && isExpanded"
          class="ui-box-header-btn ui-box-header-collapse"
          @click="collapse"
        >
          <button name="button">
            <i class="ui-box-icon ui-box-icon-collapse icon" />
          </button>
        </div>
        <div
          v-if="closable"
          class="ui-box-header-btn ui-box-header-close"
          @click="hide"
        >
          <button name="button">
            <i class="ui-box-icon ui-box-icon-close icon" />
          </button>
        </div>
      </div>
    </div>
    <div
      v-if="header && divider"
      class="divider"
    />
    <div :class="'ui-box-content main-text ' + (header ? '' : 'no-header')">
      <slot name="content" />
    </div>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'Box',
  extends: BaseComponent,
  props: {
    header: {
      type: Boolean,
      default: true,
    },
    divider: {
      type: Boolean,
      default: false,
    },
    expandable: {
      type: Boolean,
      default: false,
    },
    isExpanded: {
      type: Boolean,
      default: false,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    floatable: {
      type: Boolean,
      default: false,
    },
    isFloat: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    floatTip: {
      type: Array,
      default() {
        return ['', ''];
      },
    },
  },
  data() {
    return {
      ready: false,
    };
  },
  mounted() {
  },
  methods: {
    hide() {
      this.$emit('hide');
    },
    expand() {
      this.$emit('expand');
    },
    collapse() {
      this.$emit('collapse');
    },
    toggleFloat() {
      if (this.isFloat) {
        this.$emit('fixed');
      } else {
        this.$emit('float');
      }
    },
  },
};
</script>
<style lang="less" scoped>
@ui-box-background: var(--ui-box-background, #1c2131);

@ui-box-title-height: var(--ui-box-title-height, 60px);
@ui-box-title-height-small-screen: var(--ui-box-title-height-small-screen, 35px);


.ui-box {
  background: @ui-box-background;
  .ui-box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: @ui-box-title-height;
    padding: 0 15px;
    &.move{
      cursor: move;
    }
    &.dialog {
      height: 40px;
      padding: 0;
    }

    .ui-box-icon {
      width: 20px;
      height: 20px;
      display: inline-block;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
    }

    .ui-box-icon-expand {
      background-image: url("../../../assets/images/box/expand.svg");
    }

    .ui-box-icon-collapse {
      background-image: url("../../../assets/images/box/collapse.svg");
    }

    .ui-box-icon-close {
      background-image: url("../../../assets/images/box/box-close.svg");
    }

    .ui-box-icon-float {
      background-image: url("../../../assets/images/box/box-float.svg");
    }

    .ui-box-icon-fixed {
      background-image: url("../../../assets/images/box/box-fixed.svg");
    }

    .ui-box-header-title {
      font-size: 120%;
      font-weight: 400;
      color: #8A9099;
      line-height: 20px;
    }

    .ui-box-header-btn {
      width: 24px;
      height: 24px;
      border-radius: 3px;
      background-color: #030910;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &.mini {
        width: 20px;
        height: 20px;
      }


      i {
        display: flex;
        font-size: 20px;
        color:#fff;
      }

      &:hover {
        opacity: 1;

        i {
          opacity: 1;
        }
      }

      &:active {
        background-color: #2E68E1;
        opacity: 1;
        color:#fff;
      }
    }

    .ui-box-header-btn-group {
      display: flex;
    }

    .ui-box-header-btn + .ui-box-header-btn {
      margin-left: 8px;
    }
  }

  .divider {
    height: 2px;
    width: 100%;
    background-color: rgba(184, 184, 184, 0.1);
  }

  .ui-box-content {
    width: 100%;
    height: calc(100% - @ui-box-title-height);
    transition: all .5s;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    background: #1c2131!important;
    border-radius: 4px;
    overflow: scroll;

    &.no-header {
      height: 100%;
    }
  }
}

.small-screen {
  .ui-box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: @ui-box-title-height-small-screen;
    padding: 0 16px;

    .ui-box-header-title {
      width: 100%;
      font-size: 100%;
    }

    .header-title {
      font-size: 14px;
    }
  }

  .ui-box-content {
    height: calc(100% - @ui-box-title-height-small-screen);

    &.no-header {
      height: 100%;
    }
  }
}
</style>
