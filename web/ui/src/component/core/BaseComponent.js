import '@assets/css/reset.css';
import '@assets/theme/dark.less';
// TODO: 只有 record模式才加载
import '@assets/theme/record-mode.less';
import '@assets/theme/college-electron.less';
import '@assets/theme/college-web.less';
import 'document-register-element/build/document-register-element';

import Constant from '@/util/Constant';
import ElementUI from 'element-ui';
import HttpApi from 'i18next-http-backend';
import I18NextVue from '@panter/vue-i18next';
import Role from '@/util/Role';
import Toasted from 'vue-toasted';
import TvButtonManager from './TvButtonManager.js';
import Vue from 'vue';
import Vue2TouchEvents from 'vue2-touch-events';
import VueClipboard from 'vue-clipboard2';
import VueDOMPurifyHTML from 'vue-dompurify-html';
import i18next from 'i18next';
import vueCustomElement from 'vue-custom-element';

export const $EventBus = new Vue();
Vue.prototype.$EventBus = $EventBus;
Vue.prototype.$GlobalData = new Vue();
Vue.use(I18NextVue);
Vue.use(VueClipboard);
Vue.use(Vue2TouchEvents);
Vue.use(vueCustomElement);
Vue.use(ElementUI);
Vue.use(Toasted, {
  theme: 'toasted-primary',
  duration: 3000,
  position: TCIC.SDK.instance.isMobile() ? 'top-center' : 'middle-center',
  className: 'stage-toast',
});
Vue.use(VueDOMPurifyHTML);
Vue.directive('role', Role.detect);
Vue.directive('auth', Role.auth);
Vue.prototype.$hasRole = Role.has;
const currentLocation = location.href.substring(0, location.href.indexOf('.html'));
const basePath = currentLocation.substring(0, currentLocation.lastIndexOf('/'));
// eslint-disable-next-line no-undef
const staticTcicVerionPath = `${basePath}/static/tcic/${VERSION_CONFIG.mainVersion}`;

const currentLng = TCIC.SDK.instance.getLanguage();
console.log('初始化ui时语言', currentLng);

const i18nLoadPath = `${staticTcicVerionPath}/locales/ui/{{lng}}.json?t=202405131101`;
window.i18nextPromise = () => i18next.use(HttpApi).init({
  lng: currentLng,
  fallbackLng: /zh(-\w+)?/g.test(currentLng) ? 'zh' : 'en',
  load: /zh(-\w+)?/g.test(currentLng) ? 'currentOnly' : 'languageOnly',
  keySeparator: false,
  nsSeparator: false,
  backend: {
    loadPath: i18nLoadPath,
  },
});
window.Vue = Vue;
window.tbm = new TvButtonManager(); // 支持 TV 或盒子的 遥控器
// 注册toast
window.showToast = (message, type = 'show', options = {}) => {
  TCIC.SDK.instance.reportLog('showToast', `${message},${type}`);
  if (TCIC.SDK.instance.getState(Constant.TStateHookToastMessage)) {
    TCIC.SDK.instance.notify(Constant.TEventToastMessage, {
      message,
      type,
    });
  } else {
    Vue.toasted.show(message, options);
  }

  // 语言检查
  const language = TCIC.SDK.instance.getLanguage();
  if (!/zh(-\w+)?/g.test(language) && /[\u4e00-\u9fa5]+/g.test(message)) {
    // 不是中文，如果内容中出现了中文就上报
    TCIC.SDK.instance.reportLog('messageLanguageError', `showToast: ${message}`);
  }
};

// i18next暴露给客户
window.i18next = i18next;
window.$tt = i18next.t;
Vue.prototype.$t = i18next.t;

// 注册双师通用toast
window.coTeachingShowToast = (message, localIconIndex = 0, remoteIconUrl = null) => {
  const toastDom = TCIC.SDK.instance.getComponent('ct-toast-component');
  toastDom.getVueInstance().toastShow(message, localIconIndex, remoteIconUrl);
};

// 全局方法，web端提供截图功能，由tcic来调用
window.saveSnapshot = (snapId, base64Data, snapName) => {
  const downloadEl = document.createElement('a');
  const event = new MouseEvent('click');
  downloadEl.download = snapName;
  downloadEl.href = base64Data;
  downloadEl.dispatchEvent(event);
  return Promise.resolve({
    snapId,
    module: 'web',
    snapName,
    totalSlices: 1,
    totalSize: base64Data.length,
    sliceIndex: 0,
    sliceSize: base64Data.length,
  });
};

const i18n = new I18NextVue(i18next);
// 设置语言
export default {
  i18n,

  data() {
    return {
      isVisible: false,           // 当前组件是否可见
      isMobile: false,            // 当前是否在移动设备上使用，包括手机和Pad
      isSmallScreen: false,       // 当前是否在小屏幕上使用（小屏幕当前特指手机端，不含Pad和桌面）
      isTCICComponent: false,     // 当前组件是否TCIC组件
      theTCICComponentName: '',   // 当前组件的TCIC组件名称
      theTCICComponentLabel: '',  // 当前组件的TCIC组件标识
      theTCICComponent: null,     // 如果当前组件由TCIC的loadComponent方法创建，则该字端记录TCIC组件DOM
      theTCICComponentEventListener: null,
      pointerEventsFixed: false,  // 记录是否针对pointer-events: none样式做了保护
      boundingClientRect: null,   // 记录组件的矩形区域
      tcicEventListenerList: [],  // 记录所有监听的TCIC事件，用于组件销毁前取消事件监听
      tcicStateSubscribeList: [],  // 记录所有监听的TCIC状态，用于组件销毁前取消状态监听
      isZh: false,
      isWideText: true,
      isDestroyed: false,
      nameConfig: {
        roomInfo: {},
        roleInfo: {},
      },
    };
  },
  created() {
    this.isZh = /zh(-\w+)?/g.test(TCIC.SDK.instance.getLanguage());
    this.isWideText = !this.isZh;
    this.nameConfig = TCIC.SDK.instance.getNameConfig();
  },
  mounted() {
    this.boundingClientRect = {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    };
    // 检测是否在小屏幕上使用
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isSmallScreen = this.isMobile && !TCIC.SDK.instance.isPad();
    // 检查当前组件是否由TCIC的loadComponent方法创建，并获取组件名称和标识
    if (this.$options.name && this.$el) {
      if (this.$el.parentElement && this.$el.parentElement.tagName === this.$options.name.toUpperCase()) {
        const label = this.$el.parentElement.getAttribute('label');
        const fullName = `${this.$options.name}-${label}`;
        if (this.$el.parentElement.id === fullName && this.$el.parentElement.className.includes(fullName)) {
          this.theTCICComponentName = this.$options.name;
          this.theTCICComponentLabel = label;
          this.theTCICComponent = this.$el.parentElement;
          this.isTCICComponent = true;
        }
      }
    }
    // 对于TCIC加载的组件
    if (this.isTCICComponent) {
      // 监听组件事件
      this.theTCICComponentEventListener = (eventName, params) => {
        if (eventName === 'drag_start') {
          this.onComponentDragStart();
        } else if (eventName === 'drag_end') {
          this.onComponentDragEnd();
        } else if (eventName === 'visible_changed') {
          this.notifyVisibilityChange(params[0]);
          TCIC.SDK.instance.notify(Constant.TEventComponentUpdateLayout, this, false);
        } else if (eventName === 'rect_changed') {
          this.notifyBoundingClientRectChange(params[0]);
          TCIC.SDK.instance.notify(Constant.TEventComponentUpdateLayout, this, false);
        }
      };
      TCIC.SDK.instance.addComponentEventListener(
        this.theTCICComponentName,
        this.theTCICComponentEventListener,
        this.theTCICComponentLabel,
      );
    }

    // 检测交互事件类型
    if (!window.checkInteractiveEventType) {
      window.checkInteractiveEventType = function () {
        let touchTarget = null;
        window.addEventListener('touchstart', (event) => {
          touchTarget = event.target;
          window.interactiveEventTypeIsTouch = true;
        }, true);
        window.addEventListener('touchend', () => {
          setTimeout(() => {
            touchTarget = null;
          }, 0);
        }, true);
        if (!TCIC.SDK.instance.isMobile()) {    // 移动端不监听鼠标事件(后续若有特殊设备需要监听再额外判断)
          window.addEventListener('mousedown', (event) => {
            window.interactiveEventTypeIsTouch = touchTarget === event.target;
          }, true);
          window.addEventListener('mouseup', () => {
            touchTarget = null;
          }, true);
        }
      };
      window.interactiveEventTypeIsTouch = false;
      window.checkInteractiveEventType.apply(this);
    }
  },

  beforeDestroy() {
    this.isDestroyed = true;
    // 取消所有监听事件
    this.tcicEventListenerList.forEach((item) => {
      TCIC.SDK.instance.off(item.event, item.listener, item.options);
    });
    // 取消所有状态订阅
    this.tcicStateSubscribeList.forEach((stateSubscribe) => {
      TCIC.SDK.instance.unsubscribeState(stateSubscribe.name, stateSubscribe.listener, stateSubscribe.options);
    });
    // 对于TCIC加载的组件
    if (this.isTCICComponent) {
      // 移除组件事件监听
      TCIC.SDK.instance.removeComponentEventListener(
        this.theTCICComponentName,
        this.theTCICComponentEventListener,
        this.theTCICComponentLabel,
      );
    }
  },

  methods: {
    interactiveIsTouchEvent() {
      return window.interactiveEventTypeIsTouch;
    },

    /**
     * 组件可视状态发生变化时调用该方法以触发该组件及其所有子组件的onComponentVisibilityChange通知
     * @param {boolean} visible 新的可视状态
     * @return {boolean} 可视状态是否发生改变
     */
    notifyVisibilityChange(visible) {
      // 当可视状态发生改变时，触发事件
      if (typeof visible !== 'boolean') {
        if (this.isTCICComponent) {
          visible = TCIC.SDK.instance.isComponentVisible(this.theTCICComponentName, this.theTCICComponentLabel);
        } else {
          visible = (this.$el.offsetParent !== null);
        }
      }
      if (this.isVisible !== visible) {
        // 递归更新子组件
        this.$children.forEach((childVue) => {
          childVue.notifyVisibilityChange && childVue.notifyVisibilityChange(visible);
        });
        // 更新当前组件
        this.isVisible = visible;
        // eslint-disable-next-line
        // console.log('onComponentVisibilityChange', `${this.$options.name}|${this._uid}`, visible);
        // 异步回调
        setTimeout(() => {
          this.onComponentVisibilityChange(visible);
        });
        return true;
      }
      return false;
    },

    /**
     * 组件展示区域发生改变时调用该方法以触发该组件及其所有子组件的onBoundingClientRectChange通知
     * @param {DOMRect} rect 新的展示区域
     * @return {boolean} 展示区域是否发生改变
     */
    notifyBoundingClientRectChange(rect) {
      if (!rect) {
        if (this.theTCICComponent) {
          rect = TCIC.SDK.instance.getComponentClientRect(this.theTCICComponentName, this.theTCICComponentLabel);
        } else {
          if (!this.$el || !this.$el.getBoundingClientRect || typeof this.$el.getBoundingClientRect !== 'function') {
            console.warn('notifyBoundingClientRectChange without $el or $el.getBoundingClientRect');
            return false;
          }
          rect = this.$el.getBoundingClientRect();
        }
      }
      if (this.boundingClientRect === null
        || this.boundingClientRect.x !== rect.x
        || this.boundingClientRect.y !== rect.y
        || this.boundingClientRect.width !== rect.width
        || this.boundingClientRect.height !== rect.height) {
        // 递归更新子组件
        this.$children.forEach((childVue) => {
          childVue.notifyBoundingClientRectChange && childVue.notifyBoundingClientRectChange();
        });
        // 更新当前组件
        this.boundingClientRect = rect;
        // eslint-disable-next-line
        // console.log('onBoundingClientRectChange', `${this.$options.name}|${this._uid}`, rect);
        // 异步回调
        setTimeout(() => {
          this.onBoundingClientRectChange(rect);
        });
        return true;
      }
      return false;
    },

    /**
     * 在确定已加入课堂后触发回调
     * @param callback 在确定已进入房间后触发该回调
     */
    makeSureClassJoined(callback) {
      if (callback) {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then((...args) => {
          if (!this.isDestroyed) {
            callback.apply(this, args);
          }
        });
      }
    },

    /**
     * 组件可视状态变化通知
     * @param visible 新的可视状态
     */
    onComponentVisibilityChange(visible) {

    },

    /**
     * 组件展示区域发生改变通知
     * @param rect 新的展示区域
     */
    onBoundingClientRectChange(rect) {

    },

    /**
     * 组件开始拖动通知
     */
    onComponentDragStart() {

    },

    /**
     * 组件停止拖动通知
     */
    onComponentDragEnd() {

    },

    /**
     * 更新组件封装层DOM节点
     * @param layout
     * @return {Promise<boolean>}
     */
    updateComponent(layout) {
      if (!this.isTCICComponent) {
        return Promise.resolve(false);
      }
      return TCIC.SDK.instance
        .updateComponent(this.theTCICComponentName, layout, this.theTCICComponentLabel);
    },

    /**
     * 移动组件实例
     * @return {Promise<boolean>}
     */
    removeComponent() {
      if (!this.isTCICComponent) {
        return Promise.resolve(false);
      }
      return TCIC.SDK.instance
        .removeComponent(this.theTCICComponentName, this.theTCICComponentLabel);
    },

    /**
     * 显示组件
     */
    show() {
      // 通知父组件该组件即将被隐藏（主要供Header里使用）
      if (!this.isTCICComponent) {
        this.$emit('show');
      } else {
        this.updateComponent({
          display: 'block',
        }).then();
      }
    },

    /**
     * 隐藏组件
     */
    hide() {
      // 通知父组件该组件即将被隐藏（主要供Header里使用）
      if (!this.isTCICComponent) {
        this.$emit('hide');
      } else {
        this.updateComponent({
          display: 'none',
        }).then();
      }
    },

    /**
     * 关闭组件
     */
    close() {
      // 通知父组件该组件即将被隐藏（主要供Header里使用）
      if (!this.isTCICComponent) {
        this.$emit('close');
      } else {
        this.removeComponent().then();
      }
    },

    /**
     * 添加TCIC生命周期事件监听，通过该方法添加的事件监听会在组件销毁前自动取消监听
     * @param event 事件名称
     * @param listener 处理函数
     * @param options 监听选项
     */
    addLifecycleTCICEventListener(event, listener, options) {
      if (!TCIC.SDK.instance.on(event, listener, options)) {
        return false;
      }
      this.tcicEventListenerList.push({
        event,
        listener,
        options,
      });
      return true;
    },

    /**
     * 添加TCIC生命周期状态监听，通过该方法添加的状态监听会在组件销毁前自动取消监听
     * @param name 状态名称
     * @param listener 处理函数
     * @param options 监听选项
     */
    addLifecycleTCICStateListener(name, listener, options) {
      if (!TCIC.SDK.instance.subscribeState(name, listener, options)) {
        return false;
      }
      this.tcicStateSubscribeList.push({
        name,
        listener,
        options,
      });
      return true;
    },

    /**
     * 在弹窗中展示组件
     * @param {string} name  组件名称
     * @param {*} params 参数
     */
    showSubComponent(name, params) {
      const url = `${document.location.protocol}//${document.location.host}${document.location.pathname.replace('class.html', '')}webview.html`;
      TCIC.SDK.instance.showSubWindow(url, name, params, null);
    },

    /**
     * 启用或关闭组件DOM节点的拖动功能
     * @param enabled     是否启用拖动
     * @param domSelector 要启用拖动功能的DOM节点选择器
     * @param draggableRect 可拖动区域，可传入矩形或函数，若传入函数类型，则每次用户拖动会调用该方法获取新的可拖动区域限制
     */
    toggleComponentDrag(enabled, domSelector = '.drag-module-header__wrap', draggableRect = null, forceUpdate = true) {
      if (!this.isTCICComponent) {
        return;
      }
      TCIC.SDK.instance.toggleComponentDraggable(
        this.theTCICComponentName,
        this.theTCICComponentLabel,
        enabled,
        domSelector,
        draggableRect,
        forceUpdate,
      );
      return () => {
        TCIC.SDK.instance.updateComponentClientRect(
          this.theTCICComponentName,
          this.theTCICComponentLabel,
        );
      };
    },

    getOwnerTCICComponent() {
      let node = this;
      while (node && node !== window) {
        if (node.isTCICComponent) {
          return node;
        }
        node = node.$parent;
      }
    },
  },
};
