<template>
  <div
    ref="container"
    class="o-video__container"
  />
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import VideoWall from '@/util/VideoWall.js';
import VirtualElement from '@util/VirtualElement';
import Drag from '@util/Drag';
import Constant from '@/util/Constant';
import Lodash from 'lodash';
import Util from '@util/Util';

export default {
  components: {
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      videoWidth: 286, // 视频宽度
      videoHeight: 160, // 视频高度
      studentId: '',    // 1v1的学生id
      bufferStudentId: '',    // 由于1v1开放学生加入(可能存在多个上台学生)
      classLayout: TCIC.TClassLayout.Three,
      isVideoOnlyLayout: false,
      hasRightSide: true,
      componentRect: null,
      boardTarget: new VirtualElement(0, 0, 0, 0),
      dragMap: new Map(),
      positionMap: new Map(),
      sessionId: '',
      videoWall: null,
      isVideoWallMode: false,
      isTeacher: false,       // 是否是老师
      isPhone: false,       // 是否是手机端
      isFullScreen: false,  // 是否白板全屏
      roleInfo: {},
      roomInfo: {},
      isPortrait: TCIC.SDK.instance.isPortrait(),
      isMobile: false,
      isScreenLayout: false,
      needUpdateAfterResize: false,
      rightSideWidth: 286,
      showStudent: false,
    };
  },
  computed: {
    isMobilePortraitBigRoom() {
      this.videoWall?.updateRect(this.getVideoWallRect());
      if (this.isMobile && this.isPortrait && TCIC.SDK.instance.isBigRoom()) {
        return true;
      }
      return false;
    },
    teacherVideoWidth() {
      const rect = this.componentRect || this.$el.getBoundingClientRect();
      if (this.isMobilePortraitBigRoom) {
        if (this.showStudent) {
          return Math.min(this.videoWidth, rect.width / 2);
        }
        return this.videoWidth;
      }
        const rsWidth = this.getRightSideWidth();
        if (rsWidth > this.videoWidth) {
          return rsWidth;
        }
        return this.videoWidth;
    },
    teacherVideoHeight() {
      const rsWidth = this.getRightSideWidth();
      if (rsWidth > this.videoWidth) {
        return rsWidth / this.videoWidth * this.videoHeight;
      }
      return this.videoHeight;
    },
    studentVideoHeight() {
      const rsWidth = this.getRightSideWidth();
      if (rsWidth > this.videoWidth) {
        return rsWidth / this.videoWidth * this.videoHeight;
      }
      return this.videoHeight;
    },
    studentVideoWidth() {
      const rect = this.componentRect || this.$el.getBoundingClientRect();
      if (this.isMobilePortraitBigRoom) {
        if (this.showStudent) {
          return Math.min(this.videoWidth, rect.width / 2);
        }
        return this.videoWidth;
      }
      const rsWidth = this.getRightSideWidth();
      if (rsWidth > this.videoWidth) {
        return rsWidth;
      }
      return this.videoWidth;
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    // 监听窗口尺寸变更
    const resizeObserver = new ResizeObserver(() => {
      this.onResize();
    });
    resizeObserver.observe(this.$el);
    this.cleanupResizeObserver = () => {
      resizeObserver.disconnect();
    };

    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });

    // 初始化视频墙
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Info_Ready, true)
      .then(() => {
        this.isPhone = TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad();
        // 1v1课不支持动态切换布局，故获取课堂信息后取一次即可
        this.classLayout = TCIC.SDK.instance.getClassLayout();
        this.isVideoOnlyLayout = !TCIC.SDK.instance.isClassLayoutHasDoc();
        if (this.isPhone) {
          this.hasRightSide = !this.isVideoOnlyLayout;
        } else {
          this.hasRightSide = TCIC.SDK.instance.getClassLayoutMainFrame().sideIM;
        }
        if (this.isVideoOnlyLayout
          || (TCIC.SDK.instance.isTeacherOrAssistant() && !this.isPhone)
        ) {
          // 视频布局或非手机的老师端才开启
          console.log(`OVideoWrapComponent::create VideoWall, classLayout ${this.classLayout}`);
          this.videoWall = new VideoWall(this.getVideoWallRect());
          this.videoWall.setLayoutCallback((element, left, top, width, height, zIndex) => {
            if (element) {
              TCIC.SDK.instance.updateComponent(element.tagName.toLowerCase(), {
                position: 'absolute',
                left: `${left}px`,
                top: `${top}px`,
                width: `${width}px`,
                height: `${height}px`,
                zIndex,
                display: 'block',
                style: 'overflow: visible;',
              }, element.getAttribute('label'), false).then(() => this.updateDragTask());
            }
          });
          this.isVideoWallMode = this.isVideoOnlyLayout;
        } else {
          this.getVideoWallRect();
        }
        if (this.isVideoOnlyLayout) {   // 纯视频布局把辅助摄像头加入视频墙
          // 监听辅助摄像头
          this.addLifecycleTCICStateListener(Constant.TStateShowSubCameraComponent, this.onSubCameraVisible);
        }
      });
    // 进房事件
    this.makeSureClassJoined(this.onJoinedClass);
    // 音视频添加事件
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Add, (info) => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo.teacherId === info.userId) {   // 添加老师的组件
        if (this.classLayout === TCIC.TClassLayout.Three) return;     // 三分布已添加避免重复添加
        if (TCIC.SDK.instance.isTeacher()) {    // 老师或三分屏进课堂就加载老师组件
          this.makeSureClassJoined(() => this.loadTeacherVideoCom());
        } else {
          TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
            .then(() => {
              this.makeSureClassJoined(() => this.loadTeacherVideoCom());
            });
        }
      } else if (this.studentId === '') {  // 添加学生的组件
        this.safeLoadStudentVideoCom(info.userId);
      } else {    // 缓存用户
        this.bufferStudentId = info.userId;
      }
      if (info.userId === TCIC.SDK.instance.getUserId()) {
        // eslint-disable-next-line max-len
        const loadBeforeClassStart = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Not_Start;
        // 设备检测完成，且开始上课后才显示该提示
        TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false)
          .then(() => TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start))
          .then(() => {
            // 学生才提示
            if (!TCIC.SDK.instance.isStudent()) {
              return;
            }
            const audioEnable = info.mic;
            const videoEnable = info.camera;
            const stageEnable = info.stage;
            if (audioEnable && videoEnable) {
              if (loadBeforeClassStart && TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin)) {
                const audioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
                const videoCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Capture);
                if (audioCapture && videoCapture) {
                  window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~', { arg_0: this.roleInfo.teacher }));
                } else if (audioCapture) {
                  window.showToast(i18next.t('你已被{{arg_0}}邀请上台，视频已关闭~', { arg_0: this.roleInfo.teacher }));
                } else if (videoCapture) {
                  window.showToast(i18next.t('你已被{{arg_0}}邀请上台，麦克风已关闭~', { arg_0: this.roleInfo.teacher }));
                } else {
                  window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频已关闭~', { arg_0: this.roleInfo.teacher }));
                }
              } else {
                window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~', { arg_0: this.roleInfo.teacher }));
              }
            } else if (audioEnable) {
              window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的视频已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
            } else if (videoEnable) {
              window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的麦克风已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
            } else if (stageEnable) {
              window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的音视频已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
            }
          });
      }
    });
    // 音视频删除事件
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Remove, (info, reason) => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (info.userId === TCIC.SDK.instance.getUserId()) {
        window.showToast(i18next.t('你已下台，暂时无法参与音视频互动~'));
      }
      if (classInfo.teacherId === info.userId) {
        if (this.classLayout === TCIC.TClassLayout.Three) {
          return;     // 三分布不移除老师组件
        }
        const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (teacherDom) {
          this.dragMap.delete(teacherDom.id);
          TCIC.SDK.instance.removeComponent('teacher-component');
        }
        this.videoWall && this.videoWall.removeVideo(classInfo.teacherId);
      } else {
        const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
        if (studentDom) {
          this.dragMap.delete(studentDom.id);
        }
        TCIC.SDK.instance.removeComponent('student-component', info.userId);
        this.showStudent = false;
        this.updateContainerSize();
        if (this.isMobilePortraitBigRoom) {
          this.updateDragDomPosition('teacher-component-default');
        }
        if (this.studentId === info.userId) {
          this.videoWall && this.videoWall.removeVideo(info.userId);
          if (this.bufferStudentId === '') {
            this.studentId = '';
          } else {    // 加载缓存用户
            this.safeLoadStudentVideoCom(this.bufferStudentId);
            this.bufferStudentId = '';  // 加载后重置
          }
        } else if (this.bufferStudentId === info.userId) {
          this.bufferStudentId = '';
        } else return;
      }
      this.updateDragTask();
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    // 更新任务信息
    TCIC.SDK.instance.getTasks(0).then((tasks) => {
      if (tasks && tasks.tasks && tasks.tasks.length > 0) {
        tasks.tasks.forEach(task => this.onTaskUpdate(task));
      }
    });
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, (flag) => {
      this.isFullScreen = flag;
      console.log(`OVideoWrapComponent::onFullScreenChange ${flag}, isVideoWallMode ${this.isVideoWallMode}, componentRect`, this.componentRect);
      if (this.isVideoWallMode) {
        // 视频墙模式，resize里会更新videoWallRect
        return;
      }
      this.updateAllPosition();
      if (!this.isVideoOnlyLayout && !this.isFullScreen) {
        // 三分屏显示右侧栏，这时 componentRect 尺寸还是0，resize后再update
        console.log('OVideoWrapComponent, set needUpdateAfterResize');
        this.needUpdateAfterResize = true;
      }
    });
    // 监听屏幕分享状态
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (status) => {
      if (TCIC.SDK.instance.isTeacher()) {
        const isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
        if (status < 2 && !isSubCameraStarted && TCIC.SDK.instance.isElectron()) {  // 屏幕共享或播放视频课件
          this.quickVideoWall(false);
        }
      }
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.onPermissionUpdate);
    // 监听窗口尺寸变更
    window.addEventListener('resize', this.onWindowResize);
  },
  beforeDestroy() {
    this.cleanupResizeObserver?.();
    window.removeEventListener('resize', this.onWindowResize);
  },
  methods: {
    onPermissionUpdate(permissionList) {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        const teacherPermission = permissionList.find(({ userId }) => (userId === classInfo.teacherId || classInfo.assistants.includes(userId)));
        const isTeacherHere = !!teacherPermission;
        if (!isTeacherHere) {
          if (!this.isVideoOnlyLayout) {
            this.isVideoWallMode = false;
          }
          TCIC.SDK.instance.setState(Constant.TStateVideoWallMode, false);
          this.positionMap.clear();
          this.updateAllPosition();
        }
      });
    },
    setSize(weight, height) {
      this.videoHeight = height;
      this.videoWidth = weight;
    },
    onJoinedClass() {
      // 随机生成会话id
      this.sessionId = `${TCIC.SDK.instance.getUserId()}_${Math.floor(Math.random() * 65535)}`;
      this.componentRect = this.$el.getBoundingClientRect();
      console.log(`OVideoWrapComponent::onJoinedClass, isVideoWallMode ${this.isVideoWallMode}, componentRect`, this.componentRect);
      this.updateAllPosition();
      if (this.classLayout === TCIC.TClassLayout.Three) {   // 三分布局始终加载老师
        this.loadTeacherVideoCom();
      }
    },
    onResize() {
      this.$nextTick(() => {
        this.componentRect = this.$el.getBoundingClientRect();
        const rect = this.getVideoWallRect();
        // console.log(`OVideoWrapComponent::onResize, hasVideoWall ${!!this.videoWall}, isVideoWallMode ${this.isVideoWallMode}`);
        // console.log('OVideoWrapComponent, componentRect', this.componentRect);
        // console.log('OVideoWrapComponent, videoWallRect', rect);
        if (this.needUpdateAfterResize) {
          this.needUpdateAfterResize = false;
          console.log('OVideoWrapComponent, udpateAllPosition after resize');
          this.updateAllPosition();
          return;
        }
        if (this.videoWall) {
          this.videoWall.updateRect(rect);
        } else {
          this.updateAllPosition();
        }
      });
    },
    onVideoDbClick(ele) {
      const isTeacher = this.isTeacherDom(ele);
      const uid = this.getDomUserId(ele);
      if (this.videoWall && this.videoWall.checkVideo(uid)) {
        this.resetDomPosition(ele, true);
      } else {
        if (isTeacher) {
          this.videoWall && this.videoWall.unshiftVideo(uid, ele);
        } else {
          this.videoWall && this.videoWall.pushVideo(uid, ele);
        }
        this.prepareResize(ele);
        this.updateVideoWallState(true);
      }
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
      setTimeout(() => this.updateDragTask(), 300);
    },
    // 任务信息更新
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId === 'task-drag-array') {  // 新版位置同步任务协议(1.6.2开始支持)
        try {
          const jsonData = JSON.parse(taskInfo.content);
          this.processTaskUpdate(jsonData);
        } catch (err) {
          console.warn(`OVideoWrapComponent::onTaskUpdate=>parse task ${taskInfo.taskId} fail: ${err.toString()}`);
        }
      } else if (taskInfo.taskId === Constant.TConstantVideoWallTaskId) {
        if (!this.isVideoOnlyLayout) {
          this.isVideoWallMode = !!taskInfo.status;
          console.log(`OVideoWrapComponent::onTaskUpdate=>isVideoWallMode ${this.isVideoWallMode}`);
        }
        TCIC.SDK.instance.setState(Constant.TStateVideoWallMode, !!taskInfo.status);
      }
    },
    onSubCameraVisible(visible) {
      if (visible) {
        this.videoWall.pushVideo('sub-camera', TCIC.SDK.instance.getComponent('sub-camera-component'), true, VideoWall.VideoTypeSub);
      } else {
        this.videoWall.removeVideo('sub-camera', true, VideoWall.VideoTypeSub);
      }
    },
    quickVideoWall(flag) {
      if (flag) {
        this.dragMap.forEach((drag) => {
          const dom = drag.getDragElement();
          if (this.isTeacherDom(dom)) {
            this.videoWall && this.videoWall.unshiftVideo(TCIC.SDK.instance.getClassInfo().teacherId, dom, false);
          } else {
            this.videoWall && this.videoWall.pushVideo(dom.getAttribute('label'), dom, false);
          }
          this.prepareResize(dom);
        });
        this.updateVideoWallState(true);
        this.videoWall && this.videoWall.updateRender();    // 主动刷新
      } else {
        this.dragMap.forEach((drag) => {
          const dom = drag.getDragElement();
          this.resetDomPosition(dom);
        });
        this.updateDragTask();
      }
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
    },
    getTeacherDefaultLayout() {
      const rect = this.componentRect || this.$el.getBoundingClientRect();
      if (this.isMobilePortraitBigRoom) {
        return {
          position: 'absolute',
          left: this.showStudent ? '0px' : `calc(50% - ${this.isFullScreen ? 0 : this.teacherVideoWidth / 2}px)`,
          top: `${rect.top}px`,
          width: `${this.teacherVideoWidth}px`,
          height: `${this.teacherVideoHeight}px`,
          display: 'block',
          zIndex: '300',
        };
      }
        return {
          position: 'absolute',
          left: `calc(100% - ${this.isFullScreen ? 0 : rect.width}px)`,
          top: `${rect.top}px`,
          width: `${this.teacherVideoWidth}px`,
          height: `${this.teacherVideoHeight}px`,
          display: 'block',
          zIndex: '300',
        };
    },
    getStudentDefaultLayout() {
      const rect = this.componentRect || this.$el.getBoundingClientRect();
      if (this.isMobilePortraitBigRoom) {
        return {
          position: 'absolute',
          right: '0',
          top: `${rect.top}px`,
          width: `${this.studentVideoWidth}px`,
          height: `${this.studentVideoHeight}px`,
          display: 'block',
          zIndex: '300',
        };
      }
        return {
          position: 'absolute',
          left: `calc(100% - ${this.isFullScreen ? 0 : rect.width}px)`,
          right: null,
          top: `${rect.top + this.studentVideoHeight}px`,
          width: `${this.studentVideoWidth}px`,
          height: `${this.studentVideoHeight}px`,
          display: 'block',
          zIndex: '300',
        };
    },
    getVideoWallRect() {
      const app = document.getElementById('app');
      const appRect = app.getBoundingClientRect();
      const isMobileWeb = TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isMobileNative();
      if (this.isPortrait
        && (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isOneOnOneClass())
        && isMobileWeb) {
        const width = appRect.width;
        const height = appRect.height;
        appRect.width = height;
        appRect.height = width;
      }

      const headerHeight = this.isSmallScreen ? 45 : 64;
      const rightSideWidth = !this.hasRightSide || this.isFullScreen ? 0 : this.videoWidth;
      const areaTop = appRect.top + headerHeight;
      const areaHeight = appRect.height - headerHeight;
      const areaWidth = appRect.width - rightSideWidth;
      const areaRadio = 16 / 9;
      if (areaWidth / areaHeight > areaRadio) {
        // 左右补黑边
        const realWidth = Math.floor(areaHeight * areaRadio);
        const paddingX = Math.floor((areaWidth - realWidth) / 2);
        this.boardTarget.update(
          appRect.left + paddingX,
          areaTop,
          realWidth,
          areaHeight,
        );
      } else {
        // 上下补黑边
        const realHeight = Math.floor(areaWidth / areaRadio);
        const paddingY = Math.floor((areaHeight - realHeight) / 2);
        this.boardTarget.update(
          appRect.left,
          areaTop + paddingY,
          areaWidth,
          realHeight,
        );
      }
      return this.boardTarget.getBoundingClientRect();
    },
    enableDrag(dom) {
      const self = this;
      const uid = this.isTeacherDom(dom) ? TCIC.SDK.instance.getClassInfo().teacherId : dom.getAttribute('label');
      const dragger = new Drag({
        dragEle: dom,
        targetEles: [self.boardTarget, self.$el],
        pad: 15,
        lockRadio: true,
        enableDrag: true,
        enableResize: false,
        onTargetChange(drag, target) {
          if (target === self.boardTarget) {   // 进入白板区
            drag.enableResize(true);
          } else if (target === self.$el) {
            dragger.enableResize(false);
            drag.reSize(self.videoWidth, self.videoHeight);
          } else {
            drag.reSize(self.videoWidth, self.videoHeight);
          }
        },
        onResize: (x, y, w, h, cursor, isReSize) => { // 位置更新
          if (isReSize && (w < self.videoWidth || h < self.videoHeight)) {  // 添加最小尺寸限制
            return;
          }
          if (dom.getAttribute('delete') === 'true') {    // 组件移除后不处理
            return ;
          }
          TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
            position: 'fixed',
            left: `${x * dragger.m_zoom}px`,
            top: `${y * dragger.m_zoom}px`,
            width: `${w}px`,
            height: `${h}px`,
            zIndex: 500,
            style: 'padding: 0px; overflow: visible;',
          }, dom.getAttribute('label'), false);
          document.body.style.cursor = cursor;
          dom.classList.add('drag');
          TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
        },
        onStartDrag: (drag, x, y) => {
          console.log(`OVideoWrapComponent::initDrag[${uid}]=>onStartDrag`);
        },
        onEndDrag: (drag, position, target) => {
          console.log(`OVideoWrapComponent::initDrag[${uid}]=>onEndDrag`);
          if (target === self.$el) {
            self.resetDomPosition(dom, true);
          } else if (target === self.boardTarget) {
            if (self.isTeacherDom(dom)) {
              self.videoWall && self.videoWall.unshiftVideo(uid, dom, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
            } else {
              self.videoWall && self.videoWall.pushVideo(uid, dom, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
            }
            self.prepareResize(dom);
          }
          self.updateVideoZIndex(dom);
          self.updateDragTask();
        },
        onStartResize: (drag, x, y) => {
          console.log(`OVideoWrapComponent::initDrag[${uid}]=>onStartResize`);
        },
        onEndResize: (drag, position) => {
          console.log(`OVideoWrapComponent::initDrag[${uid}]=>onEndResize`);
          self.updateVideoZIndex(dom);
          self.updateDragTask();
        },
      });
      this.dragMap.set(dom.id, dragger);
    },
    isTeacherDom(dom) {
      return dom.tagName.toLowerCase() === 'teacher-component';
    },
    getDomUserId(dom) {
      return this.isTeacherDom(dom) ? TCIC.SDK.instance.getClassInfo().teacherId : dom.getAttribute('label');
    },
    resetDomPosition(dom, update = false) {
      if (this.isTeacherDom(dom)) {
        TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), this.getTeacherDefaultLayout());
      } else {
        TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), this.getStudentDefaultLayout(), dom.getAttribute('label'));
      }
      dom.getVueInstance().setControlDirect('left');
      dom.getVueInstance().onLeaveVideoWrap(false);
      dom.setAttribute('sync', false);    // 关闭同步
      this.dragMap.get(dom.id)?.enableResize(false);
      if (this.videoWall) {
        if (this.videoWall.removeVideo(this.getDomUserId(dom), false)) {
        // 视频墙视频清空后重置状态
          this.updateVideoWallState(false);
        } else if (TCIC.SDK.instance.getState(Constant.TStateVideoWallMode) && update) { // 视频墙模式且非最后一个才需要刷新
          this.videoWall.updateRender();
        }
      }
      if (TCIC.SDK.instance.isTeacher()
          || TCIC.SDK.instance.isAssistant()
          || TCIC.SDK.instance.isSupervisor()) {  // 恢复白板视频时要同步
        this.updateDragTask();
      }
    },
    prepareResize(dom) {  // 进入白板区域状态调整
      dom.setAttribute('sync', true);    // 开启同步
      dom.getVueInstance().setControlDirect('bottom');
      dom.getVueInstance().onLeaveVideoWrap(true);
      this.dragMap.get(dom.id)?.enableResize(true);
    },
    updateVideoZIndex(ele) {   // 重新计算视频层级(设置为高一层)
      let curZIndex = 300;
      this.dragMap.forEach((drag) => {
        const dom = drag.getDragElement();
        if (dom !== ele) {
          dom.style.zIndex = curZIndex;
          curZIndex += 1;
        }
      });
      ele.style.zIndex = curZIndex;
    },
    // 更新视频状态状态并通过task保存视频墙状态
    updateVideoWallState(flag) {
      const lastState = TCIC.SDK.instance.getState(Constant.TStateVideoWallMode, false);
      if (lastState !== flag) {
        if (!this.isVideoOnlyLayout) {
          this.isVideoWallMode = flag;
          console.log(`OVideoWrapComponent::updateVideoWallState=>isVideoWallMode ${this.isVideoWallMode}`);
        }
        TCIC.SDK.instance.setState(Constant.TStateVideoWallMode, flag);
        // 同步任务
        if (flag) {
          TCIC.SDK.instance.updateTask(Constant.TConstantVideoWallTaskId);
        } else {
          TCIC.SDK.instance.stopTask(Constant.TConstantVideoWallTaskId);
        }
      }
    },
    // 计算视频的白板相对位置
    getRelativePosition(dom) {
      if (dom) {
        const domRect = dom.getBoundingClientRect();
        const boardRect = this.boardTarget.getBoundingClientRect();
        if (domRect.width === 0
            || domRect.height === 0
            || domRect.left + domRect.width - 1 > boardRect.left + boardRect.width
            || domRect.top + domRect.height - 1 > boardRect.top + boardRect.height
            || domRect.left + 1 < boardRect.left
            || domRect.top + 1 < boardRect.top) {
          return null;
        }
        return {
          left: ((domRect.left - boardRect.left) / boardRect.width).toFixed(3),
          top: ((domRect.top - boardRect.top) / boardRect.height).toFixed(3),
          width: (domRect.width / boardRect.width).toFixed(3),
          height: (domRect.height / boardRect.height).toFixed(3),
          zIndex: dom.style.zIndex,
        };
      }
      return null;
    },
    // 更新位置信息
    updateDragTask: Lodash.throttle(function () {
      const dragTaskArr = [];
      if (!TCIC.SDK.instance.isTeacher()
      && !TCIC.SDK.instance.isAssistant()
      && !TCIC.SDK.instance.isSupervisor()) return;     // 只有老师的位置才同步
      if (this.isVideoOnlyLayout) return; // 1v1视频课不同步位置
      this.dragMap.forEach((drag) => {
        const dom = drag.getDragElement();
        if (dom.getAttribute('sync')) {
          const info = this.getRelativePosition(dom);
          info && dragTaskArr.push({
            domId: dom.id,
            info,
          });
        }
      });
      TCIC.SDK.instance.updateTask('task-drag-array', JSON.stringify({
        sessionId: this.sessionId,
        array: dragTaskArr,
      }))
        .catch((err) => {
          console.warn('VideoWrapComponent::updateDragTask=>fail: ', err);
        });
    }, 500, {
      leading: false,
      trailing: true,
    }),
    // 处理位置同步任务
    processTaskUpdate(data) {
      if (data.sessionId === this.sessionId) { // 老师则忽略自己本次产生的任务
        return;
      }
      if (this.isVideoOnlyLayout) return;   // 1v1视频课忽略位置同步
      this.positionMap.clear();
      data.array.forEach((item) => {
        if (item.info.width || item.info.height) {  // 仅记录可拖动的视频组件位置信息
          console.log(`OVideoWrapComponent::onTaskUpdate=>${item.domId}, ${JSON.stringify(item)}`);
          this.positionMap.set(item.domId, item.info);
        }
      });
      if (!TCIC.SDK.instance.isMobile()) { // 移动端开播第一期不支持跟随老师布局放大视频
        this.updateAllPosition();
      }
    },
    // 获取元素布局
    getCurrentPostion(did) {
      if (this.positionMap.has(did) && !TCIC.SDK.instance.isMobile()) {
        const info = this.positionMap.get(did);
        const rect = this.boardTarget.getBoundingClientRect();
        if (info.width || info.height) {
          const realWidth = info.width * rect.width;
          const realHeight = info.height * rect.height;
          return {
            position: 'absolute',
            left: `${info.left * rect.width + rect.left}px`,
            top: `${info.top * rect.height + rect.top}px`,
            width: `${realWidth}px`,
            height: `${realHeight}px`,
            zIndex: info.zIndex,
            display: 'block',
          };
        }
      }
      return did.startsWith('teacher-component') ? this.getTeacherDefaultLayout() : this.getStudentDefaultLayout();
    },
    // 获取组件当前悬浮窗方向
    getCurrentCtrlDirection(did) {
      if (this.positionMap.has(did)) {
        const info = this.positionMap.get(did);
        if (info.width || info.height) {
          return 'bottom';
        }
      }
      return 'left';
    },
    // 更新可拖动视频位置
    updateDragDomPosition(id) {
      const dom = document.getElementById(id);
      if (!dom) {
        console.warn('OVideoWrapComponent::updateDragDomPosition=>id not found: ', id);
        return;
      }
      const layout = this.getCurrentPostion(id);
      // console.log('OVideoWrapComponent::updateDragDomPosition, updateComponent', dom.tagName.toLowerCase(), layout);
      TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), layout, dom.getAttribute('label'), true);
      dom.getVueInstance()?.setControlDirect(this.getCurrentCtrlDirection(id));
      dom.getVueInstance()?.onLeaveVideoWrap(this.positionMap.has(id));
    },
    // 更新所有位置
    updateAllPosition() {
      ['teacher-component-default', `student-component-${this.studentId}`]
        .forEach(did => this.updateDragDomPosition(did));
    },
    // 加载老师视频组件
    loadTeacherVideoCom() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      TCIC.SDK.instance.loadComponent('teacher-component', this.getCurrentPostion('teacher-component-default'))
        .then((ele) => {
          if (ele) {
            if (this.isVideoOnlyLayout) {
              this.videoWall && this.videoWall.unshiftVideo(classInfo.teacherId, ele);
            } else {
              ele.getVueInstance().setControlDirect('left');
              if (TCIC.SDK.instance.isTeacher()
                  || TCIC.SDK.instance.isAssistant()
                  || TCIC.SDK.instance.isSupervisor()) {
                ele.getVueInstance().setDbListener(() => this.onVideoDbClick(ele));
                this.enableDrag(ele);
              }
            }
            if (this.positionMap.has(ele.id)) { // 更新同步状态
              this.videoWall && this.videoWall.pushVideo(classInfo.teacherId, ele, false);
              ele.setAttribute('sync', this.positionMap.has(ele.id));
              ele.getVueInstance().setControlDirect('bottom');
            }
          }
        });
    },
    // 加载学生视频组件
    loadStudentVideoCom() {
      if (!this.studentId) {
        return;
      }
      TCIC.SDK.instance.loadComponent('student-component', this.getCurrentPostion(`student-component-${this.studentId}`), null, this.studentId)
        .then((ele) => {
          this.updateContainerSize();
          if (ele) {
            if (this.isVideoOnlyLayout) {
              this.videoWall && this.videoWall.pushVideo(this.studentId, ele);
            } else {
              ele.getVueInstance().setControlDirect('left');
              if (TCIC.SDK.instance.isTeacher()
                  || TCIC.SDK.instance.isAssistant()
                  || TCIC.SDK.instance.isSupervisor()) {
                ele.getVueInstance().setDbListener(() => this.onVideoDbClick(ele));
                this.enableDrag(ele);
              }
            }
            if (this.positionMap.has(ele.id)) { // 更新同步状态
              this.videoWall && this.videoWall.pushVideo(this.studentId, ele, false);
              ele.setAttribute('sync', this.positionMap.has(ele.id));
              ele.getVueInstance().setControlDirect('bottom');
            }
          }
        });
    },
    // 安全加载学生视频组件
    safeLoadStudentVideoCom(id) {
      this.studentId = id;
      this.showStudent = true;
      if (this.isMobilePortraitBigRoom) {
        this.updateDragDomPosition('teacher-component-default');
      }
      if (TCIC.SDK.instance.getUserId() === this.studentId) {
        this.makeSureClassJoined(() => this.loadStudentVideoCom());
      } else {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
          .then(() => {
            this.makeSureClassJoined(() => this.loadStudentVideoCom());
          });
      }
    },
    // 三分布局下，学生加入后修改右边栏的布局
    updateContainerSize() {
      if (TCIC.SDK.instance.isOneOnOneClass() && !TCIC.SDK.instance.isMobile()) {
        Util.updateLayout();
      }
    },
    // 是否Electron屏幕分享的视频容器布局
    setWrapLayout(layout) {
      const isShareScreen = (TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2) !== 2);
      console.log(`OVideoWrapComponent::setWrapLayout=>layout: ${layout}, isShareScreen: ${isShareScreen}`);
      const isScreenLayout = layout === 'screen';
      if (isScreenLayout === this.isScreenLayout) {
        return;
      }
      this.isScreenLayout = isScreenLayout;
      TCIC.SDK.instance.reportEvent('ooo_wrap_layout', { layout });
      // 这里不用处理，后面会触发resize
    },
    getRightSideWidth() {
      this.rightSideWidth = Util.getRightSideWidth();;
      return this.rightSideWidth;
    },
    onWindowResize() {
      this.rightSideWidth = Util.getRightSideWidth();
      this.updateAllPosition();
    },
  },
};
</script>
<style lang="less">
.o-video__container {
  width: 100%;
  height: 100%;
  background-color: #1c2131;
}

</style>
