<template>
  <div class="class-layout-component">
    <ul
      class="header-more-box"
      :class="isCoteaching ? 'flex-row' : 'flex-column'"
    >
      <li
        v-for="(item, i) in layoutList"
        :key="i"
        class="header-menu-li"
        :class="[{'header-menu-coli' : isCoteaching} , {'active' : item.type === currentLayout }]"
        @click="switchLayout(item)"
      >
        <i
          class="header__i i--menu"
          :class="[{'header__i__co' : isCoteaching } , `layout-ic-${item.type}`, {'active' : item.type === currentLayout}]"
        />
        {{ item.name }}
      </li>
    </ul>
    <!--<ul class="layout-list">
      <li v-for="(item, i) in layoutList" :key="i" @click="switchLayout(item)">
        <div class="icon" :class="[`icon-${item.type}`, {'active' : item.type === currentLayout}]" ></div>
        <div class="name">{{ item.name }}</div>
      </li>
    </ul>-->
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import { DocVideoLayoutType } from '@/constants/layout';
import BaseComponent from '@/component/core/BaseComponent';
export default {
  name: 'ClassLayoutComponent',
  extends: BaseComponent,
  data() {
    return {
      layoutList: [{
        name: i18next.t('顶部布局'),
        type: TCIC.TClassLayout.Top,
      }, {
        name: i18next.t('右侧布局'),
        type: TCIC.TClassLayout.Right,
      }, {
        name: i18next.t('左侧布局'),
        type: TCIC.TClassLayout.Left,
      }, {
        name: i18next.t('双排布局'),
        type: TCIC.TClassLayout.Double,
      }, {
        name: i18next.t('三分布局'),
        type: TCIC.TClassLayout.Three,
      }], // 布局列表
      currentLayout: TCIC.TClassLayout.Top,
      isCoteaching: false,
      isLinking: false,
      coTeacherLastLayout: TCIC.TClassLayout.CTGrid9, // 连麦前的布局
      roleInfo: null,
    };
  },
  mounted() {
    this.isCoteaching = TCIC.SDK.instance.isCoTeachingClass();
    this.currentLayout = this.getLayout();
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;

    // bugfix: 教师登录后同步一下布局状态，避免和其他端布局不一致的问题
    if (TCIC.SDK.instance.isTeacher()) {
      this.switchLayout({ type: this.currentLayout });
    }

    if (this.isCoteaching) {
      // 双师课布局较特殊
      // 一：1V1连麦前
      // 1. 是的，只是本地切换，显示9宫格/16宫格，只影响老师侧的观看布局；
      // 2. 学生端有两种布局：只看老师画面和画中画（老师和学生自己）
      // 二：1V1连麦后
      // 1. 老师端切换布局后，老师及所有学生端看到的布局都会以老师布局为准；
      // 2. 学生端不可以设置布局, 不显示菜单
      const linkUsers = TCIC.SDK.instance.getState(Constant.TStateCoTeachingLinkUsers, []);
      this.updateCoTeachingLayouts(linkUsers.length > 0);
      this.addLifecycleTCICStateListener(Constant.TStateCoTeachingLinkUsers, (users) => {
        this.updateCoTeachingLayouts(users.length > 0);
      });
    }

    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
      if (this.isCoteaching) {
        const linkUsers = TCIC.SDK.instance.getState(Constant.TStateCoTeachingLinkUsers, []);
        let force = false;
        if (linkUsers.length === 0) {
          if (layout === TCIC.TClassLayout.CTOnlyTeacher || layout === TCIC.TClassLayout.CTTeacher) {
            const saveLayout = TCIC.SDK.instance.getState(Constant.TStateCoTeachingStudentLayout, TCIC.TClassLayout.CTOnlyTeacher);
            if (saveLayout !== layout) {
              layout = saveLayout;
              force = true;
            }
          } else if (TCIC.SDK.instance.isTeacherOrAssistant()) {  // 老师和助教布局同步
            this.updateCurrentLayout(layout, false, false, false);
          } else {
            // do nothing
            return;
          }
        }
        this.updateCurrentLayout(layout, linkUsers.length > 0, force);
      } else {
        this.updateCurrentLayout(layout, false, false, false);
      }
    });
  },
  beforeDestroy() {
  },
  methods: {
    getLayout() {
      const layout = TCIC.SDK.instance.getClassLayout();
      const paramLayout = TCIC.SDK.instance.getParams('layout');
      if (this.isCoteaching) return layout; // 双师课
      if (paramLayout && paramLayout !== layout) {
        let needRender = false;
        Object.keys(DocVideoLayoutType).forEach((key) => {
          if (DocVideoLayoutType[key] === paramLayout) {
            needRender = true;
          }
        });
        if (needRender) {
          TCIC.SDK.instance.setClassLayout(paramLayout, true);
          return paramLayout;
        }
      }
      return layout;
    },
    hideComponent() {
      this.hide();
      this.$emit('hide');
    },
    updateCurrentLayout(layout, linking, coTeachingForce = false, sync = true) {
      if (this.currentLayout === layout) {
        if (this.isCoteaching && coTeachingForce) {
          // 强制更新下
        } else {
          return;
        }
      }
      if (!this.isCoteaching) {
        this.currentLayout = layout;
        return;
      }

      const sdk = TCIC.SDK.instance;
      if (linking) {
        if (sdk.isTeacherOrAssistant()) {
          const localLayouts = [TCIC.TClassLayout.CTPeer, TCIC.TClassLayout.CTStudent, TCIC.TClassLayout.CTTeacher];
          let shoudLayout = localLayouts.find(item => item === layout);
          if (shoudLayout === undefined) {
            shoudLayout = TCIC.TClassLayout.CTPeer;
          }

          if (this.currentLayout !== shoudLayout) {
            this.currentLayout = shoudLayout;
            // 连麦时给学生同步布局
            sdk.setClassLayout(shoudLayout, sync);
          }
        } else {
          // 学生侧连麦时，是从老师同步过来的
          this.currentLayout = layout;
        }
      } else {
        if (sdk.isTeacherOrAssistant()) {
          const localLayouts = [TCIC.TClassLayout.CTGrid9, TCIC.TClassLayout.CTGrid16];
          let shoudLayout = localLayouts.find(item => item === layout);
          if (shoudLayout === undefined) {
            shoudLayout = TCIC.TClassLayout.CTGrid9;
          }

          if (this.currentLayout !== shoudLayout) {
            this.coTeacherLastLayout = shoudLayout;
            this.currentLayout = shoudLayout;
            sdk.setClassLayout(this.currentLayout, sync);
          }
        } else {
          // 学生侧
          const localLayouts = [TCIC.TClassLayout.CTOnlyTeacher, TCIC.TClassLayout.CTTeacher];
          let shoudLayout = localLayouts.find(item => item === layout);
          if (shoudLayout === undefined) {
            shoudLayout = TCIC.TClassLayout.CTOnlyTeacher;
          }

          if (this.currentLayout !== shoudLayout || coTeachingForce) {
            // 非连麦时，仅本地自身用
            this.currentLayout = shoudLayout;
            TCIC.SDK.instance.setState(Constant.TStateCoTeachingStudentLayout, shoudLayout);
            sdk.setClassLayout(this.currentLayout, false);
          }
        }
      }
    },
    updateCoTeachingLayouts(isLinking) {
      if (this.isCoteaching) {
        const isTeacher = TCIC.SDK.instance.isTeacherOrAssistant();
        if (isTeacher) {
          if (!isLinking) {
            this.layoutList = [{
              name: i18next.t('9宫格'),
              type: TCIC.TClassLayout.CTGrid9,
            }, {
              name: i18next.t('16宫格'),
              type: TCIC.TClassLayout.CTGrid16,
            }];
            if (this.currentLayout !== TCIC.TClassLayout.CTGrid9
              && this.currentLayout !== TCIC.TClassLayout.CTGrid16) {
              this.updateCurrentLayout(this.coTeacherLastLayout, isLinking);
            }
          } else {
            this.layoutList = [{
              name: i18next.t('对等画面'),
              type: TCIC.TClassLayout.CTPeer,
            }, {
              name: i18next.t('放大{{arg_0}}画面', { arg_0: this.roleInfo.student }),
              type: TCIC.TClassLayout.CTStudent,
            }, {
              name: i18next.t('放大我的画面'),
              type: TCIC.TClassLayout.CTTeacher,
            }];
            if (this.currentLayout !== TCIC.TClassLayout.CTPeer
              || this.currentLayout !== TCIC.TClassLayout.CTStudent
              || this.currentLayout !== TCIC.TClassLayout.CTTeacher) {
              this.updateCurrentLayout(TCIC.TClassLayout.CTPeer, isLinking);
            }
          }
        } else {
          if (!isLinking) {
            // 学生
            this.layoutList = [{
              name: i18next.t('只看{{arg_0}}画面', { arg_0: this.roleInfo.teacher }),
              type: TCIC.TClassLayout.CTOnlyTeacher,
            }, {
              name: i18next.t('画中画'),
              type: TCIC.TClassLayout.CTTeacher,
            }];
            if (this.currentLayout !== TCIC.TClassLayout.CTOnlyTeacher
              || this.currentLayout !== TCIC.TClassLayout.CTTeacher) {
              const saveLayout = TCIC.SDK.instance.getState(Constant.TStateCoTeachingStudentLayout, TCIC.TClassLayout.CTOnlyTeacher);
              this.updateCurrentLayout(saveLayout, isLinking);
            }
          }
        }
        this.isLinking = isLinking;
      }
    },
    // 布局切换
    switchLayout(layout) {
      if (TCIC.SDK.instance.getState(Constant.TStateCarousel)) {
        window.showToast(i18next.t('请先结束当前循环上台，再修改布局！'));
      } else {
        if (this.isCoteaching) {
          // 走双师课切换布局逻辑
          const linkUsers = TCIC.SDK.instance.getState(Constant.TStateCoTeachingLinkUsers, []);
          this.updateCurrentLayout(layout.type, linkUsers.length > 0);
        } else {
          TCIC.SDK.instance.setClassLayout(layout.type);
        }
        this.hideComponent();
      }
    },
  },
};
</script>
<style lang="less">
.el-popover {
  &.submenu-popover {
    .header-more-box {
      &.flex-column {
        flex-direction: column;
      }
      &.flex-row {
        list-style: none;
        flex-direction: row;
        li {
          float: left;
          display: block;
          i {
            text-align: center;
          }
        }
      }
      .header-menu-li {
        &.header-menu-coli {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-bottom: none;
          height: 188px;
          margin: 16px 8px;
          padding: 8px;
          border-radius: 8px;
          text-align: center;
          .header__i {
            margin: 10px;
            &.i--menu {
              width: 144px;
              height: 112px;
            }
          }
          &.active {
            background:var(--primary-color, #006EFF);
          }
          .layout-ic-ctgrid9 {
            background-size: cover;
            background-image: url('./assets/ctgrid9.png');
          }

          .layout-ic-ctgrid16 {
            background-size: cover;
            background-image: url('./assets/ctgrid16.png');
          }
          .layout-ic-ctpeer {
            background-size: contain;
            background-image: url('./assets/ctpeer.png');
          }
          .layout-ic-ctstudent {
            background-size: contain;
            background-image: url('./assets/ctstudent.png');
          }
          .layout-ic-ctteacher {
            background-size: contain;
            background-image: url('./assets/ctteacher.png');
          }
          .layout-ic-ctonlyteacher {
            background-size: contain;
            background-image: url('./assets/ctonlyteacher.png');
          }
        }
        &.header-menu-coli:hover {
          color: white;
        }
        .layout-ic-top {
          background-size: contain;
          background-image: url('./assets/top.png');
          &.active {
            background-image:url('./assets/top_active.png');
          }
        }
        .layout-ic-right {
          background-size: contain;
          background-image: url('./assets/right.png');
          &.active {
            background-image:url('./assets/right_active.png');
          }
        }
        .layout-ic-left {
          background-size: contain;
          background-image: url('./assets/right.png');
          transform: rotate(180deg);
          &.active {
            background-image:url('./assets/right_active.png');
          }
        }
        .layout-ic-double {
          background-size: contain;
          background-image: url('./assets/double.png');
          &.active {
            background-image:url('./assets/double_active.png');
          }
        }
        .layout-ic-three {
          background-size: contain;
          background-image: url('./assets/three.png');
          &.active {
            background-image:url('./assets/three_active.png');
          }
        }
      }
    }
  }
}
.class-layout-component {}
</style>
