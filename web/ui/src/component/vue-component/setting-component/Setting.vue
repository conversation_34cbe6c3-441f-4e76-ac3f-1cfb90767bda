<template>
  <el-tabs
    v-model="activeTab"
    class="setting__tabs"
    :class="{ 'no-header': tabList.length <= 1 }"
    :v-if="component.active"
  >
    <el-tab-pane
      v-if="tabMap.audio"
      :label="space + $t('音频') + space"
      name="audio"
    >
      <div class="setting__section">
        <div class="setting__section-title">
          {{ $t('麦克风') }}
        </div>
        <section class="setting-detect section">
          <div class="section-item">
            <el-select
              ref="microphone-select"
              v-model="microphone"
              :class="{
                'is-error': (detection.microphone.available !== null && microphones.length === 0) || detection.microphone.volume === 0,
                'is-warning': detection.microphone.volume <= 80 && detection.microphone.volume > 0,
              }"
              :popper-append-to-body="false"
              :placeholder="$t(detection.microphone.available !== null && microphones.length === 0 ? detection.microphone.tip.nodevice : $t('请选择设备'))"
              :no-data-text="$t(detection.microphone.tip.nodevice)"
              value-key="deviceId"
              popper-class="device-detect-select"
              :disabled="detection.microphone.available === null"
              @change="(arg) => onMicrophoneChange(arg, '', 'userChange')"
            >
              <el-option
                v-for="item in microphones"
                :key="item.value"
                :label="item.deviceName"
                :value="item"
              />
            </el-select>
            <el-tooltip
              effect="dark"
              :content="micTooltip"
              placement="right"
              :popper-class="detection.microphone.available ? '' : 'detect-error-tps'"
            >
              <i
                v-loading="detection.microphone.available === null"
                element-loading-spinner="el-icon-loading"
                element-loading-background="transparent"
                :class="{
                  'success-icon': detection.microphone.available,
                  'warning-icon': detection.microphone.available === false,
                  'warning-icon': detection.microphone.available && detection.microphone.volume === 0,
                  'hint-icon': detection.microphone.available && detection.microphone.volume > 0 && detection.microphone.volume <= 80,
                }"
                class="ml10 base-icon"
              />
            </el-tooltip>
          </div>
          <div class="sub-section">
            <div class="volume">
              <div class="mic-icon" />
              <ul class="capacity">
                <li
                  v-for="item in 21"
                  :key="item"
                  class="item"
                  :class="{ active: item < detection.microphone.volumeLevel }"
                />
              </ul>
            </div>
            <p class="pt10 sub-text">
              {{ $t('对着麦克风说话可以看到波动效果') }}
            </p>
            <div
              class="volume volume-scrollbar"
              @mousedown.stop
              @drag.stop
            >
              <i
                :class="[detection.microphone.volume > 0 ? 'speaker-icon' : 'speaker-icon disable']"
                @click="disableMicVolume"
              />
              <div class="capacity">
                <div class="slider-wrapper">
                  <el-slider
                    v-model="detection.microphone.volume"
                    :show-tooltip="false"
                    :format-tooltip="(item) => { return `${item}%` }"
                    @change="onMicrophoneVolumeChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <div
        v-if="!isSafari && !isMobileAll && isWeb"
        class="setting__section"
      >
        <div class="setting__section-title">
          {{ $t('扬声器') }}
        </div>
        <section class="setting-detect section">
          <!-- 去除切换扬声器的部分，只保留音量调节功能 -->
          <div class="section-item">
            <el-select
              ref="speaker-select"
              v-model="speaker"
              :class="{
                'is-error': (detection.speaker.available !== null && speakers.length === 0) || detection.speaker.volume === 0,
                'is-warning': detection.speaker.volume <= 80 && detection.speaker.volume > 0,
              }"
              :popper-append-to-body="false"
              :placeholder="$t(detection.speaker.available !== null && speakers.length === 0 ? detection.speaker.tip.nodevice : $t('请选择设备'))"
              :no-data-text="$t(detection.speaker.tip.nodevice)"
              value-key="deviceId"
              popper-class="device-detect-select"
              :disabled="detection.speaker.available === null"
              @change="(arg) => onSpeakerChange(arg, 'userChange')"
            >
              <el-option
                v-for="item in speakers"
                :key="item.value"
                :label="item.deviceName"
                :value="item"
              />
            </el-select>
          </div>
          <div class="sub-section volume-test">
            <div
              class="volume volume-scrollbar"
              @mousedown.stop
              @drag.stop
            >
              <i
                :class="[detection.speaker.volume > 0 ? 'speaker-icon' : 'speaker-icon disable']"
                @click="disableSpeakerVolume"
              />
              <div class="capacity">
                <div class="slider-wrapper">
                  <el-slider
                    v-model="detection.speaker.volume"
                    :show-tooltip="false"
                    :format-tooltip="(item) => { return `${item}%` }"
                    @change="onSpeakerVolumeChange"
                  />
                </div>
              </div>
              <el-button
                v-if="!isMobileNative"
                class="plain"
                type="text"
                size="mini"
                @click="toggleAudioPlay"
              >
                {{ audioPlayStatus ? $t('暂停') : $t('试听') }}
              </el-button>
            </div>
          </div>
        </section>
      </div>
      <div
        v-if="isAIDenoiseSupported && isRTCMode"
        class="setting__section"
      >
        <div class="setting__section-title">
          {{ $t('音频设置') }}
        </div>
        <div class="setting-simple-item switch-wrap setting-denoise">
          <span class="setting-item-label">
            {{ $t('AI降噪') }}
            <span class="setting-item-label-beta"><span>Beta</span></span>
          </span>
          <el-switch
            v-model="enableAIDenoise"
            class="switch"
            active-color="#13A449"
          />
        </div>
      </div>
      <div
        v-if="isWeb"
        class="setting__section"
      >
        <div class="setting__section-title">
          {{ $t('设备检查') }}
        </div>
        <div class="setting-simple-item switch-wrap setting-denoise">
          <span class="setting-item-label">
            <el-button
              type="primary"
              @click="startCheckDevices"
            >开始检测</el-button>
          </span>
        </div>
      </div>
      <div
        v-if="classPermission.audio"
        class="setting__section"
      >
        <div class="setting__section-title">
          {{ audioRoomLabel }}
        </div>
        <div class="setting-simple-item switch-wrap setting-video-model">
          <el-tooltip
            class="item"
            :content="audioRoomTooltip"
            placement="bottom"
            popper-class="pure-audio-tooltip"
            :manual="interactiveIsTouchEvent()"
            :value="showPureAudioTips"
          >
            <span
              class="setting-item-label"
              @touchstart="trigger"
            >
              {{ audioRoomLabel }}
              <i
                class="setting-item-label-icon"
                @click="trigger"
                @touchstart="trigger"
              />
            </span>
          </el-tooltip>
          <el-switch
            v-model="pureAudioMode"
            class="switch"
            active-color="#13A449"
            @change="switchVideoModel"
          />
        </div>
      </div>
    </el-tab-pane>
    <!-- 视频设置 -->
    <el-tab-pane
      v-if="tabMap.video"
      :label="space + $t('视频') + space"
      name="video"
    >
      <div class="setting__section">
        <div class="setting__section-title">
          {{ $t('摄像头') }}
        </div>
        <section class="setting-detect section">
          <div class="section-item">
            <el-select
              ref="camera-select"
              v-model="camera"
              :class="[detection.camera.available !== null && cameras.length === 0 ? 'is-error' : '']"
              :popper-append-to-body="false"
              :placeholder="$t(detection.camera.available !== null && cameras.length === 0 ? detection.camera.tip.nodevice : $t('请选择设备'))"
              :no-data-text="$t(detection.camera.tip.nodevice)"
              value-key="deviceId"
              popper-class="device-detect-select"
              :disabled="detection.camera.available === null"
              @change="(arg) => onCameraChange(arg, '', 'userChange')"
            >
              <el-option
                v-for="item in cameras"
                :key="item.value"
                :label="item.deviceName"
                :value="item"
                icon="el-icon-circle-plus"
              />
            </el-select>
            <el-tooltip
              effect="dark"
              :content="cameraStatusTip"
              placement="top-start"
              :popper-class="detection.camera.available ? '' : 'detect-error-tps'"
            >
              <i
                v-loading="detection.camera.available === null"
                element-loading-spinner="el-icon-loading"
                element-loading-background="transparent"
                :class="{ 'success-icon': detection.camera.available, 'warning-icon': detection.camera.available === false }"
                class="ml10 base-icon"
              />
            </el-tooltip>
          </div>
          <p class="sub-section pt10">
            <el-checkbox
              v-model="mirror"
              :disabled="!detection.camera.available"
              :checked="mirror"
            >
              {{ $t('镜像模式') }}
            </el-checkbox>
          </p>
        </section>
      </div>
      <CustomFeature feature="Beautify.Enable">
        <div class="setting__section">
          <div class="setting__section-title">
            {{ $t('美颜设置') }}
          </div>
          <div class="setting-beauty">
            <RawBeautyConfigComponent />
          </div>
        </div>
        <div
          v-if="showVirtualBackground"
          class="setting__section"
        >
          <div class="setting__section-title">
            {{ $t('虚拟背景') }}
          </div>
          <div class="setting-beauty">
            <RawVirtualBackgroundSelectComponent />
          </div>
        </div>

        <div
          v-if="showAvatar"
          class="setting__section"
        >
          <div class="setting__section-title">
            {{ $t('虚拟形象') }}
          </div>
          <div class="setting-beauty">
            <VirtualAvatarSelectComponent />
          </div>
        </div>
      </CustomFeature>
    </el-tab-pane>
    <el-tab-pane
      v-if="tabMap.general"
      :label="space + $t('常规') + space"
      name="general"
    >
      <div class="setting-simple-item select-wrap setting-language">
        <span class="setting-item-label">
          {{ $t('语言') }}
        </span>
        <el-select
          v-if="showLanguageSelector"
          v-model="selectLanguage"
          :popper-append-to-body="false"
          @change="onLanguageChange"
        >
          <el-option
            v-for="item in languageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div
          v-else
          class="setting-item-content"
        >
          {{ currentLanguageText }}
        </div>
      </div>
      <div class="setting__section">
        <div class="setting-simple-item switch-wrap setting-denoise">
          <span class="setting-item-label">
            {{ $t('显示成员进出情况') }}
          </span>
          <el-switch
            v-model="showJoinExit"
            class="switch"
            active-color="#13A449"
            @change="switchShowJoinExit"
          />
        </div>
      </div>
      <div class="setting__section">
        <div class="setting-simple-item switch-wrap setting-denoise">
          <span class="setting-item-label">
            {{ $t('显示成员举手') }}
          </span>
          <el-switch
            v-model="showHandsUp"
            class="switch"
            active-color="#13A449"
            @change="switchShowHandsUp"
          />
        </div>
      </div>
      <div
        v-if="isMiniProgramWebview && (fromMpAppId === 'wxbc2aedd3838d78cd' || fromMpAppId === 'wx4b995251e6a51482')"
        class="setting-simple-item clickable setting-policy"
        @click="onOpenPrivacyPolicy"
      >
        <span class="setting-item-label">
          {{ $t('隐私政策') }}
        </span>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import i18next from 'i18next';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import Util from '@/util/Util';
import Constant from '@/util/Constant';
import store from 'store';
import { DeviceDetectBaseComponent } from '../device-detect-component/DeviceDetectBase';
import RawBeautyConfigComponent from '../video-component/RawBeautyConfigComponent.vue';
import RawVirtualBackgroundSelectComponent from '../video-component/RawVirtualBackgroundSelectComponent.vue';
import VirtualAvatarSelectComponent from '../video-component/VirtualAvatarSelectComponent.vue';
import CustomFeature from '../../ui-component/feature/CustomFeature.vue';

export default {
  name: 'SettingComponent',
  components: { RawBeautyConfigComponent, RawVirtualBackgroundSelectComponent, VirtualAvatarSelectComponent, CustomFeature },
  extends: DeviceDetectBaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const tabMap = {
      audio: true,
      video: true,
      general: true,
    };
    const tabList = Object.keys(tabMap).filter(key => tabMap[key]);
    const activeTab = tabList[0];
    return {
      space: '\u3000',
      popoverTask: null,
      pureAudioMode: false,
      isClassStarted: false,
      closeDelay: TCIC.SDK.instance.isMobile() ? 4000 : 1000,
      isMobile: TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad(),
      isMobileNative: TCIC.SDK.instance.isMobileNative(),
      isMobileIncludePad: TCIC.SDK.instance.isMobile(),
      showPureAudioTips: false,
      showVirtualBackground: false,
      showAvatar: false,
      showLanguageSelector: false,
      isAIDenoiseSupported: false,
      hasJoinedClass: false,
      denoiseStorageKey: '', // `denoise_${audioQuality}_${userId}`
      denoise: null, // 进房时修改，触发调用 trtc 接口
      classPermission: {
        audio: false,
      },
      roomInfo: {},
      selectLanguage: '',
      currentLanguage: '',
      languageOptions: TCIC.SDK.instance.getLanguageOptions(),
      fromMpAppId: TCIC.SDK.instance.getFromMpAppId(),
      isMiniProgramWebview: TCIC.SDK.instance.isMiniProgramWebview(),
      tabMap,
      tabList,
      activeTab,
      isDeviceDetecting: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect, true),
      pendingData: {}, // 设备检测期间的数据放在这，检测完后统一处理
      showJoinExit: true,
      showHandsUp: true,
      isRTCMode: false,
      isSafari: false,
      isMobileAll: TCIC.SDK.instance.isMobile(),
      isWeb: TCIC.SDK.instance.isWeb(),
    };
  },
  computed: {
    audioRoomTooltip() {
      return i18next.t('开启音频{{arg_0}}后，您将通过仅接收音频的方式{{arg_0}}，此操作能够减轻网络卡顿现象。', { arg_0: this.roomInfo.room });
    },
    audioRoomLabel() {
      return i18next.t('音频{{arg_0}}', { arg_0: this.roomInfo.room });
    },
    currentLanguageText() {
      if (!this.currentLanguage) {
        return '';
      }
      const lngOption = this.languageOptions.find(item => item.value === this.currentLanguage);
      if (!lngOption) {
        return '';
      }
      return lngOption.label;
    },
    cameraStatusTip() {
      if (this.detection.camera.available === null) {
        return this.detection.camera.tip.detecting;
      }
      if (this.cameras.length === 0) {
        return this.detection.camera.tip.nodevice;
      }
      if (this.detection.camera.available) {
        return this.detection.camera.tip.available;
      }
      let tip = i18next.t('设备不可用（损坏、未授权等）');
      const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
      switch (errCode) {
        case -1001: // 打开失败
          tip = i18next.t('摄像头打开失败，请检查系统设置');
          break;
        case -1002: // 找不到设备
          tip = i18next.t('找不到可用摄像头');
          break;
        case -1003: // 未授权
          tip = i18next.t('摄像头权限未开启，请前往开启');
          break;
        case -1004: // 设备被占用
          tip = i18next.t('摄像头被占用，请关闭其他软件');
          break;
        case 2: // 已关闭
          tip = i18next.t('摄像头已关闭，请开启');
          break;
        default:
          break;
      }
      return tip;
    },
  },
  watch: {
    /*
      这些已经移到 DeviceDetectBase:
        mirror
        audioPlayStatus
    */
    async denoise(val) {
      if (!this.hasJoinedClass) {
        return;
      }
      this.enableAIDenoise(val);
    },
    'component.active'(value) {
      if (value) {
        this.getDevices();
      }
    },
  },
  async mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.deviceDetectReportAction = 'setting-device-detect';
    this.isAIDenoiseSupported = TCIC.SDK.instance.isAIDenoiseSupported ? TCIC.SDK.instance.isAIDenoiseSupported() : false;
    this.makeSureClassJoined(this.onJoinClass);

    // 设备检测
    this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, (val) => {
      this.isDeviceDetecting = val;
      if (!this.isDeviceDetecting) {
        console.log('[Setting] isDeviceDetecting false');
        this.initDevices();
      }
    });
    this.$EventBus.$on('camera-available', ({ deviceId, available }) => {
      if (this.isDeviceDetecting) {
        this.pendingData['camera-available'] = { deviceId, available };
        return;
      }
      TCIC.SDK.instance.reportLog('setting-device-detect', `[camera-available] deviceId ${deviceId}, available ${available}`);
      const device = this.cameras?.find(item => item.deviceId === deviceId);
      if (device) {
        this.camera = device;
      }
      // 这里拉到列表就算 available
      // this.detection.camera.available = available;
    });
    this.$EventBus.$on('mic-available', ({ deviceId, available }) => {
      if (this.isDeviceDetecting) {
        this.pendingData['mic-available'] = { deviceId, available };
        return;
      }
      TCIC.SDK.instance.reportLog('setting-device-detect', `[mic-available] deviceId ${deviceId}, available ${available}`);
      const device = this.microphones?.find(item => item.deviceId === deviceId);
      if (device) {
        this.microphone = device;
      }
      // 这里拉到列表就算 available
      // this.detection.microphone.available = available;
    });
    this.$EventBus.$on('speaker-available', ({ deviceId, available }) => {
      if (this.isDeviceDetecting) {
        this.pendingData['speaker-available'] = { deviceId, available };
        return;
      }
      TCIC.SDK.instance.reportLog('setting-device-detect', `[speaker-available] deviceId ${deviceId}, available ${available}`);
      const device = this.speakers?.find(item => item.deviceId === deviceId);
      if (device) {
        this.speaker = device;
      }
      // 这里拉到列表就算 available
      // this.detection.speaker.available = available;
    });
    this.$EventBus.$on('update:micVolume', (value) => {
      if (this.isDeviceDetecting) {
        this.pendingData['update:micVolume'] = value;
        return;
      }
      // 恢复音量在 initDevices 处理，这里保持数据同步
      const { microphone } = this.detection;
      microphone.volume = value;
    });
    this.$EventBus.$on('update:speakerVolume', (value) => {
      if (this.isDeviceDetecting) {
        this.pendingData['update:speakerVolume'] = value;
        return;
      }
      // 恢复音量在 initDevices 处理，这里保持数据同步
      const { speaker } = this.detection;
      speaker.volume = value;
    });

    this.$EventBus.$on('mic-status-change', (value) => {
      try {
        const { microphone } = this.detection;
        if (value === 'disable' || value === null) {
          microphone.available = false;
        } else {
          microphone.available = true;
        }
      } catch (e) {
        console.error('mic status err:', e, value);
      }
    });

    // 语言
    TCIC.SDK.instance.on(TCIC.TMainEvent.Language_Update, () => {
      this.updateLanguage();
    });
    this.updateLanguage();

    const videoFlag = TCIC.SDK.instance.getState(Constant.TStateAudioMode);
    this.pureAudioMode = videoFlag === true;
    this.addLifecycleTCICStateListener(Constant.TStateAudioMode, this.onAudioMode);
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Class_Status,
      (status) => {
        this.isClassStarted = status !== TCIC.TClassStatus.Not_Start;
      },
    );
    if (TCIC.SDK.instance.isMobile() && TCIC.SDK.instance.isMobileNative()) {
      await this.detectBrowser();
      if (!this.detection.browser.available) {
        TCIC.SDK.instance.showMessageBox(
          '',
          i18next.t('当前浏览器不支持观看，请更换浏览器后重试，详细的浏览器兼容请点击查看'),
          [i18next.t('确定')],
        );
      }
    }
    const showHandsUp = store.get('showHandsUp');
    if (typeof showHandsUp === 'boolean') {
      this.showHandsUp = showHandsUp;
    }
    // 监听设备的变化
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Device_Changed, this.deviceChangeListener);

    this.isSafari = DetectUtil.isSafari();
    this.getDevices();
  },
  beforeDestroy() {
    console.log('[Setting] beforeDestroy');
    this.$EventBus.$off('camera-available');
    this.$EventBus.$off('mic-available');
    this.$EventBus.$off('speaker-available');
    this.$EventBus.$off('update:micVolume');
    this.$EventBus.$off('update:speakerVolume');
  },
  methods: {
    getDevices() {
      this.reportDeviceDetectLog(`[getDevices] emited`);
      this.enumerateMicrophone();
      this.enumerateSpeaker();
      this.enumerateCamera();
    },
    deviceChangeListener({ deviceId, type, state, name }) {
      this.reportDeviceDetectLog(`[deviceChangeListener] type: ${type}, deviceId: ${deviceId}, state: ${state}`);
      switch (state) {
        case TCIC.TTrtcDeviceState.Add:
          this.deviceAddHandler({ deviceId, type });
          break;
        case TCIC.TTrtcDeviceState.Remove:
          this.deviceRemoveHandler({ deviceId, type });
          break;
        case TCIC.TTrtcDeviceState.Active:
          this.deviceActiveHandler({ deviceId, type });
          // this.showDeviceChangeToast({ deviceId, type, name });
          break;
        case TCIC.TTrtcDeviceState.Update:
          this.deviceUpdateHandler({ deviceId, type });
          break;
      }
    },
    /*
    * 设备可用
    * 如果是当前设备：
    *   1.设备检测
    * 如果不是当前设备：
    *   do nothing
    * */
    async deviceActiveHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;

      switch (type) {
        case Mic:
          console.log('::device-detect, deviceChange | deviceActiveHandler | mic => ', deviceId);
          // 如果当前设备切换成可用，重新检测
          if (this.microphone.deviceId === deviceId) {
            await this.detect('microphone', `active device ${deviceId}`);
          }
          break;
        case Camera:
          console.log('::device-detect, deviceChange | deviceActiveHandler | camera => ', deviceId);
          if (this.camera.deviceId === deviceId) {
            await this.detect('camera', `active device ${deviceId}`);
          }
          break;
        case Speaker:
          console.log('::device-detect, deviceChange | deviceActiveHandler | speaker => ', deviceId);
          if (this.speaker.deviceId === deviceId) {
            await this.detect('speaker', `active device ${deviceId}`);
          }
          break;
      }
    },

    async startCheckDevices() {
      console.log('start check devices');
      await TCIC.SDK.instance.showDeviceDetectDailog();
    },
    async onJoinClass() {
      console.log('[Setting] onJoinClass');
      this.hasJoinedClass = true;
      this.isRTCMode =  TCIC.SDK.instance.getState(TCIC.TMainState.RTC_Mode, true);
      this.classPermission.audio = TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Class_Audio, TCIC.TPermissionFlag.Read);

      const classInfo = TCIC.SDK.instance.getClassInfo();
      const isAndroidOrIOS = TCIC.SDK.instance.isAndroid() || TCIC.SDK.instance.isIOS();
      const isElectron = TCIC.SDK.instance.isElectron();

      // 不能上台的只保留常规tab：巡课、1v0的非老师
      if (TCIC.SDK.instance.isSupervisor()
        || (classInfo.maxRtcMember === 0 && !TCIC.SDK.instance.isTeacher())
      ) {
        this.tabMap = {
          general: true,
        };
        this.tabList = ['general'];
        this.activeTab = 'general';
      }
      TCIC.SDK.instance.reportLog('setting-tabs', this.tabList);

      if (this.tabMap.video) {
        // 虚拟背景
        this.showVirtualBackground = (!isAndroidOrIOS && !isElectron) || TCIC.SDK.hasElectronBeautyPlugin();
        // 不展示虚拟形象 https://tapd.woa.com/tapd_fe/70068217/story/detail/1070068217124845460
        this.showAvatar = false;
      }

      if (this.tabMap.audio) {
        // AI降噪，支持的才设置默认值
        // this.denoiseStorageKey = `denoise_${classInfo.audioQuality}_${TCIC.SDK.instance.getUserId()}`;
        if (this.isAIDenoiseSupported && this.isRTCMode) {
          TCIC.SDK.instance.reportLog(
            'setting-denoise',
            `isAIDenoiseSupported ${this.isAIDenoiseSupported} ${this.enableAIDenoise}`,
          );
          if (!this.enableAIDenoise) {
            // 如果是false，不会触发 denoise 变化，主动调用一次
            this.toggleAIDenoise(this.enableAIDenoise);
          }
        } else {
          TCIC.SDK.instance.reportLog(
            'setting-denoise',
            `isAIDenoiseSupported ${this.isAIDenoiseSupported}, audioQuality ${classInfo.audioQuality}`,
          );
        }
      }

      if (this.tabMap.general) {
        // 语言选择
        this.showLanguageSelector = TCIC.SDK.instance.isFeatureAvailable('LanguageSelector');
      }

      if (this.video || this.tabMap.audio) {
        this.initDevices();
      }
    },
    async initDevices() {
      if (!this.hasJoinedClass || this.isDeviceDetecting) {
        return;
      }
      TCIC.SDK.instance.reportLog('setting-device-detect', `initDevices, pendingData ${JSON.stringify(this.pendingData)}`);
      console.log('[Setting] initDevices', this.visible);
      // 初始化设备数据
      this.initDeviceData();

      // 初始化设备事件监听
      this.initDeviceListener();

      if (this.tabMap.video) {
          await this.enumerateCamera(true);

        if (localStorage.getItem('mirror')) {
          this.mirror = JSON.parse(localStorage.getItem('mirror'));
        } else {
          this.mirror = true;
        }
      }

      if (this.tabMap.audio) {
          // 麦克风
          await this.enumerateMicrophone(true);

        const isRTCMode = TCIC.SDK.instance.getState(TCIC.TMainState.RTC_Mode, true);
        if (isRTCMode) {
          if (localStorage.getItem('aiDenoise')) {
            this.enableAIDenoise = JSON.parse(localStorage.getItem('aiDenoise'));
          } else {
            this.enableAIDenoise = true;
          }
        }

        if (typeof this.pendingData['update:micVolume'] === 'number') {
          let micVolume = this.pendingData['update:micVolume'];
          let msg = `use micVolume from pendingData ${micVolume}`;
          if (micVolume === 0) {
            // 如果静音了，自动调整成 50%
            micVolume = 50;
            msg += `, recover to ${micVolume}`;
          }
          TCIC.SDK.instance.reportLog('setting-device-detect', msg);
          this.detection.microphone.volume = micVolume;
          await this.onMicrophoneVolumeChange(micVolume);
        }
      }
      // 不管要不要音频tab，扬声器都检测一下
      await this.enumerateSpeaker(true);
      if (typeof this.pendingData['update:speakerVolume'] === 'number') {
        let speakerVolume = this.pendingData['update:speakerVolume'];
        let msg = `use speakerVolume from pendingData ${speakerVolume}`;
        if (speakerVolume === 0) {
          // 如果静音了，自动调整成 50%
          speakerVolume = 50;
          msg += `, recover to ${speakerVolume}`;
        }
        TCIC.SDK.instance.reportLog('setting-device-detect', msg);
        this.detection.speaker.volume = speakerVolume;
        await this.onSpeakerVolumeChange(speakerVolume);
      }

      console.log('::setting-device-detect, initDevices end', this.detection);
    },

    onOpenBeautySetting() {
      const componentName = 'beauty-config-component';
      const component = TCIC.SDK.instance.getComponent(componentName);
      if (component) {
        component.getVueInstance().show();
      } else {
        TCIC.SDK.instance.loadComponent(componentName, {
          display: 'block',
          zIndex: 300,
        });
      }
      this.hide();
    },
    onOpenVirtualBackground() {
      const componentName = 'virtual-background-component';
      const component = TCIC.SDK.instance.getComponent(componentName);
      if (component) {
        component.getVueInstance().show();
      } else {
        TCIC.SDK.instance.loadComponent(componentName, {
          display: 'block',
          zIndex: 300,
        });
      }
      this.hide();
    },
    onOpenPrivacyPolicy() {
      const componentName = 'privacy-policy-component';
      const component = TCIC.SDK.instance.getComponent(componentName);
      if (component) {
        component.getVueInstance().show();
      } else {
        TCIC.SDK.instance.loadComponent(componentName, {
          display: 'block',
          zIndex: 1502, // setting-component 1501，这个要比 setting-component 高一点
        });
      }
      this.hide();
    },

    // 更新语言
    updateLanguage() {
      this.currentLanguage = TCIC.SDK.instance.getLanguage();
      this.selectLanguage = this.currentLanguage;
      TCIC.SDK.instance.reportLog('setting-language', `[updateLanguage] ${this.selectLanguage}`);
    },
    // 语言切换
    onLanguageChange(lng) {
      if (lng === this.currentLanguage) {
        return;
      }
      TCIC.SDK.instance.reportLog('setting-language', `[onLanguageChange] ${lng}`);
      TCIC.SDK.instance.setLng(lng);
      TCIC.SDK.instance.reloadClass();
    },

    /**
     * 检测摄像头
     */
    async enumerateCamera(init = false, reason) {
      const { camera } = this.detection;
      TCIC.SDK.instance.reportLog(
        'setting-device-detect',
        `[enumerateCamera] init: ${init}, reason: ${reason}, enumerating: ${camera.enumerating}`,
      );
      if (camera.enumerating) {
        return;
      }
      camera.enumerating = true;
      try {
        this.cameras = await DetectUtil.getCameras();
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateCamera] [succ] length: ${this.cameras?.length}, ${JSON.stringify(this.cameras)}, this.camera ${this.camera?.deviceId}`,
        );
      } catch (err) {
        this.cameras = [];
        console.error(err);
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateCamera] [error] name: ${err.name}, message: ${err.message}`,
        );
      }
      camera.enumerating = false;
      if (Util.isArray(this.cameras) && this.cameras.length) {
        if (init && this.pendingData['camera-available']) {
          const { deviceId } = this.pendingData['camera-available'];
          const device = this.cameras?.find(item => item.deviceId === deviceId);
          if (device) {
            TCIC.SDK.instance.reportLog(
              'setting-device-detect',
              `[enumerateCamera] use camera from pendingData, deviceId ${deviceId}, deviceName ${device.deviceName}`,
            );
            this.camera = device;
          }
        }
        if (!this.camera) {
          DetectUtil.getCameraDeviceId().then((deviceId) => {
            const lastDeviceId = localStorage.getItem('cameraId');
            const lastDevice = this.cameras?.find(item => item.deviceId === lastDeviceId);
            if (lastDevice) {
              TCIC.SDK.instance.reportLog(
                'setting-device-detect',
                `[enumerateCamera] use camera from localStorage, deviceId ${lastDevice.deviceId}, deviceName ${lastDevice.deviceName}`,
              );
              this.camera = lastDevice;
            } else {
              const device = this.cameras?.find(item => item.deviceId === deviceId) || this.cameras?.[0];
              TCIC.SDK.instance.reportLog(
                'setting-device-detect',
                `[enumerateCamera] use camera, deviceId ${device?.deviceId}, deviceName ${device?.deviceName}`,
              );
              this.camera = device;
            }
            if (this.camera.deviceId !== deviceId) {
              this.onCameraChange(this.camera, init, `enumerateCamera success and device change, ${deviceId} -> ${this.camera.deviceId}`);
            }
            // 更新相机tips状态
            const camStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
            this.detection.camera.available = camStatus > 0;
          });
        } else {
          // 更新相机tips状态
          const camStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
          this.detection.camera.available = camStatus > 0;
        }
      } else {
        if (this.camera) {
          this.camera = null;
        }
        camera.available = false;
      }
    },

    /**
     * 检测麦克风
     */
    async enumerateMicrophone(init = false, reason) {
      const { microphone } = this.detection;
      TCIC.SDK.instance.reportLog(
        'setting-device-detect',
        `[enumerateMicrophone] init: ${init}, reason: ${reason}, enumerating: ${microphone.enumerating}`,
      );
      if (microphone.enumerating) {
        return;
      }
      microphone.enumerating = true;
      try {
        this.microphones = await DetectUtil.getMicrophones();
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateMicrophone] [succ] length ${this.microphones.length}, ${JSON.stringify(this.microphones)}, this.microphone ${this.microphone?.deviceId}`,
        );
        if (this.microphones) {
          if (this.microphones.length) {
            const currentDvcId = await DetectUtil.getMicDeviceId();
            const currentDvc = this.microphones.find(ele => ele.deviceId == currentDvcId);
            if (currentDvc) {
              this.microphone = currentDvc;
            }
          };
        };
        if (!this.microphone) {
          this.microphone = this.microphones?.[0];
        };
      } catch (err) {
        this.microphones = [];
        console.error(err);
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateMicrophone] [error] name: ${err.name}, message: ${err.message}`,
        );
      }
      microphone.enumerating = false;
      if (Util.isArray(this.microphones) && this.microphones.length) {
        // 为什么这里就设为true了？
        // microphone.available = true;
        if (init && this.pendingData['mic-available']) {
          const { deviceId } = this.pendingData['mic-available'];
          const device = this.microphones?.find(item => item.deviceId === deviceId);
          if (device) {
            TCIC.SDK.instance.reportLog(
              'setting-device-detect',
              `[enumerateMicrophone] use microphone from pendingData, deviceId ${deviceId}, deviceName ${device.deviceName}`,
            );
            this.microphone = device;
          }
        }
        if (!this.microphone) {
          DetectUtil.getMicDeviceId().then((deviceId) => {
            const lastDeviceId = localStorage.getItem('micId');
            const lastDevice = this.microphones?.find(item => item.deviceId === lastDeviceId);
            if (lastDevice) {
              TCIC.SDK.instance.reportLog(
                'setting-device-detect',
                `[enumerateMicrophone] use microphone from localStorage, deviceId ${lastDevice.deviceId}, deviceName ${lastDevice.deviceName}`,
              );
              this.microphone = lastDevice;
            } else {
              const device = this.microphones?.find(item => item.deviceId === deviceId) || this.microphones?.[0];
              TCIC.SDK.instance.reportLog(
                'setting-device-detect',
                `[enumerateMicrophone] use microphone, deviceId ${device?.deviceId}, deviceName ${device?.deviceName}`,
              );
              this.microphone = device;
            }
            if (this.microphone.deviceId !== deviceId) {
              this.onMicrophoneChange(this.microphone, init, `enumerateMicrophone success and device change, ${deviceId} -> ${this.microphone.deviceId}`);
            }
            // 更新麦克风tips状态
            const astatus = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
            this.detection.microphone.available = astatus > 0;
          });
        } else {
          const astatus = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
          this.detection.microphone.available = astatus > 0;
        }
        this.detectMicrophoneVolume('mic succ');
      } else {
        if (this.microphone) {
          this.microphone = null;
        }
        microphone.available = false;
      }
      return this.microphones;
    },

    /**
     * 检测扬声器
     */
    async enumerateSpeaker(init = false, reason) {
      const { speaker } = this.detection;
      TCIC.SDK.instance.reportLog(
        'setting-device-detect',
        `[enumerateSpeaker] init: ${init}, reason: ${reason}, enumerating: ${speaker.enumerating}`,
      );
      if (speaker.enumerating) {
        return;
      }
      speaker.enumerating = true;
      speaker.available = null;

      if (TCIC.SDK.instance.isMobileNative()) {
        // TODO 这个为什么单独处理？
        speaker.enumerating = false;
        try {
          this.detection.speaker.volume = await DetectUtil.getSpeakerVolume();
          this.detection.speaker.available = true;
          this.result.speaker = {
            available: true,
            reason: null,
          };
          TCIC.SDK.instance.reportLog(
            'setting-device-detect',
            `[enumerateSpeaker] [succ] isMobileNative, volume ${speaker.volume}`,
          );
        } catch (error) {
          console.error('getSpeakerVolume', error);
          this.$message.error(i18next.t('获取扬声器音频失败，请重试'));
          this.detection.speaker.available = false;
          this.result.speaker = {
            available: false,
            reason: null,
          };
          TCIC.SDK.instance.reportLog(
            'setting-device-detect',
            `[enumerateSpeaker] [error] isMobileNative, name: ${error.name}, message: ${error.message}`,
          );
        }
        return;
      }

      let error;
      try {
        this.speakers = await DetectUtil.getSpeakers();
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateSpeaker] [succ] length ${this.speakers.length}, ${JSON.stringify(this.speakers)}, this.speaker ${this.speaker?.deviceId}`,
        );
        if (this.speakers) {
          if (this.speakers.length) {
            const currentDvcId = await DetectUtil.getSpeakerDeviceId();
            const currentDvc = this.speakers.find(ele => ele.deviceId == currentDvcId);
            if (currentDvc) {
              this.speaker = currentDvc;
              speaker.available = true;
            }
          }
        }
        if (!this.speaker) {
          this.speaker = this.speakers?.[0];
          speaker.available = true;
        };
      } catch (err) {
        this.speakers = [];
        error = err;
        TCIC.SDK.instance.reportLog(
          'setting-device-detect',
          `[enumerateSpeaker] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      speaker.enumerating = false;
      if (Util.isArray(this.speakers) && this.speakers.length) {
        if (init && this.pendingData['speaker-available']) {
          const { deviceId } = this.pendingData['speaker-available'];
          const currentUsedDvcId = await DetectUtil.getSpeakerDeviceId();
          let dvcId;
          if (currentUsedDvcId) {
            dvcId = currentUsedDvcId;
          } else if (deviceId) {
            dvcId = deviceId;
          }
          const device = this.speakers?.find(item => item.deviceId === dvcId);
          if (device) {
            TCIC.SDK.instance.reportLog(
              'setting-device-detect',
              `[enumerateSpeaker] use speaker from pendingData, deviceId ${dvcId}, deviceName ${device.deviceName}`,
            );
            this.speaker = device;
          }
        }
        if (!this.speaker) {
          DetectUtil.getSpeakerDeviceId().then((deviceId) => {
            const device = this.speakers?.find(item => item.deviceId === deviceId) || this.speakers?.[0];
            TCIC.SDK.instance.reportLog(
              'setting-device-detect',
              `[enumerateSpeaker] use speaker, deviceId ${device?.deviceId}, deviceName ${device?.deviceName}`,
            );
            this.speaker = device;
            if (!init && this.$refs['speaker-select']) {
              this.$refs['speaker-select'].$emit('change');
            }
          });
        }
        this.detection.speaker.available = true;
        console.log('获取扬声器成功');
        this.result.speaker = {
          available: true,
          reason: null,
        };
        this.detectSpeakerVolume();
      } else {
        if (this.speaker) {
          this.stopDetectSpeaker();
          this.speaker = null;
        }
        speaker.available = false;
        this.result.speaker = {
          available: false,
          reason: error ? (`${error.name || ''} : ${error.message || ''}` || error) : null,
        };
      }
    },

    needHandleDeviceEvent(type) {
      let res = false;
      switch (type) {
        case TCIC.TTrtcDeviceType.Mic:
          res = this.tabMap.audio;
          break;
        case TCIC.TTrtcDeviceType.Speaker:
          res = true;
          break;
        case TCIC.TTrtcDeviceType.Camera:
          res = this.tabMap.video;
          break;
      }
      return res;
    },

    /*
    * 设备新增
    * 如果当前没有此类型设备：
    *   1.设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceAddHandler({ deviceId, type }) {
      // if (!this.needHandleDeviceEvent(type)) {
      //   console.log('::setting-device-detect, ignore device add, type', type);
      //   return;
      // }
      const {
        Mic,
        Camera,
        Speaker,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          this.microphone = null;
          console.log('::setting-device-detect, deviceChange | deviceAddHandler | mic => ', deviceId);
          await this.enumerateMicrophone(false, `add device ${deviceId}`);
          break;
        case Camera:
          console.log('::setting-device-detect, deviceChange | deviceAddHandler | camera => ', deviceId);
          await this.enumerateCamera(false, `add device ${deviceId}`);
          break;
        case Speaker:
          console.log('::device-detect, deviceChange | deviceAddHandler | speaker => ', deviceId);
          this.speaker = null;
          // 如果当前有设备，只重新枚举设备
          await this.enumerateSpeaker(false, `add device ${deviceId}`);
          break;
      }
    },

    /*
    * 设备移除
    * 如果是当前设备：
    *   1.先复位当前选中设备 2.重新设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceRemoveHandler({ deviceId, type }) {
      // if (!this.needHandleDeviceEvent(type)) {
      //   console.log('::setting-device-detect, ignore device remove, type', type);
      //   return;
      // }
      const {
        Mic,
        Camera,
        Speaker,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          this.microphone = null;
          this.enumerateMicrophone(false, `remove device ${deviceId}`);
          await this.enumerateMicrophone(false, `remove device ${deviceId}`);
          break;
        case Camera:
          console.log('::setting-device-detect, deviceChange | deviceRemoveHandler | camera => ', deviceId);
          if (this.camera?.deviceId === deviceId) {
            console.warn(`::setting-device-detect, remove current camera ${this.camera.deviceId} ${this.camera.deviceName}`);
            this.camera = null;
          }
          await this.enumerateCamera(false, `remove device ${deviceId}`);
          break;
        case Speaker:
          console.log('::device-detect, deviceChange | deviceRemoveHandler | speaker => ', deviceId);
          if (this.speaker?.deviceId === deviceId) {
            console.warn(`::device-detect, remove current speaker ${this.speaker.deviceId} ${this.speaker.deviceName}`);
            this.stopDetectSpeaker();
          }
          if (this.audioPlayStatus) {
            this.toggleAudioPlay();
          }
          this.speaker = null;
          await this.enumerateSpeaker(false, `remove device ${deviceId}`);
          break;
      }
    },

    // TODO 这个什么情况下会触发？Mic分支没处理？
    async deviceUpdateHandler({ deviceId, type }) {
      if (!this.needHandleDeviceEvent(type)) {
        console.log('::setting-device-detect, ignore device update, type', type);
        return;
      }
      const {
        Mic,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          console.log('::setting-device-detect, deviceChange | deviceUpdateHandler | mic => ', deviceId, 'do nothing');
          break;
        case Camera:
          console.log('::setting-device-detect, deviceChange | deviceUpdateHandler | camera => ', deviceId);
          try {
            const devices = await DetectUtil.getCameras();
            const deviceId = await DetectUtil.getCameraDeviceId();
            this.camera = devices.find(item => item.deviceId === deviceId);
          } catch (err) {
            console.error(err);
          }
          break;
      }
    },

    onAudioMode(audioMode) {
      this.pureAudioMode = !!audioMode;
    },
    switchVideoModel() {
      if (!this.isClassStarted) {
        window.showToast(i18next.t('开始{{arg_0}}后才能切换至音频{{arg_0}}', { arg_0: this.roomInfo.name }));
        this.pureAudioMode = false;
        return;
      }
      if (this.pureAudioMode) {
        TCIC.SDK.instance.setState(Constant.TStateAudioMode, true);
      } else {
        TCIC.SDK.instance.setState(Constant.TStateAudioMode, false);
        TCIC.SDK.instance.setState(Constant.TStateNetWorkTipsAudio, false);
      }
      // 模拟点击，隐藏当前打开的菜单项
      setTimeout(() => {
        document.dispatchEvent(new Event('reload-header'));
      }, 50);
    },
    trigger() {
      clearTimeout(this.popoverTask);
      this.showPureAudioTips = true;
      this.popoverTask = setTimeout(() => {
        this.showPureAudioTips = false;
      }, 4000);
    },
    switchShowJoinExit(value) {
      console.log('=====switchShowJoinExit=====', value);
      TCIC.SDK.instance.setState(TCIC.TMainState.Join_Quit_Tips, value);
      store.set('showJoinExit', value);
    },
    switchShowHandsUp(value) {
      console.log('=====switchShowHandsUp=====', value);
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, value);
      store.set('showHandsUp', value);
    },
  },
};
</script>

<style lang="less">
@setting-component-item-height: var(--setting-component-item-height, 40px);
@setting-component-item-height-small-screen: var(--setting-component-item-height-small-screen, 40px);

@setting-component-item-font-size: var(--setting-component-item-font-size, 16px);
@setting-component-item-font-size-small-screen: var(--setting-component-item-font-size, 16px);

@primary-color: #006EFF;
@main-text-color: #fff;
@sub-text-color: #999;
@error-text-color: #D95A53;
@warning-text-color: #FF7200;

.setting-component {
  .header-component-popover & {
    position: fixed;
    max-width: 100vw;
    width: 398px;
    right: 0;
    top: 64px;
    bottom: 0;
    transform-origin: right top;
  }

  .setting__tabs.no-header {
    .el-tabs__header {
      display: none;
    }
  }

  .el-form-item__label {
    font-size: @setting-component-item-font-size;
    line-height: @setting-component-item-height;
  }

  .el-form-item__content {
    .el-select>.el-input {
      border: none;
      border-radius: 4px;
      background: rgba(#000, 0.15);

      .el-input__inner {
        display: -webkit-box;
        height: @setting-component-item-height;
        overflow: hidden;
        font-size: @setting-component-item-font-size;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 1; //显示多少行
        -webkit-box-orient: vertical;
      }

      .el-select__caret {
        font-size: @setting-component-item-font-size;
        font-weight: bold;
      }

      .el-input__suffix {
        right: 16px;
      }
    }
  }

  .setting__item {

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-form-item,
    .el-form-item__content,
    .el-select {
      position: relative;
      width: 100%;

      /** 修复移动端无法显示下拉选项问题 */
      .el-select-dropdown {
        position: absolute !important;
        top: 25px !important;
        left: 0px !important;
      }

      .el-input__inner {
        padding-right: 58px;
      }
    }

    .ic-setting-camera {
      background: url('../header-component/assets/ic_setting_camera.svg') 14px center no-repeat;
    }

    .ic_setting_microphone {
      background: url('../header-component/assets/ic_setting_microphone.svg') 14px center no-repeat;
    }

  }

  .setting-simple-item {
    margin-bottom: 20px;

    &.clickable {
      cursor: pointer;
    }

    .setting-item-label {
      font-size: @setting-component-item-font-size;
      line-height: @setting-component-item-height;
      font-weight: 400;
      color: #FFFFFF;
    }

    .setting-item-label-icon {
      cursor: pointer;
      display: inline-block;
      margin: 4px 8px 8px 4px;
      width: 16px;
      height: 16px;
      background-repeat: no-repeat;
      background-position: center;
      vertical-align: middle;
      background-image: url('../header-component/assets/ic_information_normal.svg');
    }

    .setting-item-label-beta {
      display: inline-flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 0 8px;
      margin-left: 8px;
      background-color: #E54545;
      border-radius: 10px;

      span {
        font-size: 12px;
        line-height: 20px;
        color: #FFFFFF;
      }
    }

    .setting-item-content {
      font-size: @setting-component-item-font-size;
      line-height: @setting-component-item-height;
      font-weight: 400;
      color: #FFFFFF;
    }
  }

  .setting-beauty {
    font-size: @setting-component-item-font-size;

    >span {
      font-size: 16px;
      line-height: 24px;
      cursor: pointer;
    }
  }

  .switch-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-switch {
      &.switch {
        height: 24px;

        .el-switch__label {
          color: #fff;

          &.is-active {
            color: #fff;
          }

          span {
            font-size: 16px;
          }
        }

        .el-switch__core {
          border-width: 2px;
          width: 40px !important;
          height: 22px !important;
          border-color: #FFFFFF !important;
          background-color: #FFFFFF !important;

          &:after {
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: rgba(0, 0, 0, 0.14);
          }
        }

        &.is-checked .el-switch__core {
          border-color: #006EFF !important;
          background-color: #006EFF !important;

          &:after {
            left: 36px;
            background-color: #FFFFFF;
          }
        }
      }
    }
  }

  .select-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .setting-item-label {
      flex: 1;
    }

    .el-select {
      width: 120px;
    }
  }

  &.small-screen {
    position: absolute;
    right: 0;
    top: 0;
    padding: 20px 0 20px;
    padding: constant(safe-area-inset-top) 0 constant(safe-area-inset-bottom);
    padding: env(safe-area-inset-top) 0 env(safe-area-inset-bottom);

    .setting__tabs {
      height: 100%;

      .el-tabs__content {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }

    .el-form-item__label {
      font-size: @setting-component-item-font-size-small-screen;
      line-height: @setting-component-item-height-small-screen;
    }

    .setting-simple-item .setting-item-label {
      font-size: @setting-component-item-font-size-small-screen;
    }

    .el-form-item__content {
      .el-select>.el-input {
        border-radius: 4px;

        .el-input__inner {
          height: @setting-component-item-height-small-screen;
          font-size: @setting-component-item-font-size-small-screen;
        }

        .el-select__caret {
          font-size: @setting-component-item-font-size-small-screen;
        }

        .el-input__suffix {
          right: 16px;
        }
      }
    }

    @media screen and (orientation: portrait) {
      & {
        width: 100vw;
      }
    }
  }
}

.mobile-scrollbar {
  height: 80%;
  overflow-y: scroll;
}

.mobile-scrollbar::-webkit-scrollbar {
  display: none;
}

.pure-audio-tooltip {
  width: 220px;
}

.setting__tabs {
  padding: 0 20px;

  .el-tabs__item {
    padding: 0 8px;
    color: #969EB4;

    &.is-active {
      color: #fff;
    }
  }

  .el-tabs__nav-wrap:after {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .el-tabs__active-bar {
    background: #fff;
    transition-duration: 0.15s;
  }

  .el-form-item__label {
    color: #fff;
  }

  .el-select-dropdown.el-popper {
    margin-top: 4px !important;
  }

  .el-tabs__content {
    overflow: visible;
  }

  .el-form-item__label {
    font-size: 1em;
  }
}

.setting__section {
  margin-bottom: 24px;
}

.setting__camera-select,
.setting__mic-select {
  .el-input__inner {
    padding-left: 50px;
  }
}

.setting__section-title {
  color: #969FB4;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 16px;
}

.setting-detect {
  padding-bottom: 16px;
  color: #515a6e;

  .label {
    padding-bottom: 10px;
  }

  .section-item {
    display: flex;
    align-items: center;

    .is-error>.el-input {
      border: @error-text-color 1px solid;
    }

    .is-warning>.el-input {
      border: @warning-text-color 1px solid;
    }
  }

  .volume-test {
    .volume-scrollbar {
      width: 320px;
      align-items: center;

      .speaker-icon {
        height: 16px;
      }

      .capacity {
        margin-right: 12px;
      }

      .el-button {
        padding: 0;
        color: @primary-color;

        &:hover {
          border-color: transparent;
        }
      }
    }
  }

  .detect-preview__video {
    position: absolute;
    height: auto !important;
    width: 100%;
    min-height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 0;
    overflow: hidden;
    background: #000 no-repeat center center / 160px 160px url('../device-detect-component/assets/camera.png');
  }

  .base-icon {
    min-width: 16px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
  }

  .success-icon {
    background: no-repeat center center / 100% auto url('../device-detect-component/assets/icon-success-new.svg');
  }

  .warning-icon {
    background: no-repeat center center / 100% auto url('../device-detect-component/assets/icon-warning-new.svg');
  }

  .hint-icon {
    background: no-repeat center center / 100% auto url('../device-detect-component/assets/icon-hint-new.svg');
  }

  .mute-icon {
    background: no-repeat center center / 100% auto url('../device-detect-component/assets/mute.svg');
  }

  .volume {
    display: flex;
    flex-wrap: nowrap;
    padding-top: 13px;
    height: 29px;

    &.disabled {
      opacity: .4;
    }

    .mic-icon {
      width: 16px;
      background: no-repeat center center / 100% auto url('../device-detect-component/assets/microphone-outline.png');
    }

    .mic-volume-value {
      width: 35px;
      line-height: 16px;
      font-weight: bolder;
      color: @main-text-color;
      text-align: center;
    }

    .speaker-icon {
      cursor: pointer;
      width: 16px;
      background: no-repeat center center / 100% auto url('../device-detect-component/assets/speaker-outline.png');

      &.disable {
        background: no-repeat center center / 100% auto url('../device-detect-component/assets/speaker-off.png');
      }
    }

    .capacity {
      position: relative;
      flex-grow: 1;
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      padding: 0 4px 0 13px;
      margin-right: 37px;

      .slider-wrapper {
        display: block;
        width: 100%;
        height: 100%;
      }

      .item {
        width: 4px;
        height: 100%;
        border-radius: 2px;
        background: #D8D8D8;
        justify-content: space-between;

        &.active {
          background: @primary-color;
        }
      }
    }
  }
}

@import "../device-detect-component/theme/college-web.less";
@import "../device-detect-component/theme/college-electron.less";
</style>
