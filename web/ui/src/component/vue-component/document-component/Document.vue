<template>
  <MixedPopper
    ref="popover"
    type="drawer"
    :visible.sync="localComponent.active"
    :wrapper-closable="false"
    drawer-btt-height="calc(100% - 45px)"
    drawer-custom-class="document-drawer inComponent"
    :drawer-rtl-width="isSmallScreen ? '100vmin' : '500px'"
    @drawer-open="handleDrawerOpen"
    @drawer-close="handleDrawerClose"
  >
    <template #title>
      <div
        v-if="isInAddPage"
        class="course-ware-back"
      >
        <a
          href="javascript:;"
          @click="changeContent('list')"
        >
          <img src="./assets/ic_return.svg">
          <span>{{ $t('返回') }}</span>
        </a>
      </div>
      <span>{{ isInAddPage ? translateTip.addInfo : roomInfo.courseware }}</span>
    </template>
    <div
      ref="xl-document"
      :class="['document-component', { 'small-screen': isSmallScreen }]"
    >
      <div :class="!isInAddPage ? 'course-ware-header' : ' course-add-header'">
        <!-- 展示 -->
        <template v-if="!isInAddPage">
          <div class="course-ware-form">
            <el-button
              v-if="documentAddable"
              size="small"
              type="primary"
              class="add-course"
              @click="changeContent('add')"
            >
              <i class="icon-plus-circle" />
              <span>{{ translateTip.addInfo }}</span>
            </el-button>
            <el-input
              v-model.trim="searchName"
              class="course-ware-input"
              :placeholder="translateTip.searchDocument"
              prefix-icon="el-icon-search"
              @input="searchDocument"
            />
          </div>
        </template>
      </div>
      <div class="course-ware-main">
        <!-- 展示 -->
        <div
          v-show="!isInAddPage"
          class="course-main-list_box"
        >
          <div class="course-main-tab">
            <div
              :class="['course-tab-item', {'course-tab-item_active': currentTab === 0, 'disable': currentTab > 9}]"
              @click="handleTab(0)"
            >
              {{ translateTip.roomInfo }}({{ totalClass }})
            </div>
            <div
              v-if="!isHidePrivateDocument"
              :class="['course-tab-item', {'course-tab-item_active': currentTab === 1, 'disable': currentTab > 9}]"
              @click="handleTab(1)"
            >
              {{ translateTip.privateInfo }}({{ totalPrivate }})
            </div>
            <div
              :class="['course-tab-item', {'course-tab-item_active': currentTab === 2, 'disable': currentTab > 9}]"
              @click="handleTab(2)"
            >
              {{ translateTip.publicInfo }}({{ totalPublic }})
            </div>
            <div
              v-if="customTab?.name"
              :class="['course-tab-item', 'custom-tab-item', {'course-tab-item_active': currentTab === 3, 'disable': currentTab > 9}]"
              @click="handleTab(3)"
            >
              {{ customTab.name }}
            </div>
          </div>
          <div
            v-if="currentTab === 3"
            class="course-main-content-wrap"
          >
            <Component
              :is="customTab.element"
              :current-tab="currentTab"
              @add-H5-courseware="(evt) => submitOnlineCourse(evt.detail[0])"
            />
          </div>
          <div
            v-else
            class="course-main-content-wrap"
          >
            <!-- tab 内容 -->
            <div
              ref="listContent"
              class="course-main-content"
            >
              <!-- 班级课件 -->
              <template v-if="currentTab === 0">
                <template v-if="courseWareClass.length > 0">
                  <div
                    v-for="(item, index) in courseWareClass"
                    :key="item.docId"
                    :class="['course-con-list', {'forbid' : item.forbid || item.transcodeState === 1 || item.transcodeState === 2
                                                 , 'last-child': index === courseWareClass.length - 1}]"
                    @click="useDocument(item)"
                  >
                    <div class="course-con-list_info">
                      <div class="course-list_left">
                        <div class="information">
                          <i :class="['icon', `icon_${item.type}`]" />
                          <div
                            class="name"
                            :title="item.docName"
                          >
                            {{ item.docName }}
                          </div>
                        </div>
                        <div class="status">
                          <!-- <span v-if="item.transcodeState === 0" class="status_default">无需转码</span> -->
                          <span
                            v-if="item.transcodeState === 1"
                            class="status_doing"
                          >{{ $t('上传中...') }}（{{ item.transcodeProgress }}%）</span>
                          <el-tooltip
                            v-else-if="item.transcodeState === 2"
                            effect="dark"
                            :content="getTranscodeError(item)"
                            placement="bottom-start"
                          >
                            <span class="status_error">{{ $t('上传失败') }}<i class="el-icon-warning " /></span>
                          </el-tooltip>
                        <!-- <span v-if="item.transcodeState === 3" class="status_success">转码成功</span> -->
                        </div>
                      </div>
                      <div
                        v-if="!isClassStarted"
                        class="course-list_operate"
                        @click.stop="unbindDocument(item.docId)"
                      >
                        <img
                          class="operate_wrap_del"
                          src="./assets/delete.svg"
                        >
                      </div>
                    </div>
                  </div>
                </template>
                <div
                  v-else
                  class="emptyContent"
                >
                  <span>{{ translateTip.emptyContent }}</span>
                </div>
              </template>
              <!-- 私有课件 -->
              <template v-else-if="currentTab === 1">
                <template v-if="courseWarePrivate.length > 0">
                  <div
                    v-for="(item, index) in courseWarePrivate"
                    :key="item.docId"
                    :class="['course-con-list', {'forbid' : item.forbid || item.transcodeState === 1 || item.transcodeState === 2
                                                 , 'last-child': index === courseWarePrivate.length - 1}]"
                    @click="useDocument(item)"
                  >
                    <div class="course-con-list_info">
                      <div class="course-list_left">
                        <div class="information">
                          <i :class="['icon', `icon_${item.type}`]" />
                          <div
                            class="name"
                            :title="item.docName"
                          >
                            {{ item.docName }}
                          </div>
                        </div>
                        <div class="status">
                          <!-- <span v-if="item.transcodeState === 0" class="status_default">无需转码</span> -->
                          <span
                            v-if="item.transcodeState === 1"
                            class="status_doing"
                          >{{ $t('上传中...') }}（{{ item.transcodeProgress }}%）</span>
                          <el-tooltip
                            v-else-if="item.transcodeState === 2"
                            effect="dark"
                            :content="getTranscodeError(item)"
                            placement="bottom-start"
                          >
                            <span class="status_error">{{ $t('上传失败') }}<i class="el-icon-warning " /></span>
                          </el-tooltip>
                        </div>
                      </div>
                      <div class="course-list_operate">
                        <img
                          class="operate_wrap_del"
                          src="./assets/delete.svg"
                          @click.stop="deleteDocument(item.docId)"
                        >
                      </div>
                    </div>
                  </div>
                </template>
                <div
                  v-else
                  class="emptyContent"
                >
                  <span>{{ translateTip.uploadNotInfo }}</span>
                </div>
              </template>
              <!-- 公有课件 -->
              <template v-else-if="currentTab === 2">
                <template v-if="courseWarePublic.length > 0">
                  <div
                    v-for="(item, index) in courseWarePublic"
                    :key="item.docId"
                    :class="['course-con-list', {'forbid' : item.forbid || item.transcodeState === 1 || item.transcodeState === 2
                                                 , 'last-child': index === courseWarePublic.length - 1}]"
                    @click="useDocument(item)"
                  >
                    <div class="course-con-list_info">
                      <div class="course-list_left">
                        <div class="information">
                          <i :class="['icon', `icon_${item.type}`]" />
                          <div
                            class="name"
                            :title="item.docName"
                          >
                            {{ item.docName }}
                          </div>
                        </div>
                        <div class="status">
                          <!-- <span v-if="item.transcodeState === 0" class="status_default">无需转码</span> -->
                          <span
                            v-if="item.transcodeState === 1"
                            class="status_doing"
                          >{{ $t('上传中...') }}（{{ item.transcodeProgress }}%）</span>
                          <el-tooltip
                            v-else-if="item.transcodeState === 2"
                            effect="dark"
                            :content="getTranscodeError(item)"
                            placement="bottom-start"
                          >
                            <span class="status_error">{{ $t('上传失败') }}<i class="el-icon-warning " /></span>
                          </el-tooltip>
                        <!-- <span v-if="item.transcodeState === 3" class="status_success">转码成功</span> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <div
                  v-else
                  class="emptyContent"
                >
                  <span>{{ translateTip.uploadInfo }}</span>
                </div>
              </template>
              <!-- 搜索结果 -->
              <template v-else>
                <template v-if="courseWareSearch.length > 0">
                  <div
                    v-for="(item, index) in courseWareSearch"
                    :key="item.docId"
                    :class="['course-con-list', {'forbid' : item.forbid || item.transcodeState === 1 || item.transcodeState === 2
                                                 , 'last-child': index === courseWareSearch.length - 1}]"
                    @click="useDocument(item)"
                  >
                    <div class="course-con-list_info">
                      <div class="course-list_left">
                        <div class="information">
                          <i :class="['icon', `icon_${item.type}`]" />
                          <div
                            class="name"
                            :title="item.docName"
                          >
                            {{ item.docName }}
                          </div>
                        </div>
                        <div class="status">
                          <!-- <span v-if="item.transcodeState === 0" class="status_default">无需转码</span> -->
                          <span
                            v-if="item.transcodeState === 1"
                            class="status_doing"
                          >{{ $t('上传中...') }}（{{ item.transcodeProgress }}%）</span>
                          <el-tooltip
                            v-else-if="item.transcodeState === 2"
                            effect="dark"
                            :content="getTranscodeError(item)"
                            placement="bottom-start"
                          >
                            <span class="status_error"><i class="el-icon-warning " />{{ $t('上传失败') }}</span>
                          </el-tooltip>
                        <!-- <span v-if="item.transcodeState === 3" class="status_success">转码成功</span> -->
                        </div>
                      </div>
                      <div
                        v-if="!isClassStarted"
                        class="course-list_operate"
                      >
                        <div
                          class="course-list_more"
                          @click.stop="handleOperate('courseWareSearch', index)"
                        >
                          <img src="./assets/icon_more.png">
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <div
                  v-else
                  class="emptyContent"
                >
                  <span>{{ translateTip.uploadNotContent }}</span>
                </div>
              </template>
            </div>
            <div class="course-main-footer">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="totalNumber"
                :page-size="pageSize"
                :current-page.sync="currentPage"
                :pager-count="isSmallScreen ? 5 : 7"
                :small="isSmallScreen && isPortrait"
                @current-change="currentPageChange"
              />
            </div>
          </div>
        </div>
        <!-- 添加流程 -->
        <div
          v-show="isInAddPage"
          class="course-main-add_box"
        >
          <div class="course-main-type">
            <div class="course-add-title">
              {{ translateTip.selectContent }}
            </div>
            <el-radio
              v-if="!isHidePrivateDocument"
              v-model="courseType"
              :label="0"
            >
              {{ translateTip.privateInfo }}
            </el-radio>
            <el-radio
              v-model="courseType"
              :label="1"
            >
              {{ translateTip.publicInfo }}
            </el-radio>
            <el-radio
              v-model="courseType"
              :label="2"
            >
              {{ translateTip.onlineInfo }}
            </el-radio>
          </div>
          <div
            v-if="courseType === 2"
            class="course-online-wrapper"
          >
            <div class="course-online-title">
              <el-input
                v-model.trim="onlineCourseTitle"
                class="course-ware-input"
                :placeholder="translateTip.titleInfo"
              />
            </div>
            <div class="course-online-url">
              <el-input
                v-model.trim="onlineCourseUrl"
                :class="['course-ware-input', !onlineCourseUrlVerify && onlineCourseUrl !== '' ? 'warning' : '']"
                :placeholder="translateTip.urlInfo"
              />
              <p :class="['course-ware-tip', !onlineCourseUrlVerify && onlineCourseUrl !== '' ? 'show' : '']">
                {{ $t('请输入正确的课件链接') }}
              </p>
            </div>
            <el-button
              size="small"
              type="primary"
              class="add-course"
              :disabled="!onlineCourseUrlVerify"
              @click="submitOnlineCourse()"
            >
              <span>{{ $t('确认') }}</span>
            </el-button>
          </div>
          <el-upload
            v-else
            class="upload-demo"
            drag
            :multiple="!isMiniProgramWebview"
            action="#"
            :accept="acceptExt"
            :auto-upload="false"
            :on-change="handleChange"
            :show-file-list="false"
          >
            <i class="el-icon-plus course-main-add_icon" />
            <div class="el-upload__text">
              <label>{{ isMobile ? $t('点击选择文件到此区域') : $t('点击选择或拖动文件到此区域') }}</label>
              <!-- <br>{{ translateTip.broswerContent }} -->
              <el-tooltip
                class="item"
                effect="dark"
                placement="bottom"
                popper-class="tooltip-upload-info"
              >
                <div class="ic-info-tip-wrap">
                  <i class="ic-info-tip" />
                </div>
                <div slot="content">
                  <div class="title">
                    {{ $t('文件格式大小说明') }}
                  </div>
                  <div class="item">
                    <label>{{ $t('文档格式类型（200MB以内）') }}</label>
                    <span>doc、ppt、pdf、xls、xlsx</span>
                  </div>
                  <div class="item">
                    <label>{{ $t('视频格式类型（500MB以内）') }}</label>
                    <span>{{ isWeb ? 'mp4、webm、ogg、mov' : 'mp4、3gp、mpg、mpeg、3g2、avi、flv、wmv、h264、m4v、mj2、mov、ogg、ogv、rm、qt、vob、webm' }}</span>
                  </div>
                  <div class="item">
                    <label>{{ $t('音频格式类型（50MB以内）') }}</label>
                    <span>{{ isWeb ? 'mp3、wav、ogg' : 'mp3、wav、wma、aac、flac、m4a、oga、opus' }}</span>
                  </div>
                  <div class="item">
                    <label>{{ $t('图片格式类型（100MB以内）') }}</label>
                    <span>png、jpg、jpeg、gif、bmp</span>
                  </div>
                  <div class="item">
                    <span>{{ translateTip.limitContent }}</span>
                  </div>
                  <div class="auto-handle">
                    <span>{{ $t('文件自动处理说明') }}</span>
                    <span>{{ $t('移除不支持的墨迹') }}</span>
                    <span>{{ $t('移除自动翻页') }}</span>
                    <span>{{ $t('移除损坏音视频') }}</span>
                  </div>
                </div>
              </el-tooltip>
            </div>
          </el-upload>
        </div>
      </div>
    </div>
    <button
      slot="reference"
      class="header__button button--secondary header__right-button"
      :class="{active: localComponent.active}"
      style="position: relative;"
    >
      <div class="tool-item-wrap">
        <i :class="!isSmallScreen ? `header__i i--menu icon-${localComponent.name}` : [`icon-${localComponent.name}`, {'mobile': isMobile}]" />
        <span class="header__btn-text">{{ roomInfo.courseware }}</span>
      </div>
    </button>
  </MixedPopper>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import DocumentUtil from '@/util/Document';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';
import Lodash from "lodash";

const TEduBoard = window.TEduBoard;

const TimeOutDuration = 4000;
export default {
  components: {
    MixedPopper,
  },
  extends: BaseComponent,
  props: {
    hideDocument: {
      type: Function,
      default: () => {},
    },
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      localComponent: this.component,
      currentContent: 'list', // list / add
      customTab: {
      },
      currentTab: 0,
      currentPage: 1,
      currentFile: null, // 正在上传的文件
      itemHeight: 50, // 注意和css部分保持一致
      pageSize: 10, // resize时候会自动调整
      totalPrivate: 0, // 私人课件数
      totalPublic: 0, // 公共课件数
      totalClass: 0,  // 本节课课件数
      totalSearch: 0, // 搜索结果
      courseWarePrivate: [],
      courseWarePublic: [],
      courseWareClass: [],
      courseWareSearch: [],
      searchName: '', // 搜索课件名称
      courseType: 2, // 课件类型 1_私人课件 2_公共课件
      courseFormat: 'normal', // 课件格式 normal_普通课件 online_在线课件
      isTabMore: false, // 是否点击过更多
      isPortrait: false,
      btnTabStatus: true, // 添加button是否可点击
      uploadStatus: false, // 上传状态
      taskId: '', // 上传文件id
      fileInfo: {}, // 上传的文件信息
      fileUrl: '', // 上传到cos的文件地址
      filePercent: 0, // 上传百分比
      uploadDocId: '', // 上传文件id
      documentAddable: true,
      acceptExt: '',
      loadTimeStamp: 0,
      timer: {
        private: null,
        public: null,
        class: null,
        search: null,
      },
      isClassStarted: false,    // 是否已经上课
      isHidePrivateDocument: false, // 是否屏蔽私人课件
      roleInfo: {},
      roomInfo: {},
      isMobile: false,
      currentDocInfo: null,
      isMiniProgramWebview: false,
      isWeb: false,
      isElectron: false,
      isUpload: false, // 是否上传成功
      cleanupResizeObserver: null,
      onlineCourseTitle: '', // 在线课件标题
      onlineCourseUrl: '', // 在线课件url
    };
  },

  computed: {
    isInAddPage() {
      return this.currentContent === 'add';
    },
    fileType() {
      let type;
      const index = this.fileInfo?.name?.lastIndexOf('.');
      const data = this.fileInfo?.name?.substring(index + 1, this.fileInfo?.name.length);
      if (data === 'jpg' || data === 'jpeg') {
        type = 'jpg';
      } else {
        type = data;
      }
      return type;
    },
    fileName() {
      return this.fileInfo?.name;
    },
    totalNumber() {
      if (this.currentTab === 0) {
        return this.totalClass;
      } if (this.currentTab === 1) {
        return this.totalPrivate;
      } if (this.currentTab === 2) {
        return this.totalPublic;
      }
      return this.totalSearch;
    },
    translateTip() {
      return {
        addInfo: i18next.t('添加{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        privateInfo: i18next.t('私有{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        publicInfo: i18next.t('公有{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        onlineInfo: i18next.t('在线{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        roomInfo: i18next.t('本{{room}}{{arg_0}}', { room: this.roomInfo.room, arg_0: this.roomInfo.courseware }),
        emptyContent: i18next.t('本{{room}}暂无{{arg_0}}，请先添加{{arg_0}}', { room: this.roomInfo.room, arg_0: this.roomInfo.courseware }),
        uploadNotInfo: i18next.t('暂无上传的{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        uploadNotContent: i18next.t('暂无符合上传{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        selectContent: i18next.t('类型'),
        uploadContent: i18next.t('2、上传{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        limitContent: i18next.t('单个{{arg_0}}不能超过2000页', { arg_0: this.roomInfo.courseware }),
        // broswerContent: i18next.t('（浏览器暂不支持音视频类{{arg_0}}，如有需要，可使用屏幕共享）', { arg_0: this.roomInfo.courseware }),
        searchDocument: i18next.t('搜索{{arg_0}}', { arg_0: this.roomInfo.courseware }),
        titleInfo: i18next.t('请输入{{arg_0}}标题（选填）', { arg_0: this.roomInfo.courseware }),
        urlInfo: i18next.t('请输入{{arg_0}}URL', { arg_0: this.roomInfo.courseware }),
      };
    },
    onlineCourseUrlVerify() {
      const regex = new RegExp('^https:\\/\\/' // 只允许 https 协议
        + '(([a-zA-Z0-9\\-]+\\.)+[a-zA-Z]{2,})' // 域名
        + '(\\/[-a-zA-Z0-9%_.~+]*)*' // 路径
        + '(\\?[;&a-zA-Z0-9%_.~+=-]*)?' // 可选的查询参数
        + '(\\#[-a-zA-Z0-9_]*)?$'); // 可选的片段标识符
      return regex.test(this.onlineCourseUrl);
    },
  },
  watch: {
    uploadStatus() {
      if (this.uploadStatus) {
        const file = {
          docName: this.currentFile.raw.name,
          transcodeState: 1,
          transcodeProgress: this.currentFile.percentage,
        };
        this.courseWareClass.unshift(file);
        this.totalClass += 1;
        if (this.courseType === 0) {
          this.courseWarePrivate.unshift(file);
          this.totalPrivate += 1;
          this.currentTab = 1;
        } else if (this.courseType === 1) {
          this.courseWarePublic.unshift(file);
          this.totalPublic += 1;
          this.currentTab = 2;
        }
      } else {
        this.getCurrentTabDocumentList();
      }
    },
  },
  beforeMount() {
    this.customTab = TCIC.SDK.instance.getState(Constant.TStateDocCustom, {}).customTab;
  },
  mounted() {
    const { roomInfo, roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isMiniProgramWebview = TCIC.SDK.instance.isMiniProgramWebview();
    this.documentAddable = TCIC.SDK.instance.isElectron() || TCIC.SDK.instance.isWeb() || TCIC.SDK.instance.isMobile();
    this.isWeb = TCIC.SDK.instance.isWeb();
    this.isElectron = TCIC.SDK.instance.isElectron();
    this.getAllTabsDocumentList();
    // @TODO 文档格式处理关于格式部分需要收敛到统一的方法里，否则页面多处配置，存在诸如支持上传不支持使用的矛盾的风险。
    // eslint-disable-next-line max-len
    this.acceptExt = '.ppt,.ppsx,.pptx,.doc,.docx,.pdf,.jpg,.jpeg,.png,.gif,.bmp,.mp3,.wav,.wma,.aac,.flac,.m4a,.oga,.opus,.mp4,.3gp,.mpg,.mpeg,.3g2,.avi,.flv,.wmv,.h264,.m4v,.mj2,.mov,.ogg,.rm,.qt,.vob,.webm,.f4v,.rmvb,'
    // eslint-disable-next-line max-len
      + '.PPT,.PPSX,.PPTX,.DOC,.DOCX,.PDF,.JPG,.JPEG,.PNG,.GIF,.BMP,.MP3,.WAV,.WMA,.AAC,.FLAC,.M4A,.OGA,.OPUS,.MP4,.3GP,.MPG,.MPEG,.3G2,.AVI,.FLV,.WMV,.H264,.M4V,.MJ2,.MOV,.OGG,.RM,.QT,.VOB,.WEBM,.F4V,.RMVB,'
      + '.xls,.xlt,.et,.ett,.xlsx,.xltx,.csv,.xlsb,.xlsm,.xltm,.ets,'
      + '.XLS,.XLT,.ET,.ETT,.XLSX,.XLTX,.CSV,.XLSB,.XLSM,.XLTM,.ETS,';
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        if (this.isTabMore === true) {
          let dataName = 'courseWareSearch';
          if (this.currentTab === 0) {
            dataName = 'courseWareClass';
          } else if (this.currentTab === 1) {
            dataName = 'courseWarePrivate';
          } else if (this.currentTab === 2) {
            dataName = 'courseWarePublic';
          }
          this[dataName].forEach((item) => {
            item.operateStatus = false;
          });
          this.isTabMore = false;
        }
      }
    });
    // 加载课件
    TCIC.SDK.instance.on(Constant.TEventLoadDocument, (docInfo) => {
      this.processDoc(docInfo);
      if (docInfo.forbid) {
        window.showToast(i18next.t('暂时不支持该格式的音视频类{{arg_0}}，如有需要，可使用屏幕共享', { arg_0: this.roomInfo.courseware }));
      } else {
        this.useDocument(docInfo);
      }
    });

    this.isClassStarted = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
    if (!this.isClassStarted) {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
        this.isClassStarted = true;
        this.useDocument(this.currentDocInfo, true);
      });
    }
    this.isHidePrivateDocument = TCIC.SDK.instance.getState(Constant.TStateHidePrivateDocument, false);
    this.addLifecycleTCICStateListener(Constant.TStateHidePrivateDocument, (state) => {
      this.isHidePrivateDocument = state;
      this.courseType = this.isHidePrivateDocument ? 1 : 0;
    });
    this.courseType = this.isHidePrivateDocument ? 1 : 0;

    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
  },

  beforeDestroy() {
    this.cleanupResizeObserver?.();
  },

  methods: {
    // 自动调整pageSize
    resize() {
      const listContentHeight = this.$refs.listContent?.clientHeight;
      if (!listContentHeight) {
        return;
      }
      let pageSize = this.pageSize;
      if (listContentHeight > 0) {
        pageSize = Math.floor(listContentHeight / this.itemHeight);
      }
      console.log(`[Document] resize, listContentHeight ${listContentHeight}, pageSize ${this.pageSize} -> ${pageSize}`);
      if (pageSize !== this.pageSize) {
        this.pageSize = pageSize;
        let currentPage = this.currentPage;
        if (this.totalNumber > 0) {
          const totalPage = Math.ceil(this.totalNumber / this.pageSize);
          currentPage = Math.min(currentPage, totalPage);
        } else {
          currentPage = 1;
        }
        console.log(`[Document] totalNumber ${this.totalNumber}, currentPage ${this.currentPag} -> ${currentPage}`);
        if (currentPage !== this.currentPage) {
          this.currentPage = currentPage;
        }
        this.getCurrentTabDocumentList();
      }
    },
    handleDrawerOpen() {
      this.$nextTick(() => {
        this.resize();

        const resizeObserver = new ResizeObserver(() => {
          this.resize();
        });
        resizeObserver.observe(this.$refs.listContent);

        this.cleanupResizeObserver = () => {
          resizeObserver.disconnect();
        };
      });
    },
    handleDrawerClose() {
      this.cleanupResizeObserver?.();
      this.cleanupResizeObserver = null;
    },
    // 获取课件列表
    getAllTabsDocumentList() {
      this.getClassDocumentList();
      this.getPrivateDocumentList();
      this.getPublicDocumentList();
    },

    checkTranscoding(files) {
      return files.find(item => item.transcodeType !== 0 && item.transcodeProgress !== 100 && item.transcodeState !== 2) || false;
    },

    // 获取本节课课件
    getClassDocumentList() {
      clearTimeout(this.timer.private);
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const params = {
        schoolId: schoolInfo.schoolId,
        classId: classInfo.classId,
        page: this.currentPage,
        limit: this.pageSize,
        permission: [0],
        owner: classInfo.teacherId,
        keyword: (this.searchName).trim(),
      };
      TCIC.SDK.instance.getDocumentList(params)
        .then((res) => {
          res.documents.forEach((item) => {
            item = this.processDoc(item);
          });
          this.courseWareClass = res.documents;
          this.totalClass = res.total;
          console.log('[Document] getClassDocumentList success', this.totalClass);
          if (this.checkTranscoding(res.documents)) {
            this.timer.class = setTimeout(() => {
              this.getClassDocumentList();
            }, TimeOutDuration);
          }
        })
        .catch((err) => {
          console.error('[Document] getClassDocumentList error', err);
          this.courseWareClass = [];
          this.totalClass = 0;
        });
    },

    // 获取私人课件
    getPrivateDocumentList() {
      clearTimeout(this.timer.private);
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const params = {
        schoolId: schoolInfo.schoolId,
        page: this.currentPage,
        limit: this.pageSize,
        permission: [0],
        owner: TCIC.SDK.instance.getUserId(),
        keyword: (this.searchName).trim(),
      };
      TCIC.SDK.instance.getDocumentList(params)
        .then((res) => {
          this.courseWareList = [];
          res.documents.forEach((item) => {
            item = this.processDoc(item);
          });
          this.courseWarePrivate = res.documents;
          this.totalPrivate = res.total;
          console.log('[Document] getPrivateDocumentList success', this.totalPrivate);
          if (this.checkTranscoding(res.documents)) {
            this.timer.private = setTimeout(() => {
              this.getPrivateDocumentList();
            }, TimeOutDuration);
          }
        })
        .catch((err) => {
          console.error('[Document] getPrivateDocumentList error', err);
          this.courseWareList = [];
          this.totalPrivate = 0;
        });
    },

    // 处理课件
    processDoc(item) {
      item.forbid = false;
      switch (item.docType.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
          item.type = 'jpg';
          break;
        case 'mp3':
        case 'wav':
        case 'wma':
        case 'aac':
        case 'flac':
        case 'm4a':
        case 'oga':
        case 'opus':
        case 'mp4':
        case '3gp':
        case 'mpg':
        case 'mpeg':
        case '3g2':
        case 'avi':
        case 'flv':
        case 'wmv':
        case 'h264':
        case 'm4v':
        case 'mj2':
        case 'mov':
        case 'ogg':
        case 'ogv':
        case 'rm':
        case 'qt':
        case 'vob':
        case 'webm':
        case 'f4v':
        case 'rmvb':
          /** 白板用了h5的video和audio支持音视频课件，目前支持以下类型
            <video>标签所支持的视频格式和编码：
              MP4 = MPEG 4文件使用 H264 视频编解码器和AAC音频编解码器
              WebM = WebM 文件使用 VP8 视频编解码器和 Vorbis 音频编解码器
              Ogg = Ogg 文件使用 Theora 视频编解码器和 Vorbis音频编解码器
            <audio>元素支持三种音频格式文件: MP3、Wav、Ogg
           */
          if ((TCIC.SDK.instance.isWeb() || TCIC.SDK.instance.isMobile())) {  // web端及移动端
            const type = item.docType.toLowerCase();
            if (type !== 'mp4' && type !== 'webm'
            && type !== 'ogg' && type !== 'mp3' && type !== 'wav' && type !== 'mov') {
              item.forbid = true;
            }
          }
          if (item.transcodeResult.endsWith('mp3')) {
            item.type = 'mp3';
          } else {
            item.type = item.docType;
          }
          break;
        default:
          item.type = item.docType;
          break;
      }
      item.operateStatus = false;
      return item;
    },

    // 获取公共课件
    getPublicDocumentList() {
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const params = {
        schoolId: schoolInfo.schoolId,
        page: this.currentPage,
        limit: this.pageSize,
        permission: [1],
        owner: '',
        keyword: (this.searchName).trim(),
      };
      TCIC.SDK.instance.getDocumentList(params)
        .then((res) => {
          this.courseWareList = [];
          res.documents.forEach((item) => {
            item = this.processDoc(item);
          });
          this.courseWarePublic = res.documents;
          this.totalPublic = res.total;
          console.log('[Document] getPublicDocumentList success', this.totalPublic);
          if (this.checkTranscoding(res.documents)) {
            this.timer.public = setTimeout(() => {
              this.getPublicDocumentList();
            }, TimeOutDuration);
          }
        })
        .catch((err) => {
          console.error('[Document] getPublicDocumentList error', err);
          this.courseWareList = [];
          this.totalPublic = 0;
        });
    },
    // 获取公共课件
    getSearchDocumentList(owner = '') {
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const params = {
        schoolId: schoolInfo.schoolId,
        page: this.currentPage,
        limit: this.pageSize,
        permission: [2],
        owner,
        keyword: (this.searchName).trim(),
      };
      TCIC.SDK.instance.getDocumentList(params)
        .then((res) => {
          this.courseWareList = [];
          res.documents.forEach((item) => {
            item = this.processDoc(item);
          });
          this.courseWareSearch = res.documents;
          this.totalSearch = res.total;
          console.log('[Document] getSearchDocumentList success', this.totalSearch);
          if (this.checkTranscoding(res.documents)) {
            this.timer.search = setTimeout(() => {
              this.getSearchDocumentList();
            }, TimeOutDuration);
          }
        })
        .catch((err) => {
          console.error('[Document] getSearchDocumentList error', err);
          this.courseWareSearch = [];
        });
    },
    // 切换tab
    handleTab(permission) {
      if (this.currentTab === permission || this.currentTab > 9) return;
      this.currentTab = permission;
      this.currentPage = 1;
      if (!this.isUpload) {
        this.getCurrentTabDocumentList();
      }
    },
    // 搜索
    searchDocument: Lodash.debounce(function (val) {
      this.currentPage = 1;
      this.searchName = val;
      if (this.searchName.trim().length > 0) {
        if (this.currentTab < 10) {
          this.currentTab += 10;
        }
      } else {
        if (this.currentTab >= 10) {
          this.currentTab -= 10;
        }
      }
      this.getCurrentTabDocumentList();
    }, 400),
    getCurrentTabDocumentList() {
      if (this.currentTab === 0) {
        this.getClassDocumentList();
      } else if (this.currentTab === 1) {
        this.getPrivateDocumentList();
      } else if (this.currentTab === 2) {
        this.getPublicDocumentList();
      } else {
        const uid = TCIC.SDK.instance.getUserId();
        this.getSearchDocumentList(uid);
      }
    },
    // 点击使用课件
    async useDocument(copyData, refresh = false) {
      if (!copyData) return;
      this.currentDocInfo = copyData;
      console.log('copyData copyData---->', copyData);
      const now = new Date().getTime();
      if (now - this.loadTimeStamp < 1000) {  // 限制快速加载
        return ;
      }
      if (copyData.forbid) {
        window.showToast(i18next.t('暂不支持该格式的音视频播放，可在客户端播放'));
        return;
      }
      if (copyData.transcodeState === 1 || copyData.transcodeState === 2) {
        window.showToast(i18next.t('无法加载未上传成功的{{arg_0}}', { arg_0: this.roomInfo.courseware }));
        return;
      } if (copyData.pages > 2000) {
        window.showToast(i18next.t('{{arg_0}}页数超过上限', { arg_0: this.roomInfo.courseware }));
        return;
      }
      this.loadTimeStamp = now;
      const teduBoard = TCIC.SDK.instance.getBoard();
      await DocumentUtil.addSnapshotMark('useDocument');
      const docUrl = copyData.transcodeType > 0 ? copyData.transcodeResult : copyData.docUrl;
      const docType = copyData.type.toUpperCase();
      const exts = '(mp4|3gp|mpg|mpeg|3g2|avi|flv|wmv|h264|m4v|mj2|mov|ogg|ogv|rm|qt|vob|webm|f4v|rmvb'
        + '|MP4|3GP|MPG|MPEG|3G2|AVI|FLV|WMV|H264|M4V|MJ2|MOV|OGG|OGV|RM|QT|VOB|WEBM|F4V|RMVB)$';
      const extRegExp = new RegExp(exts, 'i');
      if (/(bmp|jpg|jpeg|png|gif|webp|svg|BMP|JPG|JPEG|PNG|GIF)$/i.test(docType)) { // 图片
        // 如果在播放视频，则先停止
        if (this.isElectron) {
          const vodPlaying = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play) < 2;
          if (vodPlaying) {
            const vodComponent = TCIC.SDK.instance.getComponent('vod-tool-component');
            vodComponent.getVueInstance().stopVideo();
          }
        }
        const defaultFileList = teduBoard.getFileInfoList();
        let boardInfo = null;
        for (const file of defaultFileList) {
          boardInfo = file.boardInfoList.find(item => item.backgroundUrl === copyData.docUrl);
          if (boardInfo) {
            break;
          }
        }
        if (boardInfo && !refresh) {
          if (teduBoard.getCurrentBoard() !== boardInfo.boardId) {
            teduBoard.switchFile('#DEFAULT');
            teduBoard.gotoBoard(boardInfo.boardId);
          }
        } else {
          if (refresh) { // 首次开课重新加载一次文件，防止开课前数据丢失
            teduBoard.deleteFile();
          }
          // 直接设置背景
          teduBoard.addImagesFile([copyData.docUrl], copyData.docName);
        }
      } else if (extRegExp.test(docType)) {
        // 视频文件
        if (TCIC.SDK.instance.getState(TCIC.TMainState.Sub_Camera) === 0) {
          window.showToast(i18next.t('停止辅助摄像头后才可加载视频{{arg_0}}', { arg_0: this.roomInfo.courseware }));
          return ;
        }
        TCIC.SDK.instance.getMics().then((mics) => {
          if (mics.length === 0) {
            // 老师没有打开麦克风，学生端将听不到声音
            window.showToast(i18next.t('检测到当前麦克风未打开，{{arg_0}}将听不到音视频{{arg_1}}的声音', { arg_0: this.roomInfo.student, arg_1: this.roomInfo.courseware }));
          }
        });
        // PC 端播放视频与web 端保持一致，不单独使用播放器
        // if (this.isElectron) {
        //   const vodComponent = TCIC.SDK.instance.getComponent('vod-tool-component');
        //   vodComponent.getVueInstance().loadVideo(copyData.transcodeType > 0 ? copyData.transcodeResult : copyData.docUrl);
        // } else {
          const fileInfoList = teduBoard.getFileInfoList();
          copyData.docUrl = copyData.transcodeResult;

          const hasSameFile = fileInfoList.find(item => item.downloadURL === copyData.docUrl);

          if (hasSameFile) {
            teduBoard.switchFile(hasSameFile.fid);
          } else {
            teduBoard.addVideoFile(copyData.docUrl, copyData.docName);
          }
        // }
      } else if (/(mp3|wav|wma|aac|flac|m4a|oga|opus|MP3|WAV|WMA|AAC|FLAC|M4A|OGA|OPUS)$/i.test(docType)) {
        console.log('::: 音频进入');
        // 音频文件
        // if (!this.isClassStarted) {
        //   window.showToast(i18next.t('请点击{{arg_0}}后，再播放音视频{{arg_1}}', { arg_0: this.roomInfo.startRoom, arg_1: this.roomInfo.courseware }));
        //   return ;
        // }
        TCIC.SDK.instance.getMics().then((mics) => {
          if (mics.length === 0) {
            // 老师没有打开麦克风，学生端将听不到声音
            window.showToast(i18next.t('检测到当前麦克风未打开，{{arg_0}}将听不到音视频{{arg_1}}的声音',  { arg_0: this.roomInfo.student, arg_1: this.roomInfo.courseware }));
          }
        });
        const audioComponent = TCIC.SDK.instance.getComponent('audio-player-component');
        audioComponent.getVueInstance().play({
          id: copyData.docId,
          name: copyData.docName,
          path: copyData.transcodeType > 0 ? copyData.transcodeResult : copyData.docUrl,
        });
        // if (this.isElectron) {
        //   const audioComponent = TCIC.SDK.instance.getComponent('audio-player-component');
        //   audioComponent.getVueInstance().play({
        //     id: copyData.docId,
        //     name: copyData.docName,
        //     path: copyData.transcodeType > 0 ? copyData.transcodeResult : copyData.docUrl,
        //   });
        // } else {
        //   /**
        //    * 音频播放需要改成用自建
        //    * TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_GLOBAL_AUDIO
        //    * TEDU_BOARD_ELEMENT_AUDIO
        //    */
        //   const audioEleId = teduBoard.addElement(
        //     TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_GLOBAL_AUDIO,
        //     copyData.docUrl,
        //     {
        //       title: copyData.docName,
        //     },
        //   );
        //   // teduBoard.playAudio(audioEleId);
        // }
        TCIC.SDK.instance.getComponent('board-tool-component').getVueInstance()
          .setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
      } else if (copyData.transcodeType === 1) {
        // 如果在播放视频，则先停止
        if (this.isElectron) {
          const vodPlaying = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play) < 2;
          if (vodPlaying) {
            const vodComponent = TCIC.SDK.instance.getComponent('vod-tool-component');
            vodComponent.getVueInstance().stopVideo();
          }
        }
        const fileInfoList = teduBoard.getFileInfoList();
        copyData.docUrl = copyData.transcodeResult;

        const hasSameFile = fileInfoList.find(item => item.downloadURL === copyData.docUrl);

        if (hasSameFile && !refresh) {
          teduBoard.switchFile(hasSameFile.fid);
        } else {
          if (refresh) {
            teduBoard.deleteFile();
          }
          console.log('switch file ~~~~>>>>', copyData);
          const transcodeResult = copyData.transcodeResult;
          // 转码文件
          teduBoard.addTranscodeFile({
            title: copyData.docName,
            pages: copyData.pages || 10,
            resolution: ''.concat(copyData.width, 'x').concat(copyData.height),
            url: transcodeResult,
          });
        }
      }
      if (this.$refs.closeBtn) {
        this.$refs.closeBtn.click();
      }
      this.hideDocument();
      TCIC.SDK.instance.reportEvent('load_document', copyData);
      if (this.checkHasBindDocument(copyData)) {
        return;
      }
      TCIC.SDK.instance.bindDocumentToClass(copyData.docId)
        .then(() => {
          this.getClassDocumentList();
        })
        .catch((err) => {
          const error = err || {};
          const code = error.code || -1;
          TCIC.SDK.instance.reportEvent('bindDocumentToClass', copyData, code, 0, 'error');
        });
    },
    // 切换页面内容
    changeContent(val = 'list') {
      this.currentContent = val;
    },
    checkHasBindDocument(copyData) {
      copyData = copyData || {};
      const docId = copyData.docId;
      let res = false;
      if (this.courseWareClass && this.courseWareClass.length && docId) {
        const len = this.courseWareClass.length;
        for (let i = 0; i < len; i++) {
          const item = this.courseWareClass[i];
          // 如果当前课堂里已经存在同一个doc则不执行绑定
          if (item && item.docId && item.docId === docId) {
            res = true;
            break;
          }
        }
        // 如果当前doc是私有的(permission 0)且owner不是当前userId则不绑定
        if (copyData.permission === 0 && copyData.owner !== TCIC.SDK.instance.getUserId()) {
          res = true;
        }
      }
      return res;
    },
    // 添加在线课件
    submitOnlineCourse(courseware) {
      const onlineCourse = courseware || {
        docName: this.onlineCourseTitle,
        docUrl: this.onlineCourseUrl,
      };
      console.log('onlineCourse', courseware);
      const teduBoard = TCIC.SDK.instance.getBoard();
      const title = !onlineCourse.docName ? `${i18next.t('在线{{arg_0}}', { arg_0: this.roomInfo.courseware })  }#default-title#` : onlineCourse.docName;
      const boardId = teduBoard.addBoard();
      let onlineElIdMap = localStorage.getItem('onlineElIdMap');
      onlineElIdMap = onlineElIdMap ? JSON.parse(onlineElIdMap) : {};
      onlineElIdMap[boardId] = title;
      localStorage.setItem('onlineElIdMap', JSON.stringify(onlineElIdMap));
      const elId = teduBoard.addElement(TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5, onlineCourse.docUrl, { erasable: false });
      TCIC.SDK.instance.reportEvent('load_document', { docUrl: onlineCourse.docUrl, docName: title, docType: 'h5', custom: !!courseware });
      if (elId) {
        this.hideDocument();
        this.onlineCourseUrl = '';
        this.onlineCourseTitle = '';
      }
    },
    // 上传
    handleChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      if (fileList[0]) {
        console.log('fileList', file);
        const files = fileList[0].raw;
        const index = files.name.lastIndexOf('.');
        const name = files.name.substring(0, index);
        const baseUpExt = 'ppt|ppsx|pptx|doc|docx|xlsx|pdf|jpg|jpeg|png|gif|bmp|PPT|PPSX|PPTX|DOC|DOCX|PDF|JPG|JPEG|PNG|GIF|BMP|'
                         + 'xls|xlt|et|ett|xlsx|xltx|csv|xlsb|xlsm|xltm|ets|'
                         + 'XLS|XLT|ET|ETT|XLSX|XLTS|CSV|XLSB|XLSM|XLTM|ETS|';
        const audioUpExt = 'mp3|wav|wma|aac|flac|m4a|oga|opus|MP3|WAV|WMA|AAC|FLAC|M4A|OGA|OPUS|';
        const videoUpExt = 'mp4|3gp|mpg|mpeg|3g2|avi|flv|wmv|h264|m4v|mj2|mov|ogg|ogv|rm|qt|vob|webm|f4v|rmvb|'
          + 'MP4|3GP|MPG|MPEG|3G2|AVI|FLV|WMV|H264|M4V|MJ2|MOV|OGG|OGV|RM|QT|VOB|WEBM|F4V|RMVB|';
        // 支持上传类型
        const suffix = files.name.substring(index + 1, files.name.length);
        const isBaseExt = baseUpExt.indexOf(`${suffix}|`) > -1;
        const isAudioExt = audioUpExt.indexOf(`${suffix}|`) > -1;
        const isVideoExt = videoUpExt.indexOf(`${suffix}|`) > -1;
        if (!isBaseExt && !isAudioExt && !isVideoExt) {
          window.showToast(i18next.t('选择的{{arg_0}}文件格式暂不支持', { arg_0: this.roomInfo.courseware }), 'error');
          return;
        }
        // 上传文件名长度
        if (name.length > 100) {
          window.showToast(i18next.t('文件名不能超过100字符'), 'error');
          return;
        }
        // 上传文件大小
        const size = TCIC.SDK.instance.isMac() ? (files.size / 1000 / 1000).toFixed(2) : (files.size / 1024 / 1024).toFixed(2);
        if (isAudioExt && size > 50) {
          window.showToast(i18next.t('音频文件大小不超过50M'), 'error');
          return;
        } if (isVideoExt && size > 500) {
          window.showToast(i18next.t('视频文件大小不超过500M'), 'error');
          return;
        } if (!isAudioExt && !isVideoExt && size > 500) {
          window.showToast(i18next.t('文件大小不超过500M'), 'error');
          return;
        }
        console.log('[Document] uploadDocument', files.name);
        TCIC.SDK.instance.notify(Constant.TEventAddDocumentUpload, {
          file: fileList[0],
          permission: this.courseType,
          type: suffix,
          setUploadStatus: (value) => {
            this.isUpload = value;
            this.uploadStatus = false;
          },
        });
        this.changeContent('list');
        this.uploadStatus = true;
        this.currentFile = fileList[0];
        // this.uploadDocument(fileList[0].raw);
      }
    },
    // 上传课件方法
    uploadDocument(file) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const schoolId = classInfo.schoolId;
      this.fileInfo = file;
      try {
        this.filePercent = 0;
        this.taskId = '';
        TCIC.SDK.instance.uploadDocument(
          file, schoolId,
          async (taskId) => {
            this.taskId = taskId;
          },
          async (loaded, total, speed, percent) => {
            this.filePercent = (percent * 100).toFixed(0);
          },
          async (err, res) => {
            if (err.errorCode === 0) {
              this.btnTabStatus = false;
              this.fileUrl = res.location;
            }
          },
        );
      } catch (error) {
        console.log('上传课件', error);
      }
    },
    // 取消课件上传
    cancelUpload() {
      TCIC.SDK.instance.cancelUploadDocument(this.taskId);
      this.fileInfo = {};
      this.btnTabStatus = true;
      this.uploadStatus = false;
    },
    // 更多操作
    handleOperate(dataName, indexList) {
      this.isTabMore = true;
      this[dataName].forEach((item, index) => {
        if (index === indexList) {
          item.operateStatus = true;
        } else {
          item.operateStatus = false;
        }
      });
    },
    // 删除课件
    deleteDocument(docId) {
      TCIC.SDK.instance.showMessageBox('', i18next.t('您确定要删除选中的{{arg_0}}吗？', { arg_0: this.roomInfo.courseware }), [i18next.t('确定'), i18next.t('取消')], (index) => {
        if (index === 0) {
          TCIC.SDK.instance.deleteDocument(docId)
            .then(() => {
              this.currentPage = 1;
              this.getAllTabsDocumentList();
            })
            .catch((error) => {
              TCIC.SDK.instance.reportEvent('handle_delete_document', error, -1);
              window.showToast(error.errorMsg, 'error');
            });
        }
      });
    },
    // 取消绑定课件
    unbindDocument(docId) {
      TCIC.SDK.instance.showMessageBox(
        '',
        i18next.t(
          '是否解除{{arg_0}}与本{{room}}的关联关系？',
          { arg_0: this.roomInfo.courseware, room: this.roomInfo.room },
        ), [i18next.t('确定'), i18next.t('取消')], (index) => {
          if (index === 0) {
            TCIC.SDK.instance.unbindDocumentToClass(docId)
              .then(() => {
                this.currentPage = 1;
                this.getClassDocumentList();
              })
              .catch((error) => {
                TCIC.SDK.instance.reportEvent('handle_unbind_document', error, -1);
                window.showToast(error.errorMsg, 'error');
              });
          }
        },
      );
    },
    // 分页
    currentPageChange(val) {
      this.currentPage = val;
      this.getCurrentTabDocumentList();
    },

    hideComponent() {
      this.hide();
      this.$emit('hide');
    },

    getTranscodeError: DocumentUtil.getTranscodeError,
  },
};
</script>

<style lang="less">
.hide-document {
  cursor: pointer;
}

// hover提示
.tooltip-upload-info {
  display: flex;
  align-items: center;
  width: 400px;
  padding: 16px !important;
  font-size: 12px !important;
  background: #191D2C !important;
  border-radius: 10px !important;
  line-height: inherit !important;
  box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1) !important;
  .title {
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 600;
  }
  .item {
    display: flex;
    flex-direction: column;
    &:last-child {
      span {
        margin-bottom: 0;
      }
    }
  }
  label {
    line-height: 20px;
    font-size: 12px;
  }
  span {
    margin-bottom: 8px;
    font-size: 12px;
    color: #8A9099;
    line-height: 18px;
  }
  .auto-handle {
    display: flex;
    flex-direction: column;
    margin-top: 8px;
    span {
      color: #FFF;
      margin-bottom: 0px;
      line-height: 20px;
      &:nth-child(1) {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
      }
    }
  }
}
.document-drawer {
  .course-ware-back {
    vertical-align: bottom;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    cursor: pointer;
    > a {
      display: flex;
      margin-right: 8px;
    }
    span {
      color: #fff;
    }
    label {
      position: relative;
      margin-left: 12px;
      padding-left: 12px;
      color: #8A9099;
      &:after {
        position: absolute;
        top: calc(50% - 8px);
        left: 0;
        width: 1px;
        height: 18px;
        //transform: translate(0,-50%);
        opacity: 0.3;
        background: #FFFFFF;
        content: '';
      }
    }

    img {
      width: 24px;
      height: 24px;
      position: relative;
    }
  }
}
.document-component {
  height: 100%;
  padding: 0 15px;
  display: flex;
  flex-direction: column;

  .header-component-popover & {
    position: fixed;
    max-width: 100vw;
    width: 500px;
    right: 0;
    top: 64px;
    bottom: 0;
  }

  .course-ware-header {
    width: 100%;
    .course-ware-name {
      color: white;
      margin-left: 4px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      padding: 7px 22px;
      margin: 0 -15px;
    }

    .course-ware-form {
      margin: 15px 0;
      flex: 1;
      display: flex;
      justify-content: space-between;
      padding: 5px;
    }

    .course-ware-input {
      width: 100%;
      height: 34px;
      background: #293047;
      border-radius: 8px;
      margin-left: 8px;

      input:focus::-webkit-input-placeholder {
        color: transparent;
      }

      .el-input__inner {
        width: 100%;
        height: 100%;
        background: transparent;
        border: none;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
        line-height: 100%;
        outline: none;
      }

      .el-icon-search:before {
        content: url('./assets/search_icon.svg');
      }

      .el-input__inner {
        width: 100%;
        height: 100%;
        background: transparent;
        font-size: 14px;
        color: #999;
        line-height: 100%;
        outline: none;
      }
    }

    .el-button {
      background: rgba(61, 126, 253, 0.10);
      border-radius: 8px;
      border: 1px solid #3D7EFD;
      color: #3D7EFD;
      font-size: 14px;
      font-weight: 500;
      padding: 6px 16px;
      line-height: 20px;
      align-self: center;
      height: 34px;

      .icon-plus-circle {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('./assets/icon-plus-circle.svg');
        background-repeat: no-repeat;
        background-size: 100%;
        position: relative;
        top: 3px;
        margin-right: 4px;
      }

      .el-icon-plus:before {
        content: "+";
        font-size: 20px;
        color: #fff;
        line-height: 10px;
      }
    }

  }

  .course-online-wrapper {
    .course-ware-input {
      padding-bottom: 10px;
      .el-input__inner {
        border-radius: 0;
        border-color: #0C73FE;
        background-color: #000;
      }
      &.warning {
        .el-input__inner {
          border-color: #F5222D;
        }
      }
    }
    .course-ware-tip {
      color: #F5222D;
      opacity: 0;
      &.show {
        opacity: 1;
      }
    }
  }

  .course-ware-main {
    flex: 1;
    overflow: auto;
  }

  .course-main-list_box {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
  }

  .course-main-tab {
    display: flex;
    padding: 0 2px;
    margin-bottom: 12px;

    .course-tab-item {
      font-size: 14px;
      color: #fff;
      line-height: 20px;
      cursor: pointer;
      text-align: center;
      margin: 0 18px;
      position: relative;
      display: flex;
      align-items: flex-end;
      justify-content: center;

      &.disable {
        color: #888;
      }
    }

    .course-tab-item_active {
      color: #3D7EFD;

      &:after {
        content: "";
        display: block;
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        border-radius: 3px;
        background: #3D7EFD;
      }
    }
  }
  .course-main-content-wrap{
    flex: 1;
    overflow: hidden;
  }

  .course-main-content {
    height: calc(100% - 70px);
    overflow: hidden;
  }

  .course-main-footer {
    width: 100%;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #5a5a5a;
  }

  .emptyContent {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    > span {
      height: 60px;
      line-height: 20px;
      color: #fff;
      text-align: center;
    }
  }

  .course-con-list {
    width: 100%;
    height: 50px; // 注意修改js代码里的itemHeight
    padding: 0 8px;
    &.last-child {
      .course-con-list_info {
        border: none;
      }
    }
    &.forbid {
      .name {
        color: gray;
      }

      &:hover {
        border: none;
        border-bottom: 1px solid #5A5A5A;
      }
    }

    &:hover {
      border: 2px solid #006EFF;

      .course-con-list_info {
        border-bottom: none;
      }
    }
  }

  .course-con-list_info {
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #5A5A5A;
    display: flex;
    justify-content: space-between;
    cursor: pointer;

    .course-list_operate {
      color: #fff;
      align-self: center;
      position: relative;
    }

    .course-list_more {
      width: 20px;
      height: 20px;
      align-self: center;
      cursor: pointer;
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .course-list_left {
    flex: 1;
    display: flex;
    justify-content: space-between;
    width: 90%;

    .information {
      display: flex;
      flex: 1;
      width: 90%;
    }
  }


  .course-ware-main {
    .icon {
      width: 21px;
      height: 23px;
      align-self: center;
      background-size: 100%;
      background-position: 50%;
      flex-shrink: 0;
    }

    .name {
      max-width: 395px;
      font-size: 14px;
      color: #fff;
      align-self: center;
      margin-left: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
    }

    .el-pagination {
      height: 28px;
      bottom: 28px;
      text-align: center;

      button.btn-next, button.btn-prev {
        background-color: transparent;
      }

      .el-pager {
        li {
          color: #606266;
          width: auto;
          background: transparent;
          display: inline-block;
        }
      }
    }

    .icon_doc, .icon_DOC {
      background-image: url('./assets/ic_word.svg');
    }

    .icon_docx, .icon_DOCX {
      background-image: url('./assets/ic_word.svg');
    }

    .icon_xlsx, .icon_XLSX {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xls, .icon_XLS {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xlt, .icon_XLT {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_et, .icon_ET {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_ett, .icon_ETT {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xltx, .icon_XLTX {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_ett, .icon_ETT {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_csv, .icon_CSV {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xlsb, .icon_XLSB {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xlsm, .icon_XLSM {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_xltm, .icon_XLTM {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_ets, .icon_ETS {
      background-image: url('./assets/ic_xlsx.svg');
    }

    .icon_pdf, .icon_PDF {
      background-image: url('./assets/ic_pdf.svg');
    }

    .icon_ppt, .icon_PPT {
      background-image: url('./assets/ic_pptx.svg');
    }

    .icon_pptx, .icon_PPTX, .icon_ppsx, .icon_PPSX {
      background-image: url('./assets/ic_pptx.svg');
    }

    .icon_jpg, .icon_JPG {
      background-image: url('./assets/ic_Picture.svg');
    }

    .icon_png, .icon_PNG {
      background-image: url('./assets/ic_Picture.svg');
    }

    .icon_gif, .icon_GIF {
      background-image: url('./assets/ic_Picture.svg');
    }

    .icon_bmp, .icon_BMP {
      background-image: url('./assets/ic_Picture.svg');
    }

    .icon_mp4, .icon_MP4 {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_3gp, .icon_3GP {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_mpg, .icon_MPG {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_mpeg, .icon_MPEG {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_3g2, .icon_3g2 {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_avi, .icon_AVI {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_flv, .icon_FLV {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_wmv, .icon_WMV {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_h264, .icon_H264 {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_m4v, .icon_M4V {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_mj2, .icon_MJ2 {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_mov, .icon_MOV {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_ogg, .icon_OGG {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_ogv, .icon_OGV {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_rm, .icon_RM {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_qt, .icon_QT {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_vob, .icon_VOB {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_webm, .icon_WEBM {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_f4v, .icon_F4V {
      background-image: url('./assets/ic_MP4.svg');
    }
    .icon_rmvb, .icon_RMVB {
      background-image: url('./assets/ic_MP4.svg');
    }

    .icon_mp3, .icon_MP3 {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_wav, .icon_WAV {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_wma, .icon_WMA {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_aac, .icon_AAC {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_flac, .icon_FLAC {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_m4a, .icon_M4A {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_oga, .icon_OGA {
      background-image: url('./assets/ic_MP3.svg');
    }
    .icon_opus, .icon_OPUS {
      background-image: url('./assets/ic_MP3.svg');
    }
  }

  .course-list_left {
    .status {
      font-size: 12px;
      align-self: center;
      display: flex;
      align-content: center;
      align-items: center;
    }

    .status_doing {
      color: #FFC107;
    }

    .status_error {
      color: #FA3B3B;
      .el-icon-warning {
        // background-image: url('./assets/icon_refresh.png');
        vertical-align: top;
        font-size: 16px;
      }
    }

    .status_error img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      position: relative;
      top: 2px;
    }

    .status_success {
      color: #fff;
    }
  }

  .list_operate_wrap {
    width: 36px;
    height: 40px;
    background: rgba(0, 0, 0, .7);
    box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.5);
    border-radius: 22px;
    position: absolute;
    left: -8px;
    bottom: 20px;
    cursor: pointer;
    z-index: 10;
    display: flex;
    justify-content: center;


  }
  .operate_wrap_del {
    width: 16px;
    height: 16px;
    align-self: center;
  }

  .course-main-add_box {
    padding: 22px 16px 16px;
    .course-add-title {
      // margin-bottom: 16px;
      font-size: 14px;
      color: #8A9099;
      line-height: 22px;
      padding-right: 20px;
      text-align: left;
    }

    .course-main-type {
      display: flex;
      align-items: center;
      // margin-bottom: 24px;
      font-size: 16px;
      color: #fff;
      .el-radio {
        display: flex;
        align-items: center;
        justify-content: center;
        width: auto;
        height: 40px;
        margin-right: 24px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        .el-radio__label {
          color: rgba(#fff, .8);
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #fff;
        }
      }
    }

    .course-online-wrapper {
      margin-top: 14px;
      // margin-left: 77px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      div {
        width: 100%;
      }
    }

    .el-upload__text {
      display: flex;
      flex-direction: row;
      label {
        margin-bottom: 10px;
        font-size: 20px;
        line-height: 28px;
        color: #fff;
      }
      span {
        height: 28px;
        font-size: 16px;
        color: #8A9099;
        line-height: 28px;
        margin: 0 10px;
      }
      .ic-info-tip-wrap {
        width: 24px;
        height: 28px;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      .ic-info-tip {
        width: 12px;
        height: 12px;
        transition: all .2s;
        background: url('./assets/ic_information.svg') center no-repeat;
        &:hover {
          transition: all .2s;
          background: url('./assets/ic_information_hover.svg') center no-repeat;
        }
      }
    }

    .upload-demo {
      width: 100%;
      height: 260px;
    }

    .el-upload, .el-upload-dragger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: transparent;
    }

    .course-main-add_icon {
      margin-top: -40px;
      font-size: 90px;
      color: #fff;
      font-weight: 100;
      line-height: initial;
      &:hover {
        color: #006EFF;
      }
    }

    .el-icon-plus:before {
      content: "+";
    }

    .el-upload__text {
      font-size: 14px !important;
      color: #fff !important;
      line-height: 20px;
    }

    .uploadInfo {
      width: 100%;
      height: 23px;
      margin: 28px 0;
      display: flex;
      justify-content: space-between;
    }
  }

  .uploadInfo {
    .information {
      display: flex;
    }

    .uploadStatus {
      font-size: 14px;
      display: flex;
    }

    .uploadRefresh {
      width: 16px;
      height: 16px;
      cursor: pointer;
      margin-top: 2px;
    }

    .progress {
      color: #fff;
      margin: 0 10px;
    }

    .uploadSuccess {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      margin-top: 2px;
    }

    .uploadTxt {
      color: #006EFF;
      cursor: pointer;
    }

    .againChoose .el-upload-dragger {
      border: none;
    }
  }

  .course-main-list_box {
    overflow: hidden;

    .el-icon-arrow-left {
      background: url('./assets/left_arrow_icon.svg') no-repeat;
      background-size: contain;
    }

    .el-icon-arrow-right {
      background: url('./assets/right_arrow_icon.svg') no-repeat right;
      background-size: contain;
    }

    button:disabled {
      .el-icon-arrow-left, .el-icon-arrow-right {
        opacity: 0.3;
      }
    }

    .el-icon-arrow-left::before, .el-icon-arrow-right::before {
      color: transparent;
    }
  }
  &.small-screen {
    height: 100%;

    .upload-demo{
      max-height: 240px;
      height: auto;
    }
    .course-main-add_icon  {
      margin-top: 0;
      font-size: 50px;
    }
    .course-main-type {
      margin-bottom: 0;
    }
    .course-add-title {
      margin-bottom: 0;
    }
    .el-upload__text {
      height: 120px;
    }
    .course-main-footer {
      justify-content: center;
    }
    .course-main-tab {
      margin-left: -9px;
      margin-right: -9px;

      .course-tab-item {
        margin-left: 9px;
        margin-right: 9px;
        flex: 1;
      }
    }
  }

}

</style>
