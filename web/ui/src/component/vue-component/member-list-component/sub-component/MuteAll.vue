<template>
  <div
    class="mute-all-sub-component"
    @click="switchMuteAllStatus()"
  >
    <el-tooltip
      v-if="isSwitchMode"
      :manual="!disabled && interactiveIsTouchEvent()"
      class="item"
      :content="translateTip.content"
      placement="bottom"
      :hide-after="2000"
      :disabled="!disabled"
    >
      <el-checkbox
        v-model="isMuteAll"
        :disabled="disabled"
        @change="switchMuteAllStatus"
      >
        <span class="content">{{ $t('全员静音') }}</span>
      </el-checkbox>
    </el-tooltip>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Lodash from 'lodash';
import Constant from '@/util/Constant';

export default {
  name: 'MuteAllSubComponent',
  extends: BaseComponent,
  props: {
    parentMuteAll: {
      type: Boolean || null,
      default: null,
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isMuteAll: false,        // switch的表现层
      isMuteAllStatus: false,  // 真实状态
      disabled: true,
      isSwitchMode: false,    // 是否开关模式
      roomInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        content: i18next.t('开始{{arg_0}}后才可开启全员静音', { arg_0: this.roomInfo.startRoom }),
      };
    },
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.disabled = false;
    });
    this.makeSureClassJoined(() => {
      this.isSwitchMode = !TCIC.SDK.instance.isCoTeachingClass();
    });

    // 全员静音状态变更
    this.addLifecycleTCICStateListener(TCIC.TMainState.Mute_All, (value) => {
      this.parentMuteAll !== null && this.$emit('update:parentMuteAll', value);
      this.isMuteAll = value;
      this.isMuteAllStatus = value;
    });
  },

  beforeDestroy() {
  },
  methods: {
    switchTrigger(value) {
      this.switchMuteAllStatus(value);
    },
    switchMuteAllStatus: Lodash.throttle(function (value) {
      if (!this.isSwitchMode) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        TCIC.SDK.instance.updateTask(Constant.TConstantCoTeachingMuteAll, JSON.stringify({ timeStamp: new Date().getTime() }), -1, false, classInfo.teacherId)
          .then(() => {
            window.showToast(i18next.t('{{arg_0}}已开启全员静音', { arg_0: this.roleInfo.teacher }));
            this.isMuteAll = false;
          })
          .catch((error) => {
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
              arg_0: i18next.t('全员静音'),
              arg_1: error.errorCode, arg_2: error.errorMsg,
            }), 'error');
            this.isMuteAll = false;
          });
      } else {
        this.controlAllMicStatus(value);
      }
    }, 1000, {
      leading: false,
      trailing: true,
    }),
    // 全员静音
    controlAllMicStatus(value) {
      if (TCIC.SDK.instance.isTeacher()
          || TCIC.SDK.instance.isAssistant()
          || TCIC.SDK.instance.isSupervisor()) {  // 只有老师需要发起全员静音
        TCIC.SDK.instance.muteAll(this.isMobile ? value : this.isMuteAll)
          .catch((error) => {
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
              arg_0: this.isMuteAll ? i18next.t('解除全员静音') : i18next.t('全员静音'),
              arg_1: error.errorCode, arg_2: error.errorMsg,
            }), 'error');
          });
      }
    },
  },
};
</script>
<style lang="less">
.mute-all-sub-component {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-content: center;
  align-items: center;

  .mute-all-switch {
    margin-left: -4px;
    transform: scale(0.7);
    .el-switch__core {
      border-width: 2px;
      &:after {
        top: 0;
        left: 0;
      }
    }
  }

  .inactive {
    background: rgb(255, 255, 255)
  }

  .content {
    color: #fff;
  }

  i.mute-all-icon {
    content: url('../assets/ic-mute-all.svg');
    width: 22px;
    height: 22px;
    margin-right: 10px;
    opacity: 0.9;
  }
}
.header-menu-li {
  // 在 header-menu 里，和“全员奖励”等入口的样式保持一致
  .el-checkbox__input {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    .el-checkbox__inner {
      transform: scale(1.5);
      transform-origin: top left;
    }
  }
  .el-checkbox__label {
    font-size: 16px;
    line-height: 24px;
    padding-left: 0;
  }
}
</style>
