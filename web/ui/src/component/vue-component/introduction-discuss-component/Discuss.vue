<template>
  <div class="curriculum-discuss">
    <div
      id="im-area-pinned-on-top"
      class="im-custom-pinned-slot"
      data-slot="im-pinned"
    />
    <div class="msg-scroll-wrapper">
      <el-scrollbar ref="openclass-msg-scroll">
        <p
          v-if="inloading"
          class="loading-text"
        >
          {{ $t('加载中...') }}
        </p>
        <DiscussMsg
          v-for="msg in showMsgList"
          :key="msg.key"
          :msg="msg"
          :is-teacher="isTeacher"
          :is-assistant="isAssistant"
          :is-supervisor="isSupervisor"
          :enable-msg-control="enableMsgControl"
          :per="imgPercent"
          @set-reply-msg="setReplyMsg"
          @delete-msg="deleteMsg"
          @silence-msg="silenceMsg"
          @resend-img-msg="reSendImgMsg"
          @open-preview-img="openPreviewImg"
          @close-preview-img="closePreviewImg"
          @img-loaded="onImageLoaded"
        />
      </el-scrollbar>
      <NewMessagesTip
        class="new-msg-tip-wrapper"
        :class="{
          'show-reply': !!replyMsg,
        }"
        :unread-msg-count="unreadMsgCount"
        @click="handleClickNewMessagesTip"
      />
    </div>

    <!--客户端显示-->
    <div
      v-if="!isPad && !isMobile"
      :class="['discuss-footer', 'client','im-component-footer']"
      :style="[ { height: (isRightSideHeightEnough ? '225px' : '150px')} ]"
    >
      <QuickReply
        v-if="isShowQuickReply"
        :style="{padding: '5px 10px 0'}"
        :words-arr="wordsArr"
        class="im-component-quick-reply"
        @send-quick-msg="onSendQuickMsg"
      />
      <div class="discuss-footer-ops">
        <ReceiverSelector
          v-if="showReceiverSelector"
          style="margin-left: 16px"
          :disabled="replyMsg && !!replyMsg.dataExt?.IsPrivateMsg"
          :current-receiver="currentPrivateChatReceiver"
          :private-chat-receiver-list="privateChatReceiverList"
          @update:currentReceiver="handleReceiverUpdate"
        />
        <div v-else />
        <div class="img-smg">
          <EmojiSelector
            :disabled="isSupervisor"
            @send-emoji="sendEmoji"
          />
          <TranslatorSelector v-if="showTranslator" />
          <a
            v-if="supportFileMessage"
            :class="['im-component-img-tool', (silenceAll || currentSilenceMode === 3) && isStudent ? 'disabled' : '']"
            @click.capture="onInputFile"
          >
            <span class="file-icon" />
            <input
              ref="filePicker"
              :key="filePickerIndex"
              type="file"
              :title="$t('发送文件')"
              @change="handleSendingFileChange"
            >
            <span class="file-text">{{ $t('文件') }}</span>
          </a>
          <a
            :class="['im-component-img-tool',
                     (silenceAll || currentSilenceMode === 3) && isStudent ? 'disabled' : ''
                     // fileSelected ? 'file-selected' : 'file'
            ]"
            @click.capture="onInputFile"
          >
            <span class="image-icon" />
            <input
              ref="imagePicker"
              :key="imagePickerIndex"
              type="file"
              :title="$t('发送图片')"
              :accept="acceptExt"
              @change="handleChange"
            >
            <span class="file-text">{{ $t('图片') }}</span>
          </a>
          <div
            v-if="false && (isTeacher || isAssistant || isSupervisor)"
            class="discuss-silence"
          >
            <i v-if="false" />
            <i
              :class="[silenceAll ? '' : 'i-disable']"
              :title="silenceAll ? $t('解除全员禁言'): $t('全员禁言')"
              @click="onSilenceAll"
            />
          </div>
          <ChatSetting
            v-if="(isTeacher || isAssistant || isSupervisor)"
            :settings-list="silenceModeList"
            :silence-mode="currentSilenceMode"
            @update:silenceMode="handleSilenceModeUpdate"
          />
        </div>
      </div>
      <IMInputReply
        v-if="replyMsg"
        class="discuss-footer-reply"
        :msg="replyMsg"
        @click-close="setReplyMsg(null)"
      />
      <InputElement
        v-if="!imgSend && !fileSend && !showEditor"
        ref="input"
        class="discuss-client-input"
        :textarea="true"
        @enter="textInputEnter"
        @blur="textInputBlur"
        @change="textChange"
        @click="textInputClick"
        @paste-file="onInputPaste"
      />
      <div
        v-if="imgSend || fileSend || showEditor"
        id="editor"
        ref="editor"
        class="im-component-img-input inComponent"
        contenteditable="true"
        @paste.prevent="onPaste"
        @keydown.enter.prevent="textInputEnter"
        @keyup="imgTextChange"
      />
      <div
        v-if="showToast"
        class="im-toast"
      >
        {{ $t('请先发送图片再编辑文字消息') }}
      </div>
      <div
        class="discuss-client-btn ccc"
        :style="[ isRightSideHeightEnough ? null : { 'padding-bottom': 0}]"
      >
        <div class="im-component-switch-wrap">
          <div
            v-if="showJoinExitSwitch"
            class="im-component-msg-switch"
          >
            <el-switch
              :value="showJoinExit"
              @change="switchShowJoinExit"
            />
            <span>{{ showJoinExit ? $t('显示成员进出情况') : $t('不显示成员进出情况') }}</span>
          </div>
          <div
            v-if="showHandsUpSwitch"
            class="im-component-msg-switch"
          >
            <el-switch
              v-model="showHandsUp"
              @change="switchShowHandsUpMsg"
            />
            <span>{{ showHandsUp ? $t('显示成员举手') : $t('不显示成员举手') }}</span>
          </div>
        </div>
        <el-button
          type="primary"
          :disabled="textIsEmpty || isSupervisor"
          @click="textInputEnter"
        >
          {{ $t('发送') }}
        </el-button>
      </div>
    </div>

    <!--iPad端显示-->
    <div
      v-if="isPad && !isSupervisor"
      class="discuss-footer client-ipad"
    >
      <IMInputReply
        v-if="replyMsg"
        class="discuss-footer-reply"
        :msg="replyMsg"
        @click-close="setReplyMsg(null)"
      />
      <div
        v-if="showReceiverSelector"
        class="discuss-footer-selector"
      >
        <ReceiverSelector
          :disabled="replyMsg && !!replyMsg.dataExt?.IsPrivateMsg"
          :current-receiver="currentPrivateChatReceiver"
          :private-chat-receiver-list="privateChatReceiverList"
          @update:currentReceiver="handleReceiverUpdate"
        />
      </div>
      <div class="discuss-footer-ops">
        <div class="discuss-ipad-input">
          <EmojiSelector
            :disabled="isSupervisor"
            @send-emoji="sendEmoji"
          />
          <InputElement
            ref="input"
            class="discuss-ipad-sub-input"
            :readonly="isPad"
            @enter="textInputEnter"
            @blur="textInputBlur"
            @click="textInputClick"
          />
        </div>
        <div class="img-smg">
          <TranslatorSelector v-if="showTranslator" />
          <div
            :class="['discuss-img-tool', { 'small-screen': isSmallScreen }, (silenceAll || currentSilenceMode === 3) && isStudent ? 'disabled' : '', !imageSelected ? 'image' : 'image-selected']"
            @click.capture="onInputFile"
          >
            <input
              id="imagePicker"
              ref="imagePicker"
              :key="imagePickerIndex"
              type="file"
              :accept="supportFileMessage ? '' : acceptExt"
              @change="handleMobileChange"
            >
          </div>
          <div
            v-if="false && isTeacher"
            class="discuss-silence"
          >
            <i v-if="false" />
            <i
              :class="[silenceAll ? '' : 'i-disable']"
              :title="silenceAll ? $t('解除全员禁言'): $t('全员禁言')"
              @click="onSilenceAll"
            />
          </div>
          <ChatSetting
            v-if="(isTeacher || isAssistant || isSupervisor)"
            :settings-list="silenceModeList"
            :silence-mode="currentSilenceMode"
            @update:silenceMode="handleSilenceModeUpdate"
          />
        </div>
      </div>
    </div>

    <!--手机端，理论上不会走到这里，手机端用的是 FloatIM/PortraitIM 组件-->
    <div
      v-if="isMobile && !showMobileKey && !isPad"
      class="discuss-footer"
    >
      <IMInputReply
        v-if="replyMsg"
        class="discuss-footer-reply"
        :msg="replyMsg"
        @click-close="setReplyMsg(null)"
      />
      <div class="discuss-footer-ops">
        <!-- 连麦 -->
        <div
          v-if="enableStage || isOnStage"
          class="m-stage"
        >
          <i
            v-if="!isOnStage && stageStatus !== 0 && stageStatus !== 2"
            class="ic-m-stage"
            @click="stageDrawer = true"
          />
          <!-- 连麦成功 -->
          <i
            v-if="isOnStage"
            class="ic-m-stage approve"
            @click="onHangUpStage"
          />
          <!-- 连麦申请中 -->
          <div
            v-if="!isOnStage && stageStatus === 0"
            class="ic-m-stage-dynamic"
          >
            <i class="ic-m-stage" />
            <div class="m-stage-loading">
              <span />
              <span />
              <span />
            </div>
          </div>
          <!-- 连麦连接中 -->
          <div
            v-if="!isOnStage && stageStatus === 2"
            class="ic-m-stage-dynamic connecting"
          >
            <i class="ic-m-stage" />
            <div class="m-stage-loading">
              <span />
              <span />
              <span />
            </div>
          </div>
        </div>
        <div class="discuss-input">
          <el-input
            v-model="input"
            :placeholder="mobilePlaceholder"
            @click.native="weakMobileKey"
          />
          <TranslatorSelector v-if="showTranslator" />
          <div class="mobile-img-msg">
            <a
              class="discuss-img-tool"
              @click.capture="onInputFile"
            >
              <input
                id="imagePicker"
                ref="imagePicker"
                type="file"
                :accept="supportFileMessage ? '' : acceptExt"
                @change="handleMobileChange"
              >
            </a>
          </div>
        </div>
        <EmojiSelector
          :disabled="isSupervisor"
          @send-emoji="sendEmoji"
        />
      </div>
    </div>
    <div
      v-if="stageDrawer"
      class="m-stage-drawer"
    >
      <div class="m-stage-drawer_list">
        <div
          class="item"
          @click="onAskStage"
        >
          <img
            src="./assets/ic_mobile_stage.svg"
            alt=""
          >{{ translateTip.chatWithTeacher }}
        </div>
        <div
          class="item"
          @click="stageDrawer = false"
        >
          {{ $t('取消') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import store from 'store';
import Constant from '@/util/Constant';
import ImageCache from '../image-preview-component/ImageCache.js';
import { MsgImageState } from '../im-component/MsgBase';
import { IMBaseComponent } from '../im-component/IMBase';
import EmojiSelector from '../im-component/EmojiSelector';
import TranslatorSelector from '../im-component/TranslatorSelector';
import InputElement from '../im-component/InputElement';
import IMInputReply from '../im-component/IMInputReply.vue';
import DiscussMsg from './DiscussMsg';
import QuickReply from '../im-component/QuickReply.vue';
import ReceiverSelector from '@/component/vue-component/im-component/ReceiverSelector.vue';
import ChatSetting from '@/component/vue-component/im-component/ChatSetting.vue';
import NewMessagesTip from '@/component/vue-component/im-component/NewMessagesTip.vue';

export default {
  name: 'IntroductionDiscussComponent',
  components: {
    NewMessagesTip,
    ChatSetting,
    ReceiverSelector,
    DiscussMsg,
    EmojiSelector,
    TranslatorSelector,
    QuickReply,
    InputElement,
    IMInputReply,
  },
  extends: IMBaseComponent,
  data() {
    return {
      activeName: 'second',
      input: '',
      isPad: TCIC.SDK.instance.isPad(),
      textIsEmpty: true,
      showMobileKey: false,
      isTeacher: false, // 是否老师
      isAssistant: false, // 是否助教
      isSupervisor: false,
      acceptExt: '',
      imagePickerIndex: 0,
      imgSend: false,
      fileSend: false,
      showEditor: false,
      showJoinExitSwitch: false,  // 是否显示进入房切换开关
      showJoinExit: true,         // 当前是否启用了显示进出房消息
      showHandsUpSwitch: false, // 是否显示学生举手切换开关
      showHandsUp: true,  // 是否显示成员举手
      imageSelected: false, // 图片按钮处于选中状态
      fileSelected: false,       // 图片按钮处于选中状态
      file: null,
      pasteFile: null,
      imgUrl: '',
      currentImgID: 0,
      per: '',
      imgSendStatus: 2, // 0:发送失败 1:发送中 2:发送成功
      enableMsgControl: false,
      isScreenShare: false,
      stageDrawer: false, // 连麦弹出框
      stageIcon: false, // 连麦弹出框
      enableStage: false, // 是否允许上台
      stageStatus: TCIC.TCommandStatus.None, // 是否正在请求连麦
      isOnStage: false, // 权限列表中stage
      showToast: false,
      studentShowJoinExitMsg: true, // 学生是否显示进退房信息
      showJoinExitMsg: true, // 是否显示进退房信息
      tipsHandUp: i18next.t('举手'),
      lastScrollHeight: 0,
      lastModifyTime: -1,
      scrollEndTimer: null,
      inloading: false,
      showTranslator: false,
      isLiveClass: false,
      isOneOnOneClass: false, // 是否是一对一课堂
      canMemberStageUp: true,
      roleInfo: null,
      roomInfo: null,
      wordsArr: TCIC.SDK.instance.getQuickIMWords(),
      isRightSideHeightEnough: true, // 右侧区域是否足够高
      filePickerIndex: 0,
      supportFileMessage: false,
      unreadMsgCount: 0,
    };
  },
  computed: {
    canMemberHandup() {
      // 举手可用
      return !this.isLiveClass && !this.isUnitedLiveClass && this.canMemberStageUp;
    },
    mobilePlaceholder() {
      if ((!this.silenceAll || !this.currentSilenceMode === 3) && this.chatEnabled) {
        return i18next.t('输入你想说的话');
      }
      return i18next.t('已禁言');
    },
    translateTip() {
      return {
        chatWithTeacher: i18next.t('跟{{arg_0}}一起上台', { arg_0: this.roleInfo.teacher }),
      };
    },
    isShowQuickReply() {
      return this.isStudent && this.wordsArr.length !== 0;
    },
    showReceiverSelector() {
      if (!this.supportPrivateChat) {
        return false;
      }
      if (this.isStudent && !this.isAssistant) {
        return this.currentSilenceMode === 0 || this.currentSilenceMode === 2;
      }
      return true;
    },
  },
  watch: {
    showToast() {
      this.disappear();
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.acceptExt = '.JPG,.JPEG,.PNG,.GIF,.BMP';
    this.isLiveClass = TCIC.SDK.instance.isLiveClass();
    this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;

    const showJoinExit = store.get('showJoinExit');
    if (typeof showJoinExit === 'boolean') {
      TCIC.SDK.instance.setState(TCIC.TMainState.Join_Quit_Tips, showJoinExit);
    }
    const showHandsUp = store.get('showHandsUp');
    if (typeof showHandsUp === 'boolean') {
      this.showHandsUp = showHandsUp;
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
    }
    if (!this.canMemberHandup) {
      this.showHandsUp = false;
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
    }

    this.makeSureClassJoined(() => {
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
      this.showJoinExitSwitch = TCIC.SDK.instance.isFeatureAvailable('JoinExitSwitch');
      this.showHandsUpSwitch = TCIC.SDK.instance.isFeatureAvailable('HandsUpSwitch');
      this.enableMsgControl = TCIC.SDK.instance.isFeatureAvailable('MsgControl');
      this.studentShowJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('StudentShowJoinExitMsg');
      this.showJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('ShowJoinExitMsg');
      this.showTranslator = TCIC.SDK.instance.isFeatureAvailable('IMTranslator') && TCIC.SDK.instance.isOneOnOneClass();
      this.isBigRoom = TCIC.SDK.instance.isBigRoom();
      // 0411 所有课程都不显示进出开关，不显示举手开关
      this.showJoinExitSwitch = false;
      this.showHandsUpSwitch = false;
      // 如果不能上台， 不显示举手消息
      if (!this.canMemberStageUp) {
        this.showHandsUp = false;
        TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
      }

      this.$nextTick(() => {
        this.initEvents();
        if (this.isOneOnOneClass || this.isBigRoom) { // 1v1 或者大班课下 调整右侧区域
          this.checkRightSideHeight();
        }
      });
    });

    this.addLifecycleTCICEventListener(TCIC.TIMEvent.Set_Quick_IM_Words, (wordsArr) => {
      this.wordsArr = wordsArr;
    });

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.handleMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_IM_Msgs_Only_Message_List, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.handleMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Delete_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.deleteMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Update_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      // 更新至最新数据
      this.msgList = [];
      this.showMsgList = [];
      this.handleMsgs(TCIC.SDK.instance.getIMMsgList());
    });
    this.addLifecycleTCICStateListener(Constant.TStateCloseKeyboard, (value, oldValue) => {
      if (value) {
        this.showMobileKey = false;
        this.textIsEmpty = true;
      }
      TCIC.SDK.instance.setState(Constant.TStateCloseKeyboard, false);
      setTimeout(() => {
        this.$refs['openclass-msg-scroll'].wrap.scrollTop = this.$refs['openclass-msg-scroll'].wrap.scrollHeight;
      });
    });
    this.onComponentVisibilityChange(this.isVisible);

    this.addLifecycleTCICStateListener(Constant.TStateImgSendStatus, (postMsg) => {
      if (postMsg !== null && typeof postMsg === 'object') {
        this.handleImgSendState(postMsg, this.pasteFile !== null ? this.pasteFile : this.file);

        if (postMsg.imgState === MsgImageState.Success || postMsg.imgState === MsgImageState.Fail) {
          this.pasteFile = null;
          // window.URL.revokeObjectURL(this.imgUrl);
          const that = this;
          ImageCache.getImgBlobByXHR(this.imgUrl).then(
            (blob) => {
              // revokeObjectURL前，从本地文件缓存base64
              const index = this.msgList.findIndex(item => item.localImgSeq === postMsg.currentID);
              if (index >= 0) {
                const msg = that.msgList[index];
                ImageCache.cacheImage(msg.preview1 || msg.preview2, blob);
              }
              window.URL.revokeObjectURL(that.imgUrl);
            },
            () => {
              window.URL.revokeObjectURL(that.imgUrl);
            },
          );
        }

        // TODO MsgBase 里已经处理了，这里是不是应该去掉？
        const index = this.msgList.findIndex(item => item.localImgSeq === postMsg.currentID);
        if (index !== -1) {
          const msg = this.msgList[index];
          if (postMsg.currentID === msg.localImgSeq) {
            this.imgSendStatus = postMsg.imgState;
            if (postMsg.imgState === MsgImageState.Sending) {
              // sending
              this.per = postMsg.per;
              this.currentImgID = postMsg.currentID;
              if (String(this.per) === '100%') { // 修复传送状态，内容已完成还在加载的问题
                this.imgSendStatus = MsgImageState.Success;
              }
            }
          }
        }
      }
    });

    this.addLifecycleTCICStateListener(Constant.TStateFileSendStatus, (postMsg) => {
      if (postMsg !== null && typeof postMsg === 'object') {
        this.handleImgSendState(postMsg, this.pasteFile !== null ? this.pasteFile : this.file);

        if (postMsg.imgState === MsgImageState.Success || postMsg.imgState === MsgImageState.Fail) {
          this.pasteFile = null;
          const that = this;
          ImageCache.getImgBlobByXHR(this.imgUrl).then((blob) => {
            // revokeObjectURL前，从本地文件缓存base64
            const index = this.msgList.findIndex(item => item.localImgSeq === postMsg.currentID);
            if (index >= 0) {
              const msg = that.msgList[index];
              ImageCache.cacheImage(msg.preview1 || msg.preview2, blob);
            }
            window.URL.revokeObjectURL(that.imgUrl);
          }, () => {
            window.URL.revokeObjectURL(that.imgUrl);
          });
        }
      }
    });
    // 屏幕分享状态变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, this.onUpdateScreenShareState);

    const isSmallScreen = TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad();
    if (isSmallScreen) {
      // 是否开启连麦监听
      this.enableStage = TCIC.SDK.instance.getState(TCIC.TMainState.Enable_Stage, false);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
        console.log(`===>>> Discuss:Intro : Enable_Stage : ${enable}, ${this.enableStage}`);
        this.enableStage = enable;
        if (!this.enableStage) {
          TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
        }
      });
      // 上台相关的状态监听
      this.stageStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_Status, (status) => {
        if (this.stageStatus === status) {
          // 状态相同。先忽略
          return;
        }
        this.stageStatus = status;
        const deviceOrientation = TCIC.SDK.instance.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
        if (deviceOrientation === TCIC.TDeviceOrientation.Portrait) {
          switch (this.stageStatus) {
            case TCIC.TCommandStatus.Create: {
              this.stageDrawer = false;
              console.log(`===>>> : Discuss : 正在上台 : ${this.stageStatus}`);
              window.showToast(i18next.t('正在上台'));
              break;
            }
            case TCIC.TCommandStatus.Cancel: {
              window.showToast(i18next.t('上台超时'));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Reject: {
              window.showToast(i18next.t('{{arg_0}}拒绝了你的上台请求', { arg_0: this.roleInfo.teacher }));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Approve: {
              TCIC.SDK.instance
                .approveToStage({})
                .then(() => {
                  const isTeacherInviteMe = TCIC.SDK.instance.getState(TCIC.TMainState.Invite_Stage_Status, false);
                  if (!isTeacherInviteMe) {
                    window.showToast(i18next.t('{{arg_0}}同意了你的上台请求', { arg_0: this.roleInfo.teacher }));
                  }
                  TCIC.SDK.instance.updateComponent('stage-video-list-component', { display: 'block' }).then();
                })
                .catch(() => {
                  window.showToast(i18next.t('上台遇到问题'));
                  TCIC.SDK.instance.hangupToStage();
                });
              break;
            }
          }
        }
        console.log(`===>>> : Ask_Stage_Status : ${status}`);
      });

      this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
        this.isOnStage = status;
      });
    }

    /**
     * 如果有其它地方修改了配置，则同步响应，
     * @keroliang
     * 之前有个客户需求是需要在自定义 JS 里面调用 setState 来覆盖 Join_Quit_Tips 的默认值
     *  目前的业务代码里面应该没有其他地方会改这个状态
    *  后来那位客户放弃这个需求了，但考虑到这是一个良性的 bugfix 改动，所以相应的变更有保留下来
     */
    this.addLifecycleTCICStateListener(TCIC.TMainState.Join_Quit_Tips, (showJoinExit) => {
      this.showJoinExit = showJoinExit;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Hand_Up_Tips, (val) => {
      this.showHandsUp = val;
    });
    this.$EventBus.$on('toggle-translator', this.toggleTranslator);

    this.supportFileMessage = TCIC.SDK.instance.isFeatureAvailable('FileMessage');
  },
  beforeDestroy() {
    this.$EventBus.$off('toggle-translator', this.toggleTranslator);
    this.uninitEvents();
  },
  methods: {
    handleClickNewMessagesTip() {
      this.unreadMsgCount = 0;
      this.scrollToEnd();
    },
    handleReceiverUpdate(receiver) {
      this.currentPrivateChatReceiver = receiver;
    },
    disappear() {
      if (this.showToast) {
        setTimeout(() => {
          this.showToast = false;
        }, 3000);
      }
    },
    onComponentVisibilityChange(visible) {
      console.log(`Discuss.vue onComponentVisibilityChange: ${visible}......`);
      if (visible) {
        // 显示出来后直接标记所有消息为已读
        TCIC.SDK.instance.markAllMessagesAsRead();
        // 更新至最新数据
        this.msgList = [];
        this.showMsgList = [];
        this.handleMsgs(TCIC.SDK.instance.getIMMsgList());
        // 滑动到底部
        this.$nextTick(() => {
          this.scrollToEnd();
        });
      } else {
        if (!this.isMobile && this.$refs.popover) {
          // 隐藏时关掉表情弹窗
          this.$refs.popover.doClose();
          this.emojiClosed = true;
        }
      }
    },

    onInputFile(event) {
      const isSilenceAll = TCIC.SDK.instance.getState(TCIC.TMainState.Silence_Mode, TCIC.TClassSilenceMode.Free_Chat) === TCIC.TClassSilenceMode.All_Mute;
      const chatEnable = TCIC.SDK.instance.getState(TCIC.TMainState.Chat_Permission, true);
      if (isSilenceAll && this.isStudent) {
        window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }));
        event.preventDefault();
        return;
      }

      if (!chatEnable) {
        window.showToast(i18next.t('你已被禁言'));
        event.preventDefault();
        return;
      }
      if (!this.textIsEmpty && !this.imgSend && !this.fileSend) {
        window.showToast(i18next.t('请先发送文字消息，再发送文件'));
        event.preventDefault();
      } else if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前图片上传完成'));
        event.preventDefault();
        return;
      }
    },
    onInputAllKindsOfFile(event) {
      const isSilenceAll = TCIC.SDK.instance.getState(TCIC.TMainState.Silence_All, false);
      const chatEnable = TCIC.SDK.instance.getState(TCIC.TMainState.Chat_Permission, true);
      if (isSilenceAll && this.isStudent) {
        window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }));
        event.preventDefault();
        return;
      }

      if (!chatEnable) {
        window.showToast(i18next.t('你已被禁言'));
        event.preventDefault();
        return;
      }
      if (!this.textIsEmpty && !this.imgSend && !this.fileSend) {
        window.showToast(i18next.t('请先发送文字消息，再发送文件'));
        event.preventDefault();
      } else if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前消息上传完成'));
        event.preventDefault();
        return;
      }
    },

    onRecvMsg(msg) {
      if (this.isVisible) {
        // 组件可见时直接标记收到的消息为已读
        TCIC.SDK.instance.markMessageAsRead(msg.seq);
      }
    },

    switchShowJoinExit(value) {
      TCIC.SDK.instance.setState(TCIC.TMainState.Join_Quit_Tips, value);
      store.set('showJoinExit', value);
    },
    switchShowHandsUpMsg(value) {
      console.log(':::', value);
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, value);
      store.set('showHandsUp', value);
    },
    async textInputEnter() {
      // 巡课角色不允许发送消息
      if (this.isSupervisor) {
        return;
      }

      if (this.imgSend) {
        const judgeImageSizeRes = await this.judgeImageSize();
        if (!judgeImageSizeRes) {
          return;
        }
        if (this.pasteFile !== null) {
          this.sendImgMsg(this.pasteFile, this.imgUrl, null);
        } else {
          this.sendImgMsg(this.file, this.imgUrl, null);
        }
        this.$refs.editor.innerHTML = '';
        this.$refs.imagePicker.value = null;
        this.imgSend = false;
        this.textIsEmpty = true;
        // if (this.$refs.input.inputText.length > 0) {
        //   this.textIsEmpty = false;
        // }

        return;
      }

      if (this.fileSend) {
        if (!this.judgeFileSize()) {
          return;
        }
        if (this.pasteFile !== null) {
          this.sendFileMsg(this.pasteFile, this.imgUrl, null);
        } else {
          this.sendFileMsg(this.file, this.imgUrl, null);
        }
        this.$refs.editor.innerHTML = '';
        this.$refs.filePicker.value = null;
        this.fileSend = false;
        this.textIsEmpty = true;
        // this.$nextTick(() => {
        //   this.$refs.input.setText(this.imgInputText);
        //   if (this.$refs.input.inputText.length > 0) {
        //     this.textIsEmpty = false;
        //   }
        // });
        return;
      }
      // 输入空按发送实际不发送，并不提示
      if (this.$refs.input.getPureText() === '') {
        this.showMobileKey = false;
        this.textIsEmpty = true;
        // this.$refs.input.clearText();
        // window.showToast(i18next.t('发送消息不能为空'));
        return;
      }

      this.sendMsg(this.$refs.input.getPureText()).then(() => {
        this.showMobileKey = false;
        this.textIsEmpty = true;
        this.$refs.input.clearText();
      });
    },

    textChange() {
      this.inputText = this.$refs.input.inputText;
      if (this.inputText.length > 0) {
        this.textIsEmpty = false;
      } else {
        this.textIsEmpty = true;
      }
    },

    textInputBlur() {
      setTimeout(() => {
        this.$refs['openclass-msg-scroll'].wrap.scrollTop = this.$refs['openclass-msg-scroll'].wrap.scrollHeight;
      });
    },

    textInputClick() {
      if (this.isMobile) {
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component').getVueInstance()
          .showTextInput(this);
      }
    },

    sendEmoji(emojiId) {
      this.sendMsg(emojiId);
      this.$refs.popover && this.$refs.popover.doClose();
    },

    weakMobileKey() {
      this.showMobileKey = true;
      if (this.isMobile) {
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component').getVueInstance()
          .setOpenClassFlag(true);
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component').getVueInstance()
          .showTextInput(this);
      }
    },

    mobileTextChange() {
      if (this.input !== '') {
        this.textIsEmpty = false;
      } else {
        this.textIsEmpty = true;
      }
    },

    onBlur() {
      this.showMobileKey = false;
    },

    mobileSendMsg() {
      this.sendMsg(this.input).then(() => {
        this.showMobileKey = false;
        this.textIsEmpty = true;
        this.input = '';
      });
    },

    onSilenceAll() {
      this.switchAllSilence();
    },

    scrollToEnd() {
      this.$refs['openclass-msg-scroll'].wrap.scrollTop = this.$refs['openclass-msg-scroll'].wrap.scrollHeight;
    },

    async handleChange() {
      if (this.$refs.editor !== undefined && this.$refs.editor.childNodes.length === 1) {
        const myNode = document.getElementById('editor');
        myNode.removeChild(myNode.firstChild);
      }
      // this.file = document.getElementById('imagePicker');
      this.file = this.$refs.imagePicker;
      const size = TCIC.SDK.instance.isMac() ? (this.file.files[0].size / 1000 / 1000).toFixed(2) : (this.file.files[0].size / 1024 / 1024).toFixed(2);
      if (size >= 20) {
        window.showToast(i18next.t('选取的图片文件大小不能超过20M'), 'error');
        return false;
      }
      const isValidImage = await this.validateImageByLoading(this.file.files[0]);
      if (!isValidImage) {
        return false;
      }
      this.imgSend = true; // 设置后不应该retr
      // 显示到输入框
      this.$nextTick(() => {
        this.$refs.editor.focus();
        setTimeout(() => {
          this.$refs.editor.blur();
        }, 500);
      });
      window.URL.revokeObjectURL(this.imgUrl);
      this.imgUrl = window.URL.createObjectURL(this.file.files[0]);
      // window.URL.revokeObjectURL(this.imgUrl);
      this.chooseImg(this.file.files[0]);
      this.textIsEmpty = false;
      this.imagePickerIndex += 1;
      this.pasteFile = null; // 清空复制文件
    },

    handleSendingFileChange() {
      if (this.$refs.editor !== undefined
          && this.$refs.editor.childNodes.length === 1) {
        const myNode = document.getElementById('editor');
        myNode.removeChild(myNode.firstChild);
      }

      const checkRes = this.getAllKindOfFile(this.$refs.filePicker);
      if (!checkRes) {
        return false;
      }

      this.imgInputText = this.$refs.input.inputText;
      this.fileSend = true;
      // 显示到输入框
      this.$nextTick(() => {
        this.$refs.editor.focus();
        setTimeout(() => {
          this.$refs.editor.blur();
        }, 500);
      });
      this.chooseFile(this.file.files[0]);
      this.textIsEmpty = false;
      this.imagePickerIndex += 1;
      this.pasteFile = null;  // 清空复制文件
    },
    getAllKindOfFile(allKindOfFile) {
      console.log('sendFileMsg, inputRes', allKindOfFile);
      TCIC.SDK.instance.reportLog(
          'sendImgMsg',
          `getImageFile, inputRes.files.length ${allKindOfFile?.files?.length}, inputRes.name ${allKindOfFile?.name}, inputRes.size ${allKindOfFile?.size}`,
      );
      // 统一格式
      if (allKindOfFile?.files) {
        if (allKindOfFile?.files.length > 0) {
          this.file = allKindOfFile;
        } else if (allKindOfFile.name) {
          this.file = {
            files: [allKindOfFile],
          };
        } else {
          this.file = allKindOfFile;
        }
      } else {
        this.file = {
          files: [allKindOfFile],
        };
      }
      const singleFile = this.file.files[0];
      console.log('sendFileMsg, singleFile', singleFile);
      TCIC.SDK.instance.reportLog(
          'sendFileMsg',
          singleFile ? `singleFile, name ${singleFile?.name}, type ${singleFile?.type}, size ${singleFile?.size}` : 'singleFile, no file',
      );
      if (!singleFile) {
        // 没选文件不用提示
        return false;
      }
      // 加个校验，以防 accept 没生效
      const tmpIndex = singleFile.name.lastIndexOf('.');
      const fileType = tmpIndex >= 0 ? singleFile.name.slice(tmpIndex + 1) : '';
      const bytes = singleFile?.size;
      if (!bytes) {
        window.showToast(i18next.t('选取的文件不能为空'), 'error');
        return false;
      }
      const size = TCIC.SDK.instance.isMac()
          ? (bytes / 1000 / 1000).toFixed(2)
          : (bytes / 1024 / 1024).toFixed(2);
      if (size >= 100) {
        window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
        return false;
      }
      try {
        window.URL.revokeObjectURL(this.imgUrl);
      } catch (err) {
        // 注意这里不用 return false
        TCIC.SDK.instance.reportLog('sendFileMsg', `revokeObjectURL error, ${err?.message}`);
      }
      try {
        this.imgUrl = window.URL.createObjectURL(singleFile);
      } catch (err) {
        TCIC.SDK.instance.reportLog('sendFileMsg', `createObjectURL error, ${err?.message}`);
        return false;
      }
      return true;
    },

    imgTextChange() {
      if (this.$refs.editor.childNodes.length > 1) {
        this.showToast = true;
        // window.showToast(i18next.t('请先发送图片再编辑文字消息'));
        while (this.$refs.editor.childNodes.length > 1) {
          const myNode = document.getElementById('editor');
          myNode.removeChild(myNode.lastChild);
        }
        this.$refs.imagePicker.value = null;
        this.$refs.filePicker.value = null;
        return;
      }

      if (this.imgSend && this.$refs.editor.innerHTML.length === 0) {
        this.imgSend = false;
        this.textIsEmpty = true;
        this.$refs.imagePicker.value = null;
      }

      this.$nextTick(() => {
        if (this.fileSend && this.$refs.editor.innerHTML.length === 0) {
          this.fileSend = false;
          this.textIsEmpty = true;
          this.$refs.filePicker.value = null;
        }
      });
    },

    chooseImg(file) {
      const reader = new FileReader();
      const self = this;
      reader.onload = function () {
        const result = this.result;
        const imgRegx = /^data:image/;
        if (imgRegx.test(result)) {
          document.execCommand('insertHTML', false, `<img id="inputImg" src='${result}'>`);
          const imgBtn = document.getElementById('inputImg');
          if (imgBtn) {
            imgBtn.style = 'max-height:56px;max-width:100px;';
            imgBtn.onclick = () => {
              TCIC.SDK.instance.getComponent('image-preview').getVueInstance()
                .showWithUrl(self.imgUrl);
            };
          } else {
            TCIC.SDK.instance.reportLog('chooseImg', 'no inputImg btn');
          }
        }
      };
      reader.readAsDataURL(file);
    },
    chooseFile(file) {
      const reader = new FileReader();
      const that = this;
      reader.onload = function () {
        that.fileSend = true;
        that.showEditor = false;
        document.execCommand(
'insertHTML', false,
            `<div class="file-preview" contenteditable="false" id="inputFile">
<span class="file-name" contenteditable="false">${file.name}</span>
</div>`,
        );
        const fileDiv = document.getElementById('inputFile');
        fileDiv.addEventListener('keydown', (event) => {
          if (event.key === 'Backspace' || event.key === 'Delete') {
            event.preventDefault();
            fileDiv.remove();
          }
        });
      };
      reader.readAsDataURL(file);
    },
    // 图片消息
    onImageLoaded(event) {
      this.$refs['openclass-msg-scroll'].wrap.scrollTop = this.$refs['openclass-msg-scroll'].wrap.scrollHeight + 200;
    },
    async handleMobileChange() {
      this.file = document.getElementById('imagePicker');
      if (this.supportFileMessage) {
        const size = TCIC.SDK.instance.isMac() ? (this.file.files[0].size / 1000 / 1000).toFixed(2) : (this.file.files[0].size / 1024 / 1024).toFixed(2);
        if (size >= 100) {
          window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
          return false;
        }
        const checkValidateImageByLoading = await this.validateImageByLoadingWithoutToast(this.file.files[0]);
        if (checkValidateImageByLoading && size < 20) {
          this.sendImgMsg(this.file, this.imgUrl, null);
        } else {
          this.sendFileMsg(this.file, this.imgUrl, null);
        }
        // this.$refs.imagePicker.value = null;
        this.imagePickerIndex += 1;
      } else {
        const size = TCIC.SDK.instance.isMac() ? (this.file.files[0].size / 1000 / 1000).toFixed(2) : (this.file.files[0].size / 1024 / 1024).toFixed(2);
        if (size >= 20) {
          window.showToast(i18next.t('选取的图片文件大小不能超过20M'), 'error');
          return false;
        }
        const checkValidateImageByLoading = await this.validateImageByLoading(this.file.files[0]);
        if (!checkValidateImageByLoading) {
          return false;
        }
        window.URL.revokeObjectURL(this.imgUrl);
        this.imgUrl = window.URL.createObjectURL(this.file.files[0]);
        this.sendImgMsg(this.file, this.imgUrl, null);
        // this.$refs.imagePicker.value = null;
        this.imagePickerIndex += 1;
      }
    },

    async judgeImageSize() {
      // 判断图片大小
      let sizeFile = null;
      if (this.pasteFile !== null) {
        sizeFile = this.pasteFile;
      } else {
        sizeFile = this.file.files[0];
      }
      const size = TCIC.SDK.instance.isMac()
          ? (sizeFile.size / 1000 / 1000).toFixed(2)
          : (sizeFile.size / 1024 / 1024).toFixed(2);
      if (size >= 20) {
        window.showToast(i18next.t('图片文件大小不超过20M'), 'error');
        return false;
      }
      const isValidImage = await this.validateImageByLoading(sizeFile);
      return isValidImage;
    },
    judgeFileSize() {
      // 判断图片大小
      let sizeFile = null;
      if (this.pasteFile !== null) {
        sizeFile = this.pasteFile;
      } else {
        sizeFile = this.file.files[0];
      }
      const size = TCIC.SDK.instance.isMac()
          ? (sizeFile.size / 1000 / 1000).toFixed(2)
          : (sizeFile.size / 1024 / 1024).toFixed(2);
      if (size >= 100) {
        window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
        return false;
      }
      return true;
    },
    onInputPaste(event) {
      this.imgSend = true;
      this.textIsEmpty = false;
      this.$nextTick(() => {
        this.$refs.editor.focus();
        this.onPaste(event);
      });
    },

    async onPaste(e) {
      this.imgSend = true;
      this.pasteFile = null;
      const result = await this.doPaste(e);
    },

    doPaste(e) {
      if (!(e.clipboardData && e.clipboardData.items)) {
        return;
      }
      if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前消息上传完成'));
        event.preventDefault();
        return;
      }
      const clipboardData = e.clipboardData;
      if (clipboardData && clipboardData.files && clipboardData.files.length > 0) {
        // this.pasteFile2 = clipboardData.files[0];
      }
      return new Promise(async (resolve, reject) => {
        for (let i = 0, len = e.clipboardData.items.length; i < len; i++) {
          const item = e.clipboardData.items[i];
          if (item.kind === 'string') {
            item.getAsString((str) => {
              resolve(str);
            });
          } else if (item.kind === 'file') {
            this.pasteFile = item.getAsFile();

            if (this.pasteFile !== null && this.pasteFile !== undefined) {
              const fileType = this.pasteFile.type.split('/')[0];
              window.URL.revokeObjectURL(this.imgUrl);
              this.imgUrl = window.URL.createObjectURL(this.pasteFile);

              const isImage = fileType === 'image';
              const isValidImage = isImage && await this.validateImageByLoading(this.pasteFile);

              if (isValidImage) {
                this.imgSend = true;
                this.fileSend = false;
                this.chooseImg(this.pasteFile);
              } else {
                if (this.supportFileMessage) {
                  this.showEditor = true;
                  this.imgSend = false;
                  this.chooseFile(this.pasteFile);
                } else {
                  window.showToast(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件'), { type: 'error' });
                  reject(new Error(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件')));
                }
              }

              this.imagePickerIndex += 1;
            }
          } else {
            reject(new Error(i18next.t('复制失败')));
          }
        }
      });
    },
    onUpdateScreenShareState(flag) {
      if (TCIC.SDK.instance.isElectron()) {
        if (flag < 2) {
          // 进入屏幕分享
          this.isScreenShare = true;
        } else if (flag === 2) {
          // 停止屏幕分享
          this.isScreenShare = false;
        }
      }
    },
    onAskStage() {
      const isStudent = !(TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isSupervisor());
      if (!isStudent) {
        // 仅限学生操作
        return;
      }
      if (this.stageStatus === TCIC.TCommandStatus.None) {
        TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId()).then((userInfo) => {
          const req = new TCIC.TCommandReq();
          req.cmd = TCIC.TCommandID.Stage;
          // req.userId = item.userId; // 请求连麦不用填userId
          req.classId = TCIC.SDK.instance.getClassInfo().classId;
          req.type = TCIC.TCommandStatus.Create;

          // 自定义参数
          const customData = new TCIC.TStageCommandParam();
          customData.nickname = userInfo.nickname;
          customData.device = TCIC.SDK.instance.getDeviceType();
          req.param = customData;
          TCIC.SDK.instance
            .sendCommand(req)
            .then((result) => {
              // this.isAskStaging = true;
              console.log(`===>>> : ${userInfo.userId} :开始请求上台`);
            })
            .catch((error) => {
              // TODO: 从本地删除，并刷新
              // this.isAskStaging = false;
              window.showToast(error.errorMsg, 'error');
              console.log(`===>>> : askStage : ${userInfo.userId}`);
            });
        });
      }
    },
    onHangUpStage() {
      const isStudent = !(TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isSupervisor());
      if (!isStudent) {
        // 仅限学生操作
        return;
      }
      console.log('===>>> : 学生挂断 : trtc关麦克风/摄像头');
      // TODO : 调用下台接口
      TCIC.SDK.instance.hangupToStage({}).then(() => {
        TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
      });
    },
    handleMsgs(msgs) {
      if (!Array.isArray(msgs)) {
        return;
      }
      const currentUserId = TCIC.SDK.instance.getUserId();
      const filterList = msgs.filter((msg) => {
        // 学生 开启了显示进退房黑名单开关后
        // 不显示进退房Tips
        if (!this.showJoinExitMsg || (this.isStudent && !this.studentShowJoinExitMsg)) {
          return msg.msgType !== TCIC.TIMMsgType.JoinTips && msg.msgType !== TCIC.TIMMsgType.QuitTips;
        }
        return (!msg.dataExt?.IsPrivateMsg || (msg.dataExt?.PrivateInfo?.From?.ID === currentUserId || msg.dataExt?.PrivateInfo?.To?.ID === currentUserId));
      });
      if (filterList.length === 0) {
        return;
      }

      const scrollDom = this.$refs['openclass-msg-scroll'].wrap;
      const isAtBottom = scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 100;
      const navigateToBottom = isAtBottom || (msgs.length === 1 && msgs[0].from === currentUserId);

      this.appendMsgs(filterList);

      this.$nextTick(() => {
        if (navigateToBottom) {
          this.scrollToEnd();
        } else {
          this.unreadMsgCount += filterList.length;
        }
      });
    },

    initEvents() {
      const scrollDom = this.$refs['openclass-msg-scroll'].wrap;
      if (scrollDom) {
        scrollDom.addEventListener('scroll', this.handleScroll);
      }
    },
    uninitEvents() {
      const scrollDom = this.$refs['openclass-msg-scroll'].wrap;
      if (scrollDom) {
        scrollDom.removeEventListener('scroll', this.handleScroll);
      }
    },
    handleScroll() {
      const scrollDom = this.$refs['openclass-msg-scroll'].wrap;
      if (scrollDom && this.msgList.length > this.pageSize) {
        if (scrollDom.scrollHeight > scrollDom.clientHeight && scrollDom.scrollTop <= 200) {
          if (TCIC.SDK.instance.isIOS()) {
            this.inloading = this.showMsgList.length < this.msgList.length;
            // iOS需要用100ms定时器监听到滑动结束后，才处理加载事件
            if (this.scrollEndTimer) {
              clearTimeout(this.scrollEndTimer);
              this.scrollEndTimer = null;
            }
            this.scrollEndTimer = setTimeout(() => {
              this.handleScrollToTop();
            }, 100);
          } else {
            window.requestAnimationFrame(() => {
              this.handleScrollToTop();
            });
          }
        } else if (scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 10) {
          if (TCIC.SDK.instance.isIOS()) {
            if (this.scrollEndTimer) {
              clearTimeout(this.scrollEndTimer);
            }
            this.scrollEndTimer = setTimeout(() => {
              this.unreadMsgCount = 0;
              this.handleScrollBottom();
            }, 100);
          } else {
            window.requestAnimationFrame(() => {
              this.unreadMsgCount = 0;
              this.handleScrollBottom();
            });
          }
        }
        this.updateLastScrollHeight();
      } else if (scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 40) {
        this.scrollEndTimer = setTimeout(() => {
          this.unreadMsgCount = 0;
        }, 100);
      }
    },
    handleScrollToTop() {
      this.scrollEndTimer = null;
      const now = new Date().getTime();
      const canModify = now - this.lastModifyTime > 500;
      // 滑动到顶部时加载上一页，500ms内(取决于滑动窗口高度和滑动速度)只进行一次
      if (canModify) {
        this.showMsgCount += this.pageSize;
        this.lastModifyTime = now;
        if (this.msgList.length > this.showMsgCount) {
          this.showMsgList = this.msgList.slice(-this.showMsgCount);
        } else {
          this.showMsgList = this.msgList;
          this.inloading = false;
        }
        this.$nextTick(() => {
          this.updateLastScrollHeight();
        });
      }
    },
    handleScrollBottom() {
      // 滑动到底部后，showMsgCount缩小至最后一页
      const now = new Date().getTime();
      const canModify = now - this.lastModifyTime > 500;
      if (canModify && this.showMsgList.length > this.pageSize) {
        this.showMsgCount = this.pageSize;
        this.lastModifyTime = now;
        if (this.msgList.length > this.showMsgCount) {
          this.showMsgList = this.msgList.slice(-this.showMsgCount);
        } else {
          this.showMsgList = this.msgList;
        }
        this.$nextTick(() => {
          this.scrollToEnd();
        });
      }
    },
    updateLastScrollHeight() {
      const scrollDom = this.$refs['openclass-msg-scroll'].wrap;
      if (scrollDom) {
        if (scrollDom.scrollHeight > this.lastScrollHeight && scrollDom.scrollTop <= 200) {
          const addH = scrollDom.scrollHeight - this.lastScrollHeight;
          if (addH >= 200) {
            scrollDom.scrollTop += addH;
            this.inloading = false;
          }
        }
        this.lastScrollHeight = scrollDom.scrollHeight;
      }
    },
    toggleTranslator(active) {
      const msg = active ? i18next.t('翻译机器人已开启') : i18next.t('翻译机器人已关闭');
      window.showToast(msg);
    },
    // 发送快捷消息
    onSendQuickMsg(words) {
      // 只有学生才能发送快捷消息
      if (!this.isStudent) {
        return;
      }
      this.sendMsg(words);
    },
    checkRightSideHeight() {
      const h = window.innerHeight;
      const w = window.innerWidth;
      if (w > h && h < 900) {
        this.isRightSideHeightEnough = false;
      }
    },
  },
};
</script>
<style lang="less">
@import './openClassMobileEmoji.less';
// 表情弹出框
.emoji-popper {
  background: #fff !important;
  padding: 12px 16px !important;
  border-color: #fff !important;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05), 0 2px 7px 0 rgba(0, 0, 0, 0.1) !important;
  .popper__arrow {
    display: block !important;
    border-top-color: #fff !important;
    &:after {
      border-top-color: #fff !important;
    }
  }
  .emoji-popper-content {
    display: flex;
    justify-content: space-between;
    i {
      display: flex;
      width: 32px;
      height: 32px;
      opacity: 0.85;
      &:hover {
        opacity: 1;
        cursor: pointer;
      }
      //background: url('./assets/ic_emoji_all.svg') center no-repeat;
      //background-size: 226px;
      //filter: drop-shadow(0 2px 2px rgba(0,0,0,.15)); // 阴影
      &.ic-emoji-01 {
        background: url('./assets/ic_emoji_handclap.svg') center no-repeat;
        background-size: 32px;
        //background-position: -18px -13px;
      }
      &.ic-emoji-02 {
        background: url('./assets/ic_emoji_good.svg') center no-repeat;
        background-size: 32px;
        //background-position: -70px -13px;
      }
      &.ic-emoji-03 {
        background: url('./assets/ic_emoji_flower.svg') center no-repeat;
        background-size: 32px;
        //background-position: -122px -13px;
      }
      &.ic-emoji-04 {
        background: url('./assets/ic_emoji_heart.svg') center no-repeat;
        background-size: 32px;
        //background-position: -174px -13px;
      }
    }
  }
}

// 讨论区（包括输入框）
.curriculum-discuss {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 48px);
  background: #f0f3fa;
  .msg-scroll-wrapper {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .new-msg-tip-wrapper {
      position: absolute;
      right: 16px;
      bottom: 16px;
    }
  }
  .el-scrollbar {
    flex: 1;
    max-height: 100%;
    &__wrap {
      padding-top: 0.67rem;
      overflow-x: hidden;
    }
    &__bar {
      &.is-vertical {
        width: 3px;
      }
    }
  }
  .loading-text {
    text-align: center;
    color: #fff;
    line-height: 24px;
  }

  .el-scrollbar__wrap {
    overflow: scroll;
    height: 100% !important;
    overflow-x: hidden !important;
  }

  .discuss-footer {
    flex-shrink: 0;
    background: #fff;

    .file-preview {
      display: inline-block;
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 14px;
      margin: 8px 0;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      contenteditable: false !important;
    }

    .file-name {
      pointer-events: none;
      user-select: none;
      contenteditable: false !important;
    }

    .discuss-footer-ops {
      width: 100%;
      min-height: 64px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &.keyboard {
      display: flex;
      align-items: center;
      flex-direction: column;
      background: #d2d5db;
      .discuss-footer-keyboard {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        width: 100%;
        min-height: 64px;
        background: #e4e5ea;
      }
      .discuss-keyboard {
        height: 200px;
      }
      .discuss-input {
        margin: 0 12px 0 16px;
      }
    }
    .ic-emoji {
      display: flex;
      width: 36px;
      height: 36px;
      margin: 0 4px;
      opacity: 0.9;
      &:hover {
        cursor: pointer;
        opacity: 1;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }
    .discuss-input {
      position: relative;
      flex: 1;
      margin: 0 0 0 16px;
      .el-input__inner {
        height: 36px;
        padding-left: 36px;
        line-height: 36px;
        border-radius: 6px;
      }
    }
    .discuss-btn {
      width: 60px;
      height: 28px;
      padding: 0;
      margin-right: 16px;
      font-size: 0.73rem;
      &.is-disabled {
        background: #efefef !important;
        border: none;
      }
    }

    .mobile-img-msg {
      position: absolute;
      top: 0;
      left: 0;
      flex-shrink: 0;
      // 发送图片 - 手机端效果
      .discuss-img-tool {
        display: flex;
        width: 36px;
        height: 36px;
        cursor: pointer;
        background: url('./assets/ic_mobile_image.svg') center no-repeat;
        background-size: 20px;
        opacity: 0.9;
        &:hover {
          cursor: pointer;
          opacity: 1;
        }
      }

      .discuss-img-tool input {
        width: 36px !important;
        height: 36px !important;
        opacity: 0;
        cursor: pointer;
      }
    }

    // 移动端连麦入口
    .m-stage {
      display: flex;
      width: 36px;
      height: 36px;
      margin: 0 0 0 14px;
      .ic-m-stage {
        display: flex;
        width: 36px;
        height: 36px;
        background: url('./assets/ic_mobile_stage.svg') center no-repeat;
        background-size: 34px;
        &.approve {
          background: url('./assets/ic_mobile_stage_refuse.svg') center no-repeat;
        }
      }
      .ic-m-stage-dynamic {
        &.connecting {
          .ic-m-stage {
            height: 28px;
            background: url('./assets/ic_mobile_stage_connecting.svg') center no-repeat;
          }
          .m-stage-loading {
            span {
              background: #20c77a;
            }
          }
        }
        .ic-m-stage {
          height: 28px;
        }
        .m-stage-loading {
          display: flex;
          align-items: center;
          justify-content: space-around;
          padding: 2px 5px 0 5px;
          span {
            display: inline-block;
            width: 3px;
            height: 3px;
            margin: 0 1px;
            border-radius: 50%;
            background: #11c53c;
            animation: stageLoad 1s ease infinite;
            &:nth-child(1) {
              animation-delay: 0.25s;
            }
            &:nth-child(2) {
              width: 4px;
              height: 4px;
              animation-delay: 0.5s;
            }
            &:nth-child(3) {
              width: 5px;
              height: 5px;
              animation-delay: 0.75s;
            }
          }
        }
      }
    }
  }
}
@keyframes stageLoad {
  // 连麦中动态效果
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}
.m-stage-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(#000, 0.5);
  z-index: 99;
  .m-stage-drawer_list {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: #fff;
    border-radius: 9px 9px 0 0;
    animation: btt-drawer-in 0.3s;
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 68px;
      font-size: 16px;
      img {
        width: 34px;
        margin-right: 8px;
      }
      &:last-child {
        border-top: 1px solid #f4f4f4;
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
        background-color: #fff;
      }
    }
  }
}
.s {
}
</style>
