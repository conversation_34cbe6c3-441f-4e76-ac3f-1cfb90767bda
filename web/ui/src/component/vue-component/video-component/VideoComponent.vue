<template>
  <div
    ref="component"
    :class="['video-component',
             {'ios-layout' : isIOS},
             {'big-class-full-screen' : isFullScreen},
             {'big-class-mode' : isCollegeClass},
             {'no-border-radius' : !showBoardRadius},
             {'trans-style': !isWin32},
             {'mobile': isMobile}
    ]"
  >
    <!-- video close bg start /-->
    <div
      v-if="!isCoTeachingClass"
      :class="['video_close', { hidden: videoIconHidden || (!hasRendered && !isSelf) || (isSelf && !hasLocalRendered) }]"
    >
      <div
        :class="['icon', { 'avatar': bgImgUrl }]"
        :style="bgImgUrl ? { backgroundImage: `url(${bgImgUrl})` } : {}"
      />
    </div>
    <div
      v-if="isCoTeachingClass"
      class="ct_video_close"
    >
      <div
        class="icon"
      />
      <div>
        <span
          class="text"
        >{{ $t('视频已关闭') }}</span>
      </div>
    </div>
    <!-- video close bg end /-->
    <div
      ref="content"
      :class="{'no-border-radius': !showBoardRadius, 'video__content':true, }"
      :style="coverImgUrl ? {'background-image': `url(${coverImgUrl})`} : null"
      @touchstart="onTouchStart"
      @touchend="onTouchEnd"
      @dblclick="onDoubleClick"
      @click="onMobileClick"
    >
      <!--部分安卓机型创建的自定义组件不响应滚动事件,用这个占位保证用户能点到-->
      <div class="video-touch-container" />
      <div
        v-if="showVideoIconMask"
        class="video__mask"
        :style="[bgImgUrl ? `backgroundImage: url(${bgImgUrl})` : '']"
      />
      <div
        v-if="isShowResetInfo"
        class="video__reset"
      >
        <span class="reset_tips">
          {{ isResetLoading ? "loading" : "" }}
        </span>
        <span
          class="reset_btn"
          @click.stop.prevent="onResetRender"
        >
          {{ isResetLoading ? "" : $t('重新加载') }}
        </span>
      </div>
      <div
        v-if="showVideoIconMask"
        ref="nameicon"
        class="video__avatar"
        :style="[bgImgUrl ? `backgroundImage: url(${bgImgUrl})` : '']"
      />
      <div
        :id="'video-' + userId"
        ref="video"
        :data-dom-name="'user-' + userId + '-video-dom'"
        class="trtc__video"
        :class="{'no-video': isLiveClass && !isShowVideo, 'hide-video-view': hideVideoIfCameraNotOpen && !isCameraOpen }"
      />
      <div
        v-show="false"
        :id="'audio-' + userId"
        ref="audio"
        class="trtc__audio"
      />
      <div
        v-if="isCollegeClass && isHandUp"
        class="handup_status"
      >
        <i class="handup_icon" />
      </div>
      <div
        id="video__status"
        :class="['video__status', {
          'in-whiteboard' : isShowInWhiteBoard,
          'video__status-coteaching': isCoTeachingClass,
          'small-screen': isSmallScreen,
          'small-size': isVideoStatusBtnSmallSize
        }]"
        :style="isSmallMode ? 'transform:scale(0.8); transform-origin: left bottom;' : ''"
      >
        <i
          v-if="!isCollegeClass && !!userRole"
          :class="['video-role', (userRole === roleInfo.assistant) ? 'assistant' : 'teacher']"
        >
          <i
            v-if="isMobile"
            class="role-icon"
          />
          <span class="role-text">{{ userRole }}</span>
        </i>
        <div
          v-show="enableShowMicIcon && !isSmallMode"
          class="video__mic"
        >
          <template v-if="isMicOpen">
            <i class="video__volume_bk" />
            <i
              ref="volume"
              class="video__volume"
            />
          </template>
          <template v-else-if="isMicError">
            <i class="video__volume_error" />
          </template>
          <template v-else>
            <i class="video__volume_off" />
          </template>
        </div>
        <i
          v-show="enableShowNickname && !isSmallMode"
          class="video__name"
        >
          {{ nickname }}
        </i>
        <div
          v-if="isScreenOpen && !isSmallMode"
          class="video_screen"
        >
          <i class="video__screen" />
        </div>
        <NetStatusComponent
          v-if="networkQuality >= 3 && !isCollegeClass && !isSmallMode"
          :network-quality="networkQuality"
        />
        <!-- for debug -->
        <!-- <el-tooltip
          v-if="false"
          v-model="showDebugInfo"
          placement="bottom"
          effect="dark"
          :manual="true"
        >
          <div
            class="debug-info-switch"
            @touchstart.stop.prevent="showDebugInfo = !showDebugInfo"
            @mousedown.stop.prevent="showDebugInfo = !showDebugInfo"
          />
          <div slot="content">
            <div>{{ nickname }}</div>
            <div>{{ isSelf ? `self-${selfSeq}-${userId}` : `remote-${remoteSeq}-${userId}` }}</div>
            <div>mic: {{ isMicOpen ? isMicOpen : micDeviceStatus }}</div>
            <div>cam: {{ isCameraOpen ? isCameraOpen : cameraDeviceStatus }}</div>
            <div>render: {{ isSelf ? (isLocalRendering ? 'local' : 'local-none') : (isRendering ? `remote-${isRTCMode ? 'rtc' : 'live'}` : 'remote-none') }}</div>
            <div>elRect: {{ getRectStr(elementRect) }}</div>
            <div>videoRect: {{ getRectStr(videoDomRect) }}</div>
          </div>
        </el-tooltip> -->
      </div>
      <div
        v-if="linkedCount > 0 && isZh"
        :style="linkedCountLeftCSS"
        class="ct-link-count-wrap"
      >
        <div class="ct-link-count-icon" />
        <span
          class="ct-link-count"
        >{{ linkedCount }}</span>
      </div>

      <div
        v-if="isShowShareScreenTips"
        class="share-tips"
      >
        <div class="share-items">
          <div class="share-icon" />
          <span class="share-name">{{ screenShareTips }}</span>
        </div>
      </div>
      <div
        v-if="isMobile && (isControllerRole || !isSmallScreen)"
        :id="'video_shade_'+userId"
        class="video_content_shade"
      />
    </div>
    <div
      v-if="autoPlayVideo"
      class="video_auto_play"
      @click="autoPlayVideoHandler"
    >
      <div class="play_icon" />
    </div>
  </div>
</template>


<script>
import i18next from 'i18next';
import Lodash from 'lodash';
import Constant from '../../../util/Constant';
import DeviceUtil from '../../../util/DeviceUtil';
import Media from '../../../util/Media';
import Util from '../../../util/Util';
import WaterMark from '../../../util/WaterMark';
import BaseComponent from '../../core/BaseComponent';
import DetectUtil from '../device-detect-component/DetectUtil';
import NetStatusComponent from './NetStatusComponent.vue';
import Marquee from '@/util/Marquee';

let speakStartTime = 0; // 静音说话开始时间
let speakEndTime = 0; // 静音说话结束时间
let hasSpeakNotified = false; // 是否已经静音说话提示过

let selfSeq = 0;
let remoteSeq = 0;

export default {
  components: {
    NetStatusComponent,
  },
  extends: BaseComponent,
  props: {
    userRole: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
    isDisable: {
      type: Boolean,
      default: true,
    },
    networkQuality: {
      type: Number,
      default: 0,
    },
    enableShowMicIcon: {
      type: Boolean,
      default: true,
    },
    enableShowMicBorder: {
      type: Boolean,
      default: true,
    },
    enableShowNickname: {
      type: Boolean,
      default: true,
    },
    enableShowCtrlComponent: { // 点击时显示悬浮音视频控制栏
      type: Boolean,
      default: true,
    },
    linkedCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isDebugMode: process.env.NODE_ENV === 'development'
        || /^dev-/.test(location.hostname)
        || TCIC.SDK.instance.getParams('debug') === '1',
      showDebugInfo: false,
      selfSeq: 0,
      remoteSeq: 0,
      isDestroyed: false,
      isSelf: false,              // 是否是自己的视频
      isTeacher: false,           // 是否是老师
      isAssistant: false,         // 是否是助教
      isStudent: false,           // 是否为学生
      isSupervisor: false,        // 是否是巡课[审查员]
      isTeacherVideo: false,      // 当前是否老师视频
      isAssistantVideo: false,     // 当前是否助教视频
      isMobile: false,            // 移动端
      isMobileNative: false,      // 移动端Native
      isIOS: false,               // 是否iOS设备
      isAndroid: false,           // 是否安卓设备
      isMicOpen: false,           // 麦克风是否开启
      micDeviceStatus: null,      // 麦克风设备状态
      isMicOpenBeforeMuteAll: true,  // 全员静音前麦克风是否开启，退房重进后默认为true，保证取消全员静音时麦克风被打开
      isCameraOpen: false,        // 摄像头是否开启
      cameraDeviceStatus: null,   // 摄像头设备状态
      isCameraOpenBeforeMuteAll: true,  // 全员视频关闭前摄像头是否开启，退房重进后默认为true，保证打开全员视频时麦克风被打开
      isScreenShareOpen: false,   // 屏幕分享是否开启
      isScreenSharePaused: false, // 屏幕分享暂停中[由于切换屏幕或应用导致流暂停]
      isCameraOpenBeforeScreenShare: false,  // 记录屏幕分享前摄像头是否开启
      isMicEnable: false,         // 是否有麦克风权限[服务端下发/角色授权]
      isCameraEnable: false,      // 是否有摄像头权限[同上]
      isBoardEnable: false,       // 是否有白板操作权限
      isClassStarted: false,      // 是否已开始上课
      isOnStage: false,           // 是否已上台
      isHover: false,
      ctrlCom: null,              // 控制栏组件
      controlDirect: 'bottom',    // 控制栏位置
      isShowReset: false,         // 是否展示重置按钮
      nickname: '',
      bgImgUrl: '',               // 背景图片
      videoSequence: 0,           // 视频组件序列号
      isSmallMode: false,         // 是否缩小模式
      isVideoStatusBtnSmallSize: false,
      isPreviewMode: false,       // 是否预览模式
      touchTime: 0,   // 点击事件
      viewInited: false,    // 组件宽高完成初始化
      needRender: false,   // 渲染远端画面
      isShowVideo: true,    // 是否展示视频
      hasRendered: false,   // 远端是否有渲染过
      hasLocalRendered: false, // 本地是否有渲染过
      coverImgUrl: '',       // 视频水印
      audioFlag: false,  // 是否音频上课
      manualVideo: false, // 音频上课时手动打开
      isRecordMode: false,
      isCollegeClass: false,  // 是否大教学模式(大教学模式采用不同样式，关闭水印、悬浮控制栏等功能)
      isShowInWhiteBoard: false,    // 显示在白板区域模式(大教学时使用)
      isFullScreen: false, // 大教学模式下是否全屏
      singleClickDebounce: null,  // 大教学模式下有单击双击事件，双击触发后取消单击
      isLiveClass: false,  // 是否公开课
      isShowVodFooterCtrl: false, // 是否公开课PC/Pad底部控制栏
      showBoardRadius: true,
      showVideoIconMask: true,
      isShareScreenEnable: false, // 是否有屏幕共享的权限
      isShowShareScreenTips: false,
      screenShareTips: '',
      isRendering: false,   // 是否需要渲染
      isLocalRendering: false,  // 是否正在预览本地视频
      isPaused: false,   // 是否暂停播放
      elementRo: null,    // 监控尺寸变更
      elementRect: null,
      elementIo: null,    // 监控可见性变更
      isShowCtrlVideo: true, // 是否显示视频开启开关
      VideoQuestionIsAnswer: false, // 视频防重入多次提醒
      AudioQuestionIsAnswer: false, // 音频防重入多次提醒
      enterClassQuestionIsAnswer: false, // 重新链接RTC房间防止多重提醒
      micConfirmBoxId: -1,  // 学生音频开启前确认弹窗
      videoConfirmBoxId: -1,  // 学生视频开启前确认弹窗
      enterClassConfirmId: -1, // 学生视频RTC被挤出了房间，支持重新进入
      isScreenOpen: false,   // 正在屏幕共享
      isResetLoading: false,
      resetLoadTimer: null,
      hasVideoStream: false,    // 远端依赖TRTC视频流事件
      hasLocalState: false,     // 本地依赖后台下发的cameraState状态
      isCoTeachingClass: false, // 是否是双师课堂
      pauseVideoHide: true, // 组件不可见时暂停视频渲染
      isHandUp: false,
      isWin32: false,   // 是否Win32桌面端
      isElectron: false, // 是否为桌面端
      dbClickListener: null,    // 双击回调
      coTeachingVideoList: [],      // 双师课上行视频列表
      coTeachingPublishMyVideo: false,    // 双师课是否上行自己的视频
      isCoTeachingLinkUser: false,    // 当前是否双师课连麦用户
      isWeb: false,
      lastClickTime: 0, // 单击时间，用于模拟双击
      lastTaskId: -1,  // 最新任务id(避免乱序)
      isVideoWall: false, // 当前视频墙是否开启
      linkedCountLeftCSS: 'left: 0px;',
      linkCountResizeObserver: null,
      studentNumber: 0,
      // 这个是进入课堂前的设备选择的状态，应该只作用于页面首次打开和刷新后
      isLocalCamera: 1, // 本地开启摄像头
      isLocalMic: 1, // 本地开启麦克风开关
      isLocalSpeaker: 1, // 本地开启扬声器
      isPortrait: false,
      roleInfo: {},
      roomInfo: {},
      isVideoOnlyClass: false,
      isOneOnOneClass: false,
      isOneOnOneVideoClass: false,
      loadingCameraState: null, // 正在开启/关闭Camera
      loadingMicState: null, // 正在开启/关闭Mic
      permissionOperatorId: '',
      isTeacherOperator: true,
      recvOperator: false,
      enableDirectControl: 0,
      isBigRoom: false, // 是否大班课
      isRTCMode: true,
      remoteVideoStreamType: TCIC.TTrtcVideoStreamType.Big,
      videoDomRo: null,    // 监控 videoDom 尺寸变更
      videoDomRect: null,
      isMicError: false,
      videoIconHidden: false, // 隐藏video关闭图标
      autoPlayVideo: false,
      eventResume: () => {},
    };
  },
  computed: {
    shortUserId() {
      return this.userId ? `${this.userId.substr(0, 4)}***${this.userId.substr(-4)}` : '';
    },
    isShowResetInfo() {  // Android端(本地摄像头状态同步后，远端有视频流)显示重试引导，双师课不展示
      return !this.isCoTeachingClass && this.isCameraOpen && this.isAndroid && (this.hasLocalState || this.hasVideoStream) && this.isShowVideo;
    },
    isShowCtrl() {
      if (this.ctrlCom) {
        return this.ctrlCom.getVueInstance().getIsShow();
      }
      return false;
    },
    hideVideoIfCameraNotOpen() {
      return (this.isSelf && this.isMobileNative) || (!this.isSelf && this.isBigRoom);
    },
    isControllerRole() {
      return this.isTeacher || this.isAssistant;
    },
  },
  watch: {
    userId(newVal, oldVal) {
      if (newVal) {
        // 初始化组件
        this.init();
      }
    },
    enableShowCtrlComponent(newVal) {
      if (!newVal) {
        this.hideCtrlComponent();
      }
    },
    isMicOpen(value) {
      hasSpeakNotified = false;
      if (this.isSelf) {
        this.$EventBus.$emit('tabbar-mic-status', value);
      }
    },
    isCameraOpen(value) {
      if (this.isSelf) {
        this.$EventBus.$emit('tabbar-camera-status', value);
      }
      if (this.bgImgUrl) {
        this.videoIconHidden = value;
      }
    },
  },

  mounted() {
    // 注册状态
    TCIC.SDK.instance.registerState(Constant.TStateVideoCtrlUserId, '当前显示的视频悬浮窗用户id', '');
    this.isWeb = TCIC.SDK.instance.isWeb();
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isMobileNative = TCIC.SDK.instance.isMobileNative();
    this.isIOS = TCIC.SDK.instance.isIOS();
    this.isAndroid = TCIC.SDK.instance.isAndroid();
    this.isWin32 = TCIC.SDK.instance.isWin32Electron();
    this.isElectron = TCIC.SDK.instance.isElectron();
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.isLocalCamera = Number(TCIC.SDK.instance.getState(Constant.TStateOpenCamera));
    this.isLocalMic = Number(TCIC.SDK.instance.getState(Constant.TStateOpenMic));
    this.isLocalSpeaker = Number(TCIC.SDK.instance.getState(Constant.TStateOpenSpeaker));
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
    this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    this.isOneOnOneVideoClass = this.isOneOnOneClass && this.isVideoOnlyClass;
    this.stageUpOption = TCIC.SDK.instance.getStageMediaOption();
    // 监听音量回调
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, this.onTTrtcVolume);
    this.addLifecycleTCICEventListener(TCIC.TLiveEvent.Volume_Update, this.onTLiveVolume);
    // 监听退出回调
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Before_Leave, () => {
      this.videoIconHidden = true;
    });
    // 纯音频模式
    this.addLifecycleTCICStateListener(Constant.TStateAudioMode, this.onAudioMode);
    // 开关Camera/Mic的loading
    this.addLifecycleTCICEventListener(Constant.TStateLoadingCameraState, ({ value, userId, type }) => {
      if (userId === this.userId) {
        this.loadingCameraState = value?.loading ? value : null;
        // isCameraOpen 已经在 Video_Capture/Video_Publish 事件里更新了
      }
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState, ({ value, userId, type }) => {
      if (userId === this.userId) {
        this.loadingMicState = value?.loading ? value : null;
        // isMicOpen 已经在 Audio_Capture/Audio_Publish 事件里更新了
      }
    });
    if (this.userId) {   // 若有指定userId则开始初始化
      this.init();
    }
    if (!TCIC.SDK.instance.isCollegeClass()) {  // 大教学模式无需悬浮控制栏
      if (this.isMobile) {
        // 直接绑在dom上
      } else {
        this.$refs.content.addEventListener('mouseover', this.onMouseOver);
        this.$refs.content.addEventListener('mouseleave', this.onMouseLeave);
      }
    }
    // 监听水印更新
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {    // 录制不需要背景水印
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.WaterMark_Update, this.onWaterMarkUpdate);
      const waterParams = TCIC.SDK.instance.getWaterMarkParam();
      if (waterParams && waterParams.cover) {
        this.coverImgUrl = waterParams.cover;
      }
    }

    if (typeof window.ResizeObserver !== 'undefined') {
      this.elementRo = new ResizeObserver((entries) => {
        this.elementRect = entries[0].contentRect;
        this.onBoundingClientRectChange(entries[0].contentRect);
      });
      this.elementRo.observe(this.$el);
      this.videoDomRo = new ResizeObserver((entries) => {
        this.videoDomRect = entries[0].contentRect;
      });
      this.videoDomRo.observe(this.$refs.video);
    } else {
      TCIC.SDK.instance.reportLog('ResizeObserver', '[VideoComponent] not support');
    }
    if (typeof window.IntersectionObserver !== 'undefined') {
      this.elementIo = new IntersectionObserver((entries) => {
        this.onVisibleChanged(null);
      });
      this.elementIo.observe(this.$el);
    } else {
      TCIC.SDK.instance.reportLog('IntersectionObserver', '[VideoComponent] not support');
    }

    TCIC.SDK.instance.subscribeState(Constant.TStateVideoCtrlUserId, (userId) => {
      if (this.isCollegeClass && this.ctrlCom) {
        // 大教学中控制栏悬浮窗是单一组件，当显示的悬浮窗userid不属于自己，将ctrlCom置为空，防止updateCtrlStatus时将状态污染
        if (userId !== this.userId) {
          this.ctrlCom = null;
        }
      }
    });
    // 监听辅流变化
    if (this.isAndroid) {
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Video_Changed, (param) => {
        if (param.userId === this.userId) {
          this.hasVideoStream = !!param.available;
        }
      });
    }
    setTimeout(() => {
      this.addLinkCountObserver();
    }, 500);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
      if (this.isMobile && this.isTeacherVideo && !this.isSelf) {
        if (this.isPortrait) {
          let safeTop = 'env(safe-area-inset-top)';
          if (this.isIOS) {
            const versions = TCIC.SDK.instance.getIOSVersion();
            // evn在ios11.2以下的兼容性
            if (versions.length === 2
                      && parseInt(versions[0], 10) <= 11
                      && parseInt(versions[1], 10) <= 2) {
              safeTop = 'constant(safe-area-inset-top)';
            }
          } else if (this.isAndroid) {
            safeTop = '45px';
          }
        }
      }
    });
    // 处理videostatus 按钮
    if (TCIC.SDK.instance.isCoTeachingClass()) {   // 双师课堂
      this.setVideoStatusBtnSmallSize(false);
    } else if (TCIC.SDK.instance.isOneOnOneClass()) {   // 1v1课堂
      this.setVideoStatusBtnSmallSize(false);
    } else if (TCIC.SDK.instance.isVideoOnlyClass()) {   // 纯视频课堂
      this.setVideoStatusBtnSmallSize(false);
    } else if (TCIC.SDK.instance.isUnitedRTCClass() || TCIC.SDK.instance.isTeacherOrAssistant()) {
      // RTC课堂
      this.setVideoStatusBtnSmallSize(true);
    }
  },
  beforeDestroy() {
    this.isDestroyed = true;
    this.removeLinkCountObserver();
    if (this.ctrlCom) {
      TCIC.SDK.instance.removeComponent('video-ctrl-component', this.userId);
    }
    if (this.isSelf) {
      // 不再处理 CameraFoot 组件的消息
      this.$EventBus.$off('self-toggle-mic', this.toggleMic);
      this.$EventBus.$off('self-toggle-camera', this.toggleCamera);

      // 停止渲染
      this.isLocalRendering = false;
      TCIC.SDK.instance.reportLog('stopLocalVideo', '[VideoComponent] beforeDestroy');
      TCIC.SDK.instance.stopLocalVideo();
      TCIC.SDK.instance.reportLog('stopLocalAudio', '[VideoComponent] beforeDestroy');
      TCIC.SDK.instance.stopLocalAudio();
      const userId = TCIC.SDK.instance.getUserId();
      const myPermission = TCIC.SDK.instance.getPermission(userId);
      if (TCIC.SDK.instance.isInteractClass() && myPermission.screen === 3 && myPermission.screenState < 2) {
        // 互动班课
        // 正在使用辅助摄像头
      } else {
        TCIC.SDK.instance.reportLog('stopScreenShare', '[VideoComponent] beforeDestroy');
        TCIC.SDK.instance.stopScreenShare();
      }
      // 关闭对话框
      if (this.videoConfirmBoxId !== -1) {
        TCIC.SDK.instance.reportLog('confirmEnableCamera', `[VideoComponent] selfSeq ${this.selfSeq}, beforeDestroy closeMessageBox`);
        TCIC.SDK.instance.closeMessageBox(this.videoConfirmBoxId);
        this.videoConfirmBoxId = -1;
      }
      if (this.micConfirmBoxId !== -1) {
        TCIC.SDK.instance.reportLog('confirmEnableMic', `[VideoComponent] selfSeq ${this.selfSeq}, beforeDestroy closeMessageBox`);
        TCIC.SDK.instance.closeMessageBox(this.micConfirmBoxId);
        this.micConfirmBoxId = -1;
      }
    } else {
      this.isRendering = false;
      this.stopRenderVideo('beforeDestroy');
    }
    if (this.isMobile) {
      // 直接绑在dom上
    } else {
      this.$refs.content.removeEventListener('mouseover', this.onMouseOver);
      this.$refs.content.removeEventListener('mouseleave', this.onMouseLeave);
    }
    if (this.isCollegeClass && this.isTeacher && this.singleClickDebounce) {
      this.$refs.content.removeEventListener('click', this.singleClickDebounce);
    }
    this.elementIo?.disconnect();
    this.elementRo?.disconnect();
    this.videoDomRo?.disconnect();
  },
  methods: {
    autoPlayVideoHandler() {
      if (this.autoPlayVideo) {
        this.autoPlayVideo = false;
        this.eventResume();
      }
    },
    getRectStr(rect) {
      if (!rect) {
        return '';
      }
      return `(${rect.x}, ${rect.y}, ${rect.width}, ${rect.height})`;
    },
    // 暂停播放(没有显示)
    onPause() {
      if (this.isRendering) {
        console.log(`[VideoComponent][${this.userId}] onPause`);
        this.stopRenderVideo('onPause');
      }
    },
    // 继续播放
    onResume() {
      if (this.isRendering) {
        console.log(`[VideoComponent][${this.userId}] onResume`);
        this.startRenderVideo('onResume');
      }
    },
    // 暂停渲染(安卓端频繁修改渲染尺寸会异常，修改尺寸时需要暂停渲染)
    onPauseRender() {
      if (this.isLocalRendering) {
        TCIC.SDK.instance.pauseVideoRender(TCIC.SDK.instance.getUserId(), TCIC.TTrtcVideoStreamType.Big);
      } else if (this.isRendering) {
        TCIC.SDK.instance.pauseVideoRender(this.userId, this.remoteVideoStreamType);
      }
    },
    onResumeRender() {
      if (this.isLocalRendering) {
        TCIC.SDK.instance.resumeVideoRender(TCIC.SDK.instance.getUserId(), TCIC.TTrtcVideoStreamType.Big);
      } else if (this.isRendering) {
        TCIC.SDK.instance.resumeVideoRender(this.userId, this.remoteVideoStreamType);
      }
    },
    onResetRender() {
      this.isResetLoading = true;
      if (this.resetLoadTimer) {
        window.clearTimeout(this.resetLoadTimer);
      }
      this.resetLoadTimer = window.setTimeout(() => {
        this.isResetLoading = false;
        this.resetLoadTimer = null;
      }, 1000);
      if (this.isLocalRendering) {
        TCIC.SDK.instance.resetVideoRender(TCIC.SDK.instance.getUserId(), TCIC.TTrtcVideoStreamType.Big);
      } else if (this.isRendering) {
        TCIC.SDK.instance.resetVideoRender(this.userId, this.remoteVideoStreamType);
      }
    },
    onClassStarted() {
      // 监听自定义消息移到 class.js
    },
    // 设置缩小模式
    enableSmallMode(flag) {
      this.isSmallMode = flag;
    },
    updateControl(direct) {
      // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] updateControl, direct ${direct}, isHover ${this.isHover}`);
      this.controlDirect = direct;
      if (this.isHover) {
        if (direct === 'hide') {
          this.hideCtrlComponent();
        } else {
          this.showCtrlComponent();
        }
      } else {  // 更新时不需要展示则先隐藏
        this.hideCtrlComponent();
      }
    },
    updateShowReset(flag) {
      // console.log("VideoComponent::updateShowReset=>", flag, this.isShowReset);
      this.isShowReset = flag;
      if (this.ctrlCom) {
        this.ctrlCom.getVueInstance().updateEnableReset(flag);
      }
    },
    init() {
      // 教师ID等信息需要确保进房后才能拿到
      this.makeSureClassJoined(() => {
        console.log(`VideoComponent::initInfo=>userId: ${this.userId}`);
        const classInfo = TCIC.SDK.instance.getClassInfo();
        this.isSelf = this.userId === TCIC.SDK.instance.getUserId();
        if (this.isSelf) {
          // eslint-disable-next-line no-plusplus
          selfSeq++;
          this.selfSeq = selfSeq;
        } else {
          // eslint-disable-next-line no-plusplus
          remoteSeq++;
          this.remoteSeq = remoteSeq;
        }
        this.isTeacher = TCIC.SDK.instance.isTeacher();
        this.isAssistant = TCIC.SDK.instance.isAssistant();
        this.isStudent = TCIC.SDK.instance.isStudent();
        this.isSupervisor = TCIC.SDK.instance.isSupervisor();
        this.isTeacherVideo = this.userId === classInfo.teacherId;
        this.isAssistantVideo = classInfo.assistants.includes(this.userId);
        this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();
        this.isLiveClass = TCIC.SDK.instance.isLiveClass();
        this.isCoTeachingClass = TCIC.SDK.instance.isCoTeachingClass();
        this.isBigRoom = TCIC.SDK.instance.isBigRoom();
        this.showVideoIconMask = this.isCollegeClass;
        this.enableDirectControl = classInfo.enableDirectControl;

        // isRTCMode/remoteVideoStreamType，仅用来显示调试信息，切换流的具体逻辑在TAV
        this.isRTCMode = TCIC.SDK.instance.getState(TCIC.TMainState.RTC_Mode, true);
        this.remoteVideoStreamType = this.isRTCMode ? TCIC.TTrtcVideoStreamType.Big : TCIC.TTrtcVideoStreamType.QLive;
        this.addLifecycleTCICStateListener(TCIC.TMainState.RTC_Mode, (isRTCMode) => {
          this.isRTCMode = isRTCMode;
          this.remoteVideoStreamType = this.isRTCMode ? TCIC.TTrtcVideoStreamType.Big : TCIC.TTrtcVideoStreamType.QLive;
        });

        this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, (isBigVideoModel) => {
          const isLayoutHasDoc = TCIC.SDK.instance.isClassLayoutHasDoc();
          if (this.isLiveClass && this.isTeacher && !this.isTeacherVideo && isLayoutHasDoc) {
            // 公开课老师端文档+视频布局，白板和老师视频互换位置时，需将最大化显示的视频还原
            const videoDom = this.$el.parentElement.parentElement;
            // 当前视频已经最大化, 需还原
            if (videoDom && videoDom.getAttribute('drag') === 'true') {
              this.toggleMaxVideo();
            }
          }
          if (this.isLiveClass && this.isTeacherVideo && isBigVideoModel) {
            // 公开课手机app上老师视频, 大视频布局时不展示圆角
            this.showBoardRadius = false;
            // 切换至大视频布局时，隐藏视频控制器
            this.hideCtrlComponent();
          } else if (!isLayoutHasDoc && !this.isSmallScreen) {
            // 视频墙时不展示圆角
            this.showBoardRadius = false;
          } else {
            this.showBoardRadius = true;
          }
        });
        // 大教学模式老师通过点击打开悬浮控制栏
        if (this.isCollegeClass && this.isTeacher) {
          // 200ms后响应onClick，在此期间doubleclick会取消onClick的调用，减少悬浮控制栏闪烁
          this.singleClickDebounce = Lodash.debounce(this.onClick, 200);
          this.$refs.content.addEventListener('click', this.singleClickDebounce);

          this.addLifecycleTCICStateListener(Constant.TStateCollClassVodFullScreen, (userId) => {
            this.isFullScreen = userId === this.userId;
            if (this.ctrlCom) {
              this.ctrlCom.getVueInstance().updateFullScreen(this.isFullScreen);
            }
            if (this.isFullScreen) {
            // 获取当前具有权限的成员列表
              this.isShowShareScreenTips = false;
              this.isScreenOpen = false;
              const permissionMembers = TCIC.SDK.instance.getPermissionList();
              const userPermission = permissionMembers.find(permission => permission.userId === userId);
              // 大教学模式逻辑
              if (this.isCollegeClass
              && userPermission.screen === 1
              && userPermission.screenState < 2
              && userId !== TCIC.SDK.instance.getClassInfo().teacherId) {
                this.screenShareTips = i18next.t('来自 {{arg_0}} 的屏幕共享', { arg_0: this.nickname });
                this.isShowShareScreenTips = true;
                this.isScreenOpen = true;
              }
            } else {
              this.isShowShareScreenTips = false;
              this.isScreenOpen = false;
            }
          });
          const filter = {
            page: 1,
            limit: 30,
          };
          TCIC.SDK.instance.getHandUpList(filter).then((handupListResult) => {
            const handupMembers = handupListResult.members;
            this.isHandUp = !!handupMembers.find(member => member.userId === this.userId);
          });
          this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Hand_Up, ({ userId }) => {
            if (userId === this.userId) {
              this.isHandUp = true;
            }
          });

          this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Hand_Up_Cancel, ({ userId }) => {
            if (userId === this.userId) {
              this.isHandUp = false;
            }
          });
        }

        /*
        * 设备检测事件通知
        * */
        TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
          // 权限变更事件监听
          this.permissionChangeHandler();

          // 大教学老师端未开始上课开启本地视频、麦克风
          if (this.isCollegeClass) {
            this.initCollegeClass();
          }
        });

        // 远端流监听音频状态变化
        // if (!this.isSelf) {
        //   this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Audio_Changed, (info) => {
        //     if (info.userId === this.userId) {  // 更新状态
        //       this.updatePermission(TCIC.SDK.instance.getPermission(this.userId));
        //     }
        //   });
        // }

        // 本地流监听屏幕分享状态变化
        this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (status) => {
          this.isScreenShareOpen = status < 2;
          this.isScreenSharePaused = status === 1;
          if (status === 2 && this.isCameraOpenBeforeScreenShare && this.isWeb) {
            this.isLocalRendering = true;
            TCIC.SDK.instance.reportLog('startLocalVideo', `[VideoComponent] on Screen_Share ${status}`);
            TCIC.SDK.instance.startLocalVideo(this.$refs.video);
          }
          this.updateCtrlStatus();
        });
        // 获取昵称信息
        this.updateNick();

        // 初始化音视频
        this.initAV();
        // 开始上课后再开始推拉流
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
          .then(async () => {
            this.isClassStarted = true;
            if (this.isSelf) {
              // 只有移动端做特殊处理，其它端已改成上课后再进房
              if (this.isMobile) {
                if (this.isLocalCamera) {
                  TCIC.SDK.instance.muteLocalVideo(false);
                }
                if (this.isLocalMic) {
                  TCIC.SDK.instance.muteLocalAudio(false);
                }
                if (this.isLocalSpeaker) {
                  TCIC.SDK.instance.getSpeakerVolume().then(async (volume) => {
                    await DetectUtil.setSpeakerVolume(volume);
                  });
                }
              }
            } else {
              const audioMode = TCIC.SDK.instance.getState(Constant.TStateAudioMode, false);
              console.log(`VideoComponent::bindRemoteVideoDom=>userId: ${this.userId}/${audioMode}`);
              if (!audioMode || TCIC.SDK.instance.isLiveClass()) {   // 公开课或非纯音视频模式才去流视频流
                this.isRendering = true;
                this.startRenderVideo('classStarted');
              }
            }
            this.onClassStarted();
            this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
              this.renderMarquee();
            });
            this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, isBigVideoMode => this.renderMarquee());
          });

        // 课前预览音视频
        // eslint-disable-next-line max-len
        const isClassStart = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
        const isPreviewVideo = TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin);
        const userPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
        // 【学生】未上课且不在台上，且需要课前预览 则加载自己视频组件
        if (this.isSelf && !isClassStart && isPreviewVideo && !userPermission.stage && !TCIC.SDK.instance.isTeacher()) {
          this.isPreviewMode = true;
          this.enableCamera(this.isLocalCamera);
          this.enableMic(this.isLocalMic);
          // 上课后移除
          TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
            .then(() => {
              this.isPreviewMode = false;
              this.enableCamera(false);
              this.enableMic(false);
            });
        }
        // 大教学学生端老师视频监听老师视频位置
        if (this.isCollegeClass && !this.isTeacher && this.isTeacherVideo) {
          TCIC.SDK.instance.subscribeState(Constant.TStateShowTeacherVideoInWhiteBoardArea, (isShowInBoard) => {
            this.isShowInWhiteBoard = isShowInBoard;
            // 大教学学生端老师视频显示在白板时不显示圆角
            this.showBoardRadius = !isShowInBoard;
          });
        }

        // 双师课初始化上行状态
        if (TCIC.SDK.instance.isCoTeachingClass()) {
          this.initCoTechingClass();
        }

        // 老师端监听视频墙状态
        if (this.isTeacherVideo || this.isAssistantVideo) {
          this.addLifecycleTCICStateListener(Constant.TStateVideoWallMode, (flag) => {
            this.isVideoWall = flag;
            this.updateCtrlStatus();
          });
          this.addLifecycleTCICStateListener(Constant.TStateVodPlayerVisible, (flag) => {
            this.updateCtrlStatus();
          });
        }
        this.addLifecycleTCICStateListener(TCIC.TMainState.Mute_All, (muteAll, oldStatus) => {
          if (this.isOneOnOneClass) {
            if ((this.isTeacher || this.isAssistant) && this.isSelf) {
              if (muteAll) {
                window.showToast(i18next.t('已开启全员静音'));
              } else {
                window.showToast(i18next.t('已解除全员静音'));
              }
            } else if (this.isStudent && !this.isTeacherVideo) {
              if (muteAll) {
                window.showToast(i18next.t('{{arg_0}}已开启全员静音', { arg_0: this.roleInfo.teacher }));
              } else {
                window.showToast(i18next.t('{{arg_0}}已解除全员静音', { arg_0: this.roleInfo.teacher }));
              }
            }
          }
        }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

        // 处理 CameraFoot 组件的消息，不用一层层传参数
        if (this.isSelf) {
          this.$EventBus.$on('self-toggle-mic', this.toggleMic);
          this.$EventBus.$on('self-toggle-camera', this.toggleCamera);
        }

        TCIC.SDK.instance.on(TCIC.TMainEvent.WaterMark_Update, this.renderWaterMark);
        TCIC.SDK.instance.on(TCIC.TMainEvent.Marquee_Update, this.renderMarquee);
        TCIC.SDK.instance.on(TCIC.TTrtcEvent.AUTOPLAY_FAILED, this.autoPlayFiledHandler);
        TCIC.SDK.instance.on(TCIC.TTrtcEvent.AUTOPLAY_CONFIRM, this.autoPlayConfirmHandler);
      });
    },
    autoPlayFiledHandler(result) {
      if (this.userId === result.userId) {
        this.autoPlayVideo = true;
        this.eventResume = result.resume;
      }
    },
    autoPlayConfirmHandler(result) {
      if (this.autoPlayVideo) {
        this.autoPlayVideo = false;
        this.eventResume();
      }
    },
    // 获取昵称信息
    updateNick() {
      TCIC.SDK.instance.getUserInfo(this.userId)
        .then((rsp) => {
          if (rsp && rsp.nickname) {
            this.nickname = rsp.nickname;
            this.bgImgUrl = rsp.avatar;
            if (this.bgImgUrl.length < 1) {
              if (this.$refs.nameicon) {
                this.$refs.nameicon.textContent = this.nickname[0];
              }
            }
          } else {
            this.nickname = this.userId;
          }
        })
        .catch((err) => {
          this.nickname = this.userId;
          setTimeout(() => {  // 2秒后重试
            this.updateNick();
          }, 2000);
        });
    },
    async initAV() { // 初始化音视频
      console.log(`VideoComponent::initVideo=>userId: ${this.userId}/${this.isSelf}`);
      if (this.isSelf && this.isMobile) {
        // 先不对外推流
        // 注意：移动端如果默认静音进房的话，不推流，后面打不开声音, 这里要注释掉
        // TCIC.SDK.instance.muteLocalVideo(true);
        // TCIC.SDK.instance.muteLocalAudio(true);
        // TCIC.SDK.instance.getSpeakerVolume().then(async (volume) => {
        //   await DetectUtil.setSpeakerVolume(volume);
        // });
      }
      // this.updatePermissionList(TCIC.SDK.instance.getPermissionList());

      if (this.isSelf) {
        // this.isMicOpen = this.micVolume > 0;
        this.updateCtrlStatus();
        if (!this.isMicOpen) {
          this.$el.classList.remove('focus');
        }
        this.isCameraOpen = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Capture);
        console.log('[VideoComponent] initAV, isCameraOpen', this.isCameraOpen);
        // 上课前视频采集结果更新camera
        this.addLifecycleTCICStateListener(TCIC.TMainState.Video_Capture, (flag) => {
          if (!this.isClassStarted) {
            this.isCameraOpen = flag;
            console.log(`[VideoComponent] Video_Capture change, isClassStarted ${this.isClassStarted}, isCameraOpen ${this.isCameraOpen}`);
            this.loadingCameraState = null;
            DeviceUtil.notifyDeviceLoadingEvent(
              this.userId,
              'camera',
              {
                loading: false,
                open: flag,
              },
              `VideoComponent-VideoCaptureState-${flag}`,
            );
            this.updateCtrlStatus();
          }
        });
        // 上课后视频推流结果再更新camera
        this.addLifecycleTCICStateListener(TCIC.TMainState.Video_Publish, (flag) => {
          this.isCameraOpen = flag;
          console.log(`[VideoComponent] Video_Publish change, isClassStarted ${this.isClassStarted}, isCameraOpen ${this.isCameraOpen}`);
          this.loadingCameraState = null;
          DeviceUtil.notifyDeviceLoadingEvent(
            this.userId,
            'camera',
            {
              loading: false,
              open: flag,
            },
            `VideoComponent-VideoPublishState-${flag}`,
          );
          this.updateCtrlStatus();
        });
        // 视频设备状态
        this.addLifecycleTCICStateListener(TCIC.TMainState.Video_Device_Status, (deviceStatus) => {
          this.cameraDeviceStatus = deviceStatus;
          console.log(`[VideoComponent] Video_Device_Status change, cameraDeviceStatus ${this.cameraDeviceStatus}`);
          this.updateCtrlStatus();
        });

        // 上课前本地音频采集结果更新mic
        this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Capture, (flag) => {
          if (!this.isClassStarted) {
            this.isMicOpen = flag;
            console.log(`[VideoComponent] Audio_Capture change, isClassStarted ${this.isClassStarted}, isMicOpen ${this.isMicOpen}`);
            this.loadingMicState = null;
            DeviceUtil.notifyDeviceLoadingEvent(
              this.userId,
              'mic',
              {
                loading: false,
                open: flag,
              },
              `VideoComponent-AudioCaptureState-${flag}`,
            );
            this.updateCtrlStatus();
          }
        });
        // 上课后音频推流结果再更新mic
        this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Publish, (flag) => {
          this.isMicOpen = flag;
          console.log(`[VideoComponent] Audio_Publish change, isClassStarted ${this.isClassStarted}, isMicOpen ${this.isMicOpen}`);
          this.loadingMicState = null;
          DeviceUtil.notifyDeviceLoadingEvent(
            this.userId,
            'mic',
            {
              loading: false,
              open: flag,
            },
            `VideoComponent-AudioPublishState-${flag}`,
          );
          this.updateCtrlStatus();
        });
        // 音频设备状态
        this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Device_Status, (deviceStatus) => {
          this.micDeviceStatus = deviceStatus;
          console.log(`[VideoComponent] Audio_Device_Status change, micDeviceStatus ${this.micDeviceStatus}`);
          this.updateCtrlStatus();
        });
      }
    },
    // 麦克风动画
    updateVolumeAnimation(volume) {
      requestAnimationFrame(() => {
        if (this.enableShowMicIcon && this.enableShowMicBorder && volume > 2) {
          this.$el.classList.add('focus');
        } else {
          this.$el.classList.remove('focus');
        }
        if (this.$refs.volume) {
          // 利用sin函数对音量做非线性变换，让低音量的时候变换幅度更明显，做归一化处理，音量放大2倍显示
          const volumeFin = Media.amplifier(volume, 20);
          // 底部1像素不可见
          this.$refs.volume.style.clip = `rect(${Math.ceil(16 - (1 + volumeFin))}px, 16px, 16px, 0px)`;
        }
      });
    },
    onTTrtcVolume(info) {
      if (info.userId !== this.userId) return;
      if (this.isMicOpen) {
        this.updateVolumeAnimation(info.volume);
      } else {
        // 关麦说话持续1s进行提示
        if (!this.isSelf) return;
        if (!this.isClassStarted) return;
        if (hasSpeakNotified) return; // 关麦后只提示一次
        if (info.volume > 2) {
          if (speakStartTime === 0) speakStartTime = +new Date();
          else {
            speakEndTime = +new Date();
            const speakTime = speakEndTime - speakStartTime;
            if (speakTime < 1000) return;
            this.$message({
              message: i18next.t('检测到您正在说话，目前您正处于静音状态，如需要请切换到非静音'),
              duration: 3000,
              type: 'warning',
              showClose: false,
              offset: 48,
            });
            hasSpeakNotified = true;
          }
        } else {
          speakStartTime = 0;
        }
      }
    },
    onTLiveVolume(info) {
      if (info.userId === this.userId && this.isMicOpen) {
        this.updateVolumeAnimation(info.volume);
      }
    },
    // 纯音频课堂
    onAudioMode(audioMode) {
      if (audioMode) {    // 关闭视频功能
        this.manualVideo = false;
        if (this.isSelf) {
          this.isLocalRendering = false;
          TCIC.SDK.instance.reportLog('stopLocalVideo', `[VideoComponent] onAudioMode ${audioMode}`);
          TCIC.SDK.instance.stopLocalVideo();
        } else {
          this.isRendering = false;
          this.stopRenderVideo(`onAudioMode ${audioMode}`);
        }
        this.audioFlag = true;
      } else {    // 关闭音频上课 恢复到之前的状态
        this.audioFlag = false;
        const permissionLastList = TCIC.SDK.instance.getPermissionList();
        for (let i = 0; i < permissionLastList.length; i++) {
          if (permissionLastList[i].userId !== this.userId) {
            continue;
          }
          if (permissionLastList[i].camera) {
            if (this.isSelf) {
              this.isLocalRendering = true;
              TCIC.SDK.instance.reportLog('startLocalVideo', `[VideoComponent] onAudioMode ${audioMode}, has camera permission`);
              TCIC.SDK.instance.startLocalVideo(this.$refs.video);
            } else if (permissionLastList[i].cameraState === TCIC.TDeviceStatus.Open) {
              this.isRendering = true;
              this.startRenderVideo(`onAudioMode ${audioMode}, has camera permission and camera open`);
            }
          } else {
            if (this.isSelf) {
              this.isLocalRendering = true;
              TCIC.SDK.instance.reportLog('stopLocalVideo', `[VideoComponent] onAudioMode ${audioMode}, no camera permission`);
              TCIC.SDK.instance.stopLocalVideo();
            } else {
              this.isRendering = false;
              this.stopRenderVideo(`onAudioMode ${audioMode}, no camera permission`);
            }
          }
        }
      }
    },
    onDoubleClick(check = true) {
      if (check && this.isMobile) return;    // 移动端忽略原生双击事件
      if (this.dbClickListener && this.dbClickListener()) {   // 有设置双击回调且回调返回true则忽略后续处理
        return;
      }
      // 大教学下双击学生视频，可以放大学生视频的图像，用于屏幕共享模式
      if (this.isCollegeClass) {
        const layout = TCIC.SDK.instance.getState(Constant.TStateCollegeVideoLayout);
        if (layout === Constant.TConstantCollegeLayoutNormal) {
          const userId = this.userId;
          const wrap = TCIC.SDK.instance.getComponent('teacher-videowrap-component');
          wrap && wrap.getVueInstance().toggleFullScreen(userId);
          if (this.singleClickDebounce) {
            this.singleClickDebounce.cancel();
          }
        }
        return;
      }
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
      const isBigVideoMode = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
      if ((this.isLiveClass && !TCIC.SDK.instance.isClassLayoutHasDoc())
        || (this.isLiveClass && this.isTeacherVideo)
        // || ((TCIC.SDK.instance.isInteractClass() || TCIC.SDK.instance.isUnitedClass()) && classLayout === TCIC.TClassLayout.Three && this.isTeacherVideo)
      ) {
        // - 公开课纯视频布局下、所有视频不能双击
        // - 公开课不能双击老师的视频
        // - 互动班课，统一课堂三分屏布局时，老师的视频不能双击
        return;
      }
      if (TCIC.SDK.instance.isInteractClass() && isBigVideoMode && classLayout === TCIC.TClassLayout.Three  && !this.isTeacherVideo) {
        // 互动班课三分屏布局大视频模式下，双击学生视频时不响应
        console.log('onDoubleClick, isBigVideoMode', classLayout);
        return;
      }

      if (!this.isMobile) {  // 移动端(主要是Android)双击事件有问题
        this.toggleMaxVideo();
      }
    },
    onMobileClick() {
      if (!this.isMobile || (this.isSmallScreen && this.isTeacher)) return;    // 非移动端忽略
      const nowTime = new Date().getTime();
      if (nowTime - this.lastClickTime < 500) {
        this.onDoubleClick(false);
        return;
      }
      this.lastClickTime = nowTime;
    },
    onWaterMarkUpdate(params) {   // 水印更新
      if (params && params.cover) {
        this.coverImgUrl = params.cover;
      }
    },
    onClassInfoUpdate(classInfo) {  // 课堂信息更新
      console.log('%c [ classInfo ]-1178', 'font-size:13px; background:pink; color:#bf2c9f;', classInfo);
      if (this.isSelf) {
        TCIC.SDK.instance.reportLog('selfClassInfoUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, ${JSON.stringify(classInfo)}`);
      }
      if (this.isSelf && this.isStudent) {    // 大教学学生端处理
        // 全员静音状态更新
        if (Object.prototype.hasOwnProperty.call(classInfo?.data || {}, 'mute_all')) {
          const myPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
          if (myPermission && myPermission.mic && !classInfo.data.mute_all && myPermission.micState === TCIC.TDeviceStatus.Closed) {
            // 当前已有权限但麦克风是关闭状态
            const { micAutoOpen } = this.getStageUpOption();
            if (micAutoOpen) {
              this.isLocalMic = 1;
              this.enableMic(true);
            } else {
              this.confirmEnableMic();
            }
          } else if (classInfo.data.mute_all) {
            // 如果解除静音的对话框已经弹出，老师开启全体静音自动将其关闭
            if (this.micConfirmBoxId !== -1) {
              TCIC.SDK.instance.closeMessageBox(this.micConfirmBoxId);
              this.micConfirmBoxId = -1;
            }
          }
        }
        // 全员视频状态更新
        if (Object.prototype.hasOwnProperty.call(classInfo?.data || {}, 'mute_video_all')) {
          const myPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
          if (myPermission && myPermission.camera && !classInfo.data.mute_video_all && myPermission.cameraState === TCIC.TDeviceStatus.Closed) {
            // 当前已有权限摄像头是关闭状态
            this.confirmEnableCamera();
          } else if (classInfo.data.mute_video_all) {   // 收到消息就提示toast
            if (this.videoConfirmBoxId !== -1) {
              TCIC.SDK.instance.closeMessageBox(this.videoConfirmBoxId);
              this.videoConfirmBoxId = -1;
            }
            window.showToast(i18next.t('{{arg_0}}关闭了全体摄像头', { arg_0: this.roleInfo.teacher }));
          }
        }
      }
      if (this.isSelf) {
        TCIC.SDK.instance.reportLog('selfClassInfoUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, end`);
      }
    },
    // 视频放大还原切换事件
    toggleMaxVideo() {
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
      let isTeacherVideoMax = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode);
      if (TCIC.SDK.instance.isInteractClass() && classLayout !== TCIC.TClassLayout.Three) {
        isTeacherVideoMax = false; // 互动班课非三分屏布局时，忽略BigVideoMode
      }
      if (this.isControllerRole && !this.isScreenShareOpen && !isTeacherVideoMax) {    // 仅老师端需要处理
        const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
        const videoDom = this.$el.parentElement.parentElement;
        if (wrap && videoDom) {
          if (videoDom.getAttribute('drag') === 'true') {
            // 最小化视频
            const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
            if (classLayout === TCIC.TClassLayout.Three && this.isTeacherVideo) {
              wrap.getVueInstance().resetUserVideo(videoDom);
              TCIC.SDK.instance.setState(Constant.TStateSkipTeacherUpdateLayout, false);
              videoDom.removeAttribute('drag');
            } else {
              wrap.getVueInstance()
              .resetDragDom(videoDom, false);
              wrap.getVueInstance()
                .toggleShow(true);   // 展示视频栏
            }
          } else {
            if (this.isMobile && this.isTeacherVideo && !this.isSelf) {
              if (this.isPortrait) {
                let safeTop = 'env(safe-area-inset-top)';
                if (this.isIOS) {
                  const versions = TCIC.SDK.instance.getIOSVersion();
                  // evn在ios11.2以下的兼容性
                  if (versions.length === 2
                    && parseInt(versions[0], 10) <= 11
                    && parseInt(versions[1], 10) <= 2) {
                    safeTop = 'constant(safe-area-inset-top)';
                  }
                } else if (this.isAndroid) {
                  safeTop = '45px';
                }
                TCIC.SDK.instance.updateComponent('teacher-component', {
                  height: '30%',
                  top: `calc(${safeTop} + 45px)`,
                });
              } else {
                TCIC.SDK.instance.updateComponent('teacher-component', {
                  height: '89%',
                  top: '45px',
                });
              }
            }
            // 最大化指定视屏
            wrap.getVueInstance().maxUserVideo(videoDom);
            if (classLayout === TCIC.TClassLayout.Three && this.isTeacherVideo) {
              TCIC.SDK.instance.setState(Constant.TStateSkipTeacherUpdateLayout, true);
              this.updateShowReset(false);
            }
          }
        }
      }
    },
    setDbListener(listener) {
      this.dbClickListener = listener;
    },
    updatePermissionList(permissionList, reason) {
      const userPermission = permissionList.find(permission => permission.userId === this.userId);
      const userCameraOpen = userPermission?.camera && userPermission?.cameraState === TCIC.TDeviceStatus.Open;
      // console.log(`[VideoComponent] updatePermissionList, userId: ${this.userId}, userCameraOpen ${userCameraOpen}`, userPermission);
      if (!this.isSelf) {
        if (this.hasRendered) {
          // 公开课依赖权限列表中状态决定是否显示视频，需渲染过(避免web端无法展示视频)
          // 由于web端屏幕分享走主路，避免分享画面被占位图挡住
          const isWebScreenShare = userPermission?.platform === TCIC.TPlatform.Web && userPermission?.screenState < 2;
          this.isShowVideo = userCameraOpen || isWebScreenShare;
        }
        if (userCameraOpen) {
          this.isRendering = true;
          this.startRenderVideo('updatePermissionList, has camera permission and camera open');
        }
      } else if (this.isAndroid) { // 安卓端记录后台返回的本地摄像头状态
        this.hasLocalState = userPermission && userPermission.camera && userPermission.cameraState === 1;
      }
      if (this.isTeacherVideo) {
        const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
        const teacherPermission = permissionList.find(permission => permission.userId === teacherId);
        const teacherCameraOpen = teacherPermission?.camera && teacherPermission?.cameraState === TCIC.TDeviceStatus.Open;
        if (teacherCameraOpen) {
          this.renderWaterMark();
          this.renderMarquee();
        } else {
          this.destroyWaterMark();
          this.destroyMarquee();
        }
      }
      this.updateThisPermission(userPermission, reason);
    },

    // 处理本节点的权限变更，不一定是自己的
    async updateThisPermission(permission, reason) {
      // 更新权限
      const lastStageFlag = this.isOnStage;
      const lastMicFlag = this.isMicEnable;
      const lastCameraFlag = this.isCameraEnable;
      const lastBoardFlag = this.isBoardEnable;
      const lastShareScreenFlag = this.isShareScreenEnable;
      this.isShowCtrlVideo = true;
      if (permission) {
        this.isOnStage = permission.stage;
        this.isMicEnable = permission.mic;
        this.isCameraEnable = permission.camera;
        this.isBoardEnable = permission.board;
        this.isShareScreenEnable = permission.screen;
        // operatorId 优先Recv_Member_Action
        if (permission.operatorId !== this.userId && !this.recvOperator) {
          this.permissionOperatorId = permission.operatorId;
          this.isTeacherOperator = !permission.operatorId || TCIC.SDK.instance.isTeacher(permission.operatorId);
        }
        this.recvOperator = false;
        if (permission.screen === 1 && permission.screenState < 2) {
          this.isShowCtrlVideo = false;
        }
        if (this.isCollegeClass && this.isTeacher) {
          this.isShowCtrlVideo = true;
          if (this.isFullScreen) {
            if (permission.screen === 1 && permission.screenState < 2) {
              this.isShowShareScreenTips = true;
              this.screenShareTips = i18next.t('来自 {{arg_0}} 的屏幕共享', { arg_0: this.nickname });
            } else {
              this.isShowShareScreenTips = false;
            }
          } else {
            this.isShowShareScreenTips = false;
          }
        }
      } else {
        this.isOnStage = false;
        this.isMicEnable = false;
        this.isCameraEnable = false;
        this.isBoardEnable = false;
        this.isShareScreenEnable = false;
        this.isShowCtrlVideo = false;
      }
      const isSelfPermissionChanged = this.isSelf && (
        lastStageFlag !== this.isOnStage
          || lastMicFlag !== this.isMicEnable
          || lastCameraFlag !== this.isCameraEnable
          || lastBoardFlag !== this.isBoardEnable
          || lastShareScreenFlag !== this.isShareScreenEnable
      );
      if (isSelfPermissionChanged) {
        TCIC.SDK.instance.reportLog('selfPermissionUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, start, reason ${reason}, permission ${JSON.stringify(permission)}`);
      }

      if (this.isCollegeClass && TCIC.SDK.instance.isElectron() && this.ctrlCom !== null) {
        this.ctrlCom.getVueInstance().updateShareScreen(this.isShareScreenEnable);
        this.ctrlCom.getVueInstance().updateShowVideo(this.isShowCtrlVideo);
      }

      if (this.isSelf) {  // 更新本地音视频流状态
        // this.isMicOpen = permission && this.isMicEnable
        //   && (permission.micState === TCIC.TDeviceStatus.Unknown || permission.micState === TCIC.TDeviceStatus.Open);

        if (this.isCollegeClass && !this.isTeacher && (!this.isShareScreenEnable || !this.isOnStage)) {
          this.disableScreenShare();
        }
        if (reason === TCIC.TPermissionUpdateReason.MuteAll) {  // 全员静音操作
          if (this.isClassStarted && !this.isTeacher && lastStageFlag === this.isOnStage) {
            const muteAll = TCIC.SDK.instance.getState(TCIC.TMainState.Mute_All);
            if ((!muteAll && !this.isMicEnable) || (!muteAll && this.isMicEnable && permission.micState === TCIC.TDeviceStatus.Close))  {
              const { micAutoOpen } = this.getStageUpOption();
              if (micAutoOpen) {
                this.isLocalMic = 1;
                this.enableMic(true);
              } else {
                this.confirmEnableMic();
              }
            }
            if (!muteAll && this.isMicEnable && permission.micState === TCIC.TDeviceStatus.Open) {
              window.showToast(i18next.t('{{arg_0}}已解除全员静音', { arg_0: this.roleInfo.teacher }));
            }
          }
        }
      } else {  // 更新远端流音视频状态
        // 如果未授权，设备不可能处于打开状态；如果已授权，用户也可以自己关闭设备
        this.isMicOpen = permission
          && this.isMicEnable
          && (permission.micState === TCIC.TDeviceStatus.Unknown || permission.micState === TCIC.TDeviceStatus.Open);
        this.micDeviceStatus = permission ? permission.micState : TCIC.TDeviceStatus.Close;
        this.isCameraOpen = permission
          && this.isCameraEnable
          && (permission.cameraState === TCIC.TDeviceStatus.Unknown || permission.cameraState === TCIC.TDeviceStatus.Open);
        this.cameraDeviceStatus = permission ? permission.cameraState : TCIC.TDeviceStatus.Close;
        if (this.isCollegeClass && this.isTeacher && !this.isSelf) {  // 老师端才显示学生屏幕分享状态
          this.isScreenOpen = permission && permission.screen && permission.screenState < 2;
        }
        TCIC.SDK.instance.reportLog('otherPermissionUpdate', `[VideoComponent] reason ${reason}, permission ${JSON.stringify(permission)}, now ${JSON.stringify({
          isMicOpen: this.isMicOpen,
          micDeviceStatus: this.micDeviceStatus,
          isCameraOpen: this.isCameraOpen,
          cameraDeviceStatus: this.cameraDeviceStatus,
          isScreenOpen: this.isScreenOpen,
        })}`);
      }

      const isSelfCameraPermissionChanged = this.isSelf && lastCameraFlag !== this.isCameraEnable;
      if (isSelfCameraPermissionChanged) {
        // 自己的摄像头权限发生变更
        TCIC.SDK.instance.reportLog('selfPermissionUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, camera ${lastCameraFlag} -> ${this.isCameraEnable}`);
        this.$nextTick(() => {
          this.handleSelfCameraPermissionChanged({ reason, lastStageFlag });
        });
      } else {
        if (!this.isCameraEnable && this.isSelf) {
          this.hasLocalRendered = true;
        }
      }

      const isSelfMicPermissionChanged = this.isSelf && lastMicFlag !== this.isMicEnable;
      if (isSelfMicPermissionChanged) {
        // 自己的麦克风权限发生变更
        TCIC.SDK.instance.reportLog('selfPermissionUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, mic ${lastMicFlag} -> ${this.isMicEnable}`);
        this.$nextTick(() => {
          // 当mic、camera同时执行时，可能会导致audio状态异常，通过延时来保证mic开启完毕再开摄像头
          // TODO 通过延时不能保证吧？？？
          this.handleSelfMicPermissionChanged({ reason, lastStageFlag });
        });
      }

      this.$nextTick(() => {
        if (!this.isMicOpen) {
          this.$el.classList.remove('focus');
        }
      });
      // 更新控制栏
      this.updateCtrlStatus();

      if (isSelfPermissionChanged) {
        TCIC.SDK.instance.reportLog('selfPermissionUpdate', `[VideoComponent] selfSeq ${this.selfSeq}, end`);
      }
    },

     getStageUpOption() {
      // 获取上台媒体选项
      const stageUpOption = TCIC.SDK.instance.getStageMediaOption();
      // 根据是否启用直接控制来设置基础选项
      const baseOptions = this.enableDirectControl
        ? { micAutoOpen: true, cameraAutoOpen: true }
          : { micAutoOpen: false, cameraAutoOpen: false };

      const combinedOption = {
        ...baseOptions,
        ...stageUpOption,
      };
      // 如果没有启用直接控制，且当前合并后的选项里麦克风和摄像头自动开启都为 true，
      // 则将它们都设置为 false，符合特定业务逻辑要求
      if (!this.enableDirectControl && combinedOption.cameraAutoOpen && combinedOption.micAutoOpen) {
          combinedOption.micAutoOpen = false;
          combinedOption.cameraAutoOpen = false;
      }
      return combinedOption;
    },
    // 处理自己的摄像头权限变更，从 updateThisPermission 里抽出来
    handleSelfCameraPermissionChanged({ reason, lastStageFlag }) {
      if (reason === TCIC.TPermissionUpdateReason.MuteVideoAll) {  // 全员视频操作
        if (!this.isTeacher) {
          // 只有学生受MuteVideoAll影响
          if (this.isCameraEnable) {  // 打开全员视频
            if (this.isCameraOpenBeforeMuteAll && !this.isCollegeClass) {  // 只有关闭全员视频前摄像头处于打开状态才去打开摄像头
              this.enableCamera(this.isLocalCamera);
            }
          } else {  // 关闭全员视频
            this.isCameraOpenBeforeMuteAll = this.isCameraOpen;  // 记录摄像头状态
            this.enableCamera(false);
          }
        }
      } else {
        const { cameraAutoOpen } = this.getStageUpOption();
        if (this.isControllerRole && reason === 'init') {
          if (!this.isLocalCamera && this.isCameraEnable) {
            // 为什么要有这个提示？
            window.showToast(i18next.t('摄像头正在打开'));
          }
          this.isLocalCamera = 1;
          this.enableCamera(this.isCameraEnable, { sendMsgIfFail: reason !== TCIC.TPermissionUpdateReason.Init });
        } else {
          const handleCameraOpen = () => {
            if (this.videoConfirmBoxId !== -1) {
              TCIC.SDK.instance.closeMessageBox(this.videoConfirmBoxId);
              this.videoConfirmBoxId = -1;
            }
            this.isLocalCamera = 1;
            this.enableCamera(this.isCameraEnable);
          };
          if (this.enableDirectControl) {
            if (cameraAutoOpen || reason !== 'init') {
              handleCameraOpen();
            } else {
              this.isLocalCamera = 0;
              // 拒绝了也显示头像icon
              this.hasLocalRendered = true;
            }
          } else {
            if (!cameraAutoOpen && this.isCameraEnable) {
              this.confirmEnableCamera();
            } else {
              handleCameraOpen();
            }
          }
        }
      }
      // 只有开始上课后才需要提示，上课前老师无法操作学员设备
      // 被上下台时不需要提示
      if (this.isClassStarted && !this.isControllerRole && lastStageFlag === this.isOnStage) {
        // 只有权限相对实际状态产生了变化时，并且不是由全员操作导致的，才提示
        if (reason !== TCIC.TPermissionUpdateReason.MuteAll
            && reason !== TCIC.TPermissionUpdateReason.MuteVideoAll) {
          const params = { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant };
          if (this.isCollegeClass) {
            if (!this.isCameraEnable) {
              window.showToast(this.isCameraEnable ? i18next.t('{{arg_0}}开启了你的摄像头', params) : i18next.t('{{arg_0}}关闭了你的摄像头', params));
            }
          } else {
            // 在弹出确认框的情况下不进行提示
            if (this.videoConfirmBoxId === -1) {
              window.showToast(this.isCameraEnable ? i18next.t('{{arg_0}}开启了你的摄像头', params) : i18next.t('{{arg_0}}关闭了你的摄像头', params));
            }
          }
        }
        if (reason === TCIC.TPermissionUpdateReason.MuteVideoAll) {
          if (this.isCameraEnable) {
            this.confirmEnableCamera();
          }
        }
      }
    },

    // 处理自己的麦克风权限变更，从 updateThisPermission 里抽出来
    handleSelfMicPermissionChanged({ reason, lastStageFlag }) {
      if (reason === TCIC.TPermissionUpdateReason.MuteAll) {  // 全员静音操作
        if (!this.isCollegeClass || !this.isTeacher) {
          // 大教学的老师不受全员静音影响
          if (this.isMicEnable) {  // 取消全员静音
            if (this.isMicOpenBeforeMuteAll && !this.isCollegeClass) {  // 只有全员静音前麦克风处于打开状态才去打开麦克风
              this.enableMic(this.isLocalMic);
            }
          } else {  // 全员静音
            this.isMicOpenBeforeMuteAll = this.isMicOpen;  // 记录麦克风状态
            this.enableMic(false);
          }
        }
      } else if (reason === TCIC.TPermissionUpdateReason.Init && (this.isCollegeClass || TCIC.SDK.instance.isCoTeachingClass())) {
        // 大教学老师打开音频， 学生默认不打开音频
        if (this.isControllerRole) {
          this.enableMic(true);
        }
      } else {
        if (this.isControllerRole && reason === 'init') {
          // if (reason === TCIC.TPermissionUpdateReason.Action) {
          if (!this.isLocalMic && this.isMicEnable) {
            // 为什么要有这个提示？
            window.showToast(i18next.t('麦克风正在打开'));
          }
          this.isLocalMic = 1;
          this.enableMic(this.isMicEnable, { sendMsgIfFail: true, alertPermission: reason !== TCIC.TPermissionUpdateReason.Init });
          // } else {
          //   this.enableMic(this.isMicEnable && this.isLocalMic, true);
          // }
        } else {
          const handleMicOpen = () => {
             // 如果解除静音的对话框已经弹出，老师取消权限则将其关闭
            if (this.micConfirmBoxId !== -1) {
              TCIC.SDK.instance.closeMessageBox(this.micConfirmBoxId);
              this.micConfirmBoxId = -1;
            }
            this.isLocalMic = 1;
            this.enableMic(this.isMicEnable, { sendMsgIfFail: false, alertPermission: reason !== TCIC.TPermissionUpdateReason.Init });
          };
          const { micAutoOpen } = this.getStageUpOption();
          if (this.enableDirectControl) {
            if (micAutoOpen || reason !== 'init') {
              handleMicOpen();
            }
          } else {
            if (this.isMicEnable && !micAutoOpen) {
              this.confirmEnableMic();
            } else {
              handleMicOpen();
            }
          }
        }
      }
      // 只有开始上课后才需要提示，上课前老师无法操作学员设备
      // 被上下台时不需要提示
      if (this.isClassStarted && !this.isTeacher && lastStageFlag === this.isOnStage) {
        // 只有权限相对实际状态产生了变化时，并且不是由全员操作导致的，才提示
        if (reason !== TCIC.TPermissionUpdateReason.MuteAll
        && reason !== TCIC.TPermissionUpdateReason.MuteVideoAll) {
          const params = { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant };
          if (this.isCollegeClass) {
            if (!this.isMicEnable) {
              window.showToast(this.isMicEnable ? i18next.t('{{arg_0}}开启了你的麦克风', params) : i18next.t('{{arg_0}}关闭了你的麦克风', params));
            }
          } else {
            // 在弹出确认框的情况下不进行提示
            if (this.micConfirmBoxId === -1) {
              window.showToast(this.isMicEnable ? i18next.t('{{arg_0}}开启了你的麦克风', params) : i18next.t('{{arg_0}}关闭了你的麦克风', params));
            }
          }
        }
      }
    },

    // 关闭屏幕分享
    async disableScreenShare() {
      if (this.isScreenShareOpen) {
        TCIC.SDK.instance.reportLog('stopScreenShare', '[VideoComponent] disableScreenShare');
        await TCIC.SDK.instance.stopScreenShare();
      }
      // 非老师的
      // if (this.isCollegeClass && !this.isTeacher && this.isSelf) {
      // }
    },
    // 开启屏幕分享
    async enableScreenShare() {
      const isWeb = this.isWeb;
      // Web端屏幕分享，将采集到的视频渲染到老师视频节点上
      if (!this.isScreenShareOpen) {
        this.isCameraOpenBeforeScreenShare = this.isCameraOpen;
        if (this.isCameraOpenBeforeScreenShare && isWeb) {
          this.isRendering = false;
          TCIC.SDK.instance.reportLog('stopLocalVideo', '[VideoComponent] enableScreenShare on web');
          await TCIC.SDK.instance.stopLocalVideo();
        }
        try {
          if (isWeb) {
            // Web端只有开启声音采集后才会出现声音采集选项【屏幕共享采集本地声音】
            await TCIC.SDK.instance.enableSystemAudioLoopback(true);
            // 避免未开启音频时打开屏幕共享，重新创建流的问题
            const isAudioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
            if (!isAudioCapture) {
              try {
                TCIC.SDK.instance.reportLog('setMicVolume', '[VideoComponent] enableScreenShare on web, volume 0');
                await TCIC.SDK.instance.setMicVolume(0);
                TCIC.SDK.instance.reportLog('startLocalAudio', '[VideoComponent] enableScreenShare on web');
                await TCIC.SDK.instance.startLocalAudio(this.$refs.audio);
              } catch (err) {
                window.showToast(i18next.t('开启屏幕共享失败，没有可用的麦克风'));
                throw err;
              }
            }
          }
          TCIC.SDK.instance.reportLog('startScreenShare', '[VideoComponent] enableScreenShare');
          await TCIC.SDK.instance.startScreenShare(this.$refs.video);
        } catch (err) {  // 打开失败或者取消后，回退
          if (this.isCameraOpenBeforeScreenShare && isWeb) {
            this.isLocalRendering = true;
            TCIC.SDK.instance.reportLog('startLocalVideo', '[VideoComponent] enableScreenShare on web error');
            await TCIC.SDK.instance.startLocalVideo(this.$refs.video);
          }
          throw err;
        }
      } else {
        if (this.isCollegeClass) {
          window.showToast(i18next.t('你有正在共享的内容，如有需要，请先停止共享'));
        }
      }
    },
    // 发送开启设备的反馈
    sendUICtrlFeedback(deviceType, feedbackAction, params) {
      const info = {
        action: feedbackAction,
        nick: this.nickname,
        userId: this.userId,
        permissionOperatorId: this.permissionOperatorId, // TODO 这里有问题，并发action的话会被覆盖
        ...params,
      };
      console.log('[VideoComponent] sendUICtrlFeedback', deviceType, info);
      TCIC.SDK.instance.sendGroupCustomMessage('ui_ctrl', JSON.stringify(info));
    },
    // 二次确认开启音频
    confirmEnableMic() {
      if (TCIC.SDK.instance.getState(Constant.TStateCollegeRequestMicPermission)) {
        this.enableMic(this.isLocalMic);
        TCIC.SDK.instance.setState(Constant.TStateCollegeRequestMicPermission, false);
        return;
      }
      const permission = TCIC.SDK.instance.getPermissionList().find(permission => permission.userId === this.userId);
      if (!this.AudioQuestionIsAnswer && permission.mic) {
        this.AudioQuestionIsAnswer = true;
        TCIC.SDK.instance.reportLog('confirmEnableMic', `[VideoComponent] selfSeq ${this.selfSeq}, confirmEnableMic showMessageBox`);
        this.micConfirmBoxId = TCIC.SDK.instance.showMessageBox('', i18next.t(
          '{{arg_0}}希望你解除静音',
          { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant },
        ), [i18next.t('解除静音'), i18next.t('保持静音')], (index, options) => {
          if (this.isDestroyed) {
            // 已销毁
            return;
          }
          const confirm = index === 0;
          TCIC.SDK.instance.reportLog('confirmEnableMic', `[VideoComponent] selfSeq ${this.selfSeq}, confirmEnableMic callback, confirm ${confirm}`);
          if (confirm) {
            window.showToast(i18next.t('{{arg_0}}开启了你的麦克风', { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant }));
            this.isLocalMic = 1;
            this.enableMic(true, { sendMsgIfFail: true });
            this.sendUICtrlFeedback('mic', 'open_mic');
          } else {
            this.isLocalMic = 0;
            this.enableMic(false, { sendMsgIfFail: true });
            this.sendUICtrlFeedback('mic', 'stay_mic', {
              deviceState: DeviceUtil.getReportDeviceState(false, TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status)),
            });
          }
          this.AudioQuestionIsAnswer = false;
          this.micConfirmBoxId = -1;
        });
      }
    },
    // 二次确认开启视频
    confirmEnableCamera() {
      if (!this.VideoQuestionIsAnswer) {
        this.VideoQuestionIsAnswer = true;
        TCIC.SDK.instance.reportLog('confirmEnableCamera', `[VideoComponent] selfSeq ${this.selfSeq}, confirmEnableCamera showMessageBox`);
        this.videoConfirmBoxId = TCIC.SDK.instance.showMessageBox('', i18next.t(
          '{{arg_0}}希望打开你的摄像头',
          { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant },
        ), [i18next.t('打开视频'), i18next.t('保持关闭')], (index, options) => {
          if (this.isDestroyed) {
            // 已销毁
            return;
          }
          const confirm = index === 0;
          TCIC.SDK.instance.reportLog('confirmEnableCamera', `[VideoComponent] selfSeq ${this.selfSeq}, confirmEnableCamera callback, confirm ${confirm}`);
          if (confirm) {
            window.showToast(i18next.t('{{arg_0}}开启了你的摄像头', { arg_0: this.isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant }));
            this.isLocalCamera = 1;
            this.enableCamera(true, { sendMsgIfFail: true });
            this.sendUICtrlFeedback('camera', 'open_camera');
          } else {
            this.isLocalCamera = 0;
            // 拒绝了也显示头像icon
            this.hasLocalRendered = true;
            this.enableCamera(false, { sendMsgIfFail: true });
            this.sendUICtrlFeedback('camera', 'stay_camera', {
              deviceState: DeviceUtil.getReportDeviceState(false, TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status)),
            });
          }
          this.VideoQuestionIsAnswer = false;
          this.videoConfirmBoxId = -1;
        });
      }
    },
    // 二次确认是否要刷新RTC状态
    confirmReloadClass() {
      if (!this.enterClassQuestionIsAnswer) {
        this.enterClassQuestionIsAnswer = true;
        this.enterClassConfirmId = TCIC.SDK.instance.showMessageBox(
          '',
          i18next.t('您的音视频互动服务因超时断连，是否重新连接?'),
          [i18next.t('重新连接'), i18next.t('取消')], (index, options) => {
            this.enterClassQuestionIsAnswer = false;
            this.enterClassConfirmId = -1;
            if (index === 0) {
              TCIC.SDK.instance.reloadClass();
            }
          },
        );
      }
    },
    // 切换屏幕分享状态
    toggleScreenShare() {
      if (!this.isScreenShareOpen) {
        this.enableScreenShare();
      } else {
        this.disableScreenShare();
      }
    },

    // 打开关闭camera
    async enableCamera(flag, { sendMsgIfFail = false, alertPermission = true } = {}) {
      console.log('VideoComponent::enableCamera =>', this.userId, flag);
      if (flag && TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isTeacher()) {
        await TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_TRTC, true);     // 公开课学生打开设备需要等待进入音视频房间
      }
      if (TCIC.SDK.instance.isInteractClass()) {
        const videoWrap = TCIC.SDK.instance.getComponent('videowrap-component');
        if (videoWrap) {  // 操作摄像头时显示我的视频
          videoWrap.getVueInstance().showMyVideo();
        }
      }

      if (flag) {
        if (this.isScreenShareOpen
            && this.isCollegeClass
            && this.isWeb) {
          window.showToast(i18next.t('你正在进行屏幕共享，暂时无法开启摄像头'));
          return;
        }
        this.isLocalRendering = true;

        // startingLocal 时不能重复点，waitingStartLocal 时可以
        console.log(`[VideoComponent] enableCamera ${flag} when ${this.loadingCameraState?.loading}`);
        if (this.loadingCameraState?.loading === 'startingLocal') {
          // loading中
          console.log(`[VideoComponent] enableCamera ${flag} when ${this.loadingCameraState?.loading}, return`);
          return;
        }

        // 不知道这一段的来历，保留
        // if (TCIC.SDK.instance.isWeb()) {
        //   // 避免开启摄像头后再开启音频导致视频闪一下的问题
        //   const isAudioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
        //   if (flag && !isAudioCapture) {
        //     TCIC.SDK.instance.reportLog('setMicVolume', `[VideoComponent] enableCamera ${flag} on web, volume 0`);
        //     await TCIC.SDK.instance.setMicVolume(0);
        //     TCIC.SDK.instance.reportLog('startLocalAudio', `[VideoComponent] enableCamera ${flag} on web`);
        //     await TCIC.SDK.instance.startLocalAudio(this.$refs.audio);
        //   }
        // }

        try {
          const res = await DeviceUtil.toggleLocalDeviceWithLoadingEvent(
            'camera',
            flag,
            () => TCIC.SDK.instance.startLocalVideo(this.$refs.video),
            {
              caller: 'VideoComponent',
              reason: `enableCamera-${flag}`,
              reportAction: 'startLocalVideo',
            },
          );
          this.hasLocalRendered = true;

          if (TCIC.SDK.instance.isCoTeachingClass()) {  // 双师课更新推流状态
            TCIC.SDK.instance.muteLocalVideo(!this.coTeachingPublishMyVideo);
          }
          if (this.isTeacherVideo) {
            this.renderWaterMark();
            this.renderMarquee();
          }
          if (!res) {
            this.checkAndShowAndroidErrorDialog('camera');
            // 更新控制栏状态
            this.updateCtrlStatus();
          }
          return Promise.resolve();
        } catch (err) {
          this.isLocalRendering = false;

          // 错误提示
          const errMsg = DeviceUtil.showErrorMessage('camera', err);
          this.checkAndShowAndroidErrorDialog('camera');
          // 更新控制栏状态
          this.updateCtrlStatus();
          const isOpenByOther = this.permissionOperatorId && this.permissionOperatorId !== TCIC.SDK.instance.getUserId();
          if ((!this.isControllerRole || isOpenByOther) && sendMsgIfFail) {
            // 如果是学生或者是被其他人打开的，通知回去
            this.sendUICtrlFeedback('camera', 'camera_fail', {
              deviceState: DeviceUtil.getReportDeviceState(false, TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status)),
              reason: errMsg || i18next.t('摄像头打开失败'),
            });
          }

          return Promise.reject(err);
        }
      } else {
        this.isLocalRendering = false;

        // stoppingLocal 时不能重复点，waitingStopLocal 时可以
        console.log(`[VideoComponent] enableCamera ${flag} when ${this.loadingCameraState?.loading}`);
        if (this.loadingCameraState?.loading === 'stoppingLocal') {
          // loading中
          console.log(`[VideoComponent] enableCamera ${flag} when ${this.loadingCameraState?.loading}, return`);
          return;
        }

        try {
          await DeviceUtil.toggleLocalDeviceWithLoadingEvent(
            'camera',
            flag,
            () => TCIC.SDK.instance.stopLocalVideo(),
            {
              caller: 'VideoComponent',
              reason: `enableCamera-${flag}`,
              reportAction: 'stopLocalVideo',
            },
          );

          if (this.isTeacherVideo) {
            this.destroyWaterMark();
            this.destroyMarquee();
          }

          return Promise.resolve();
        } catch (err) {
          return Promise.reject(err);
        }
      }
    },
    checkAndShowAndroidErrorDialog(type) {
      const isAndroid = TCIC.SDK.instance.isAndroid();
      if (!isAndroid) {
        return;
      }
      if (type === 'mic') {
        let tip = i18next.t('设备不可用（损坏、未授权等）');
        const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
        switch (errCode) {
          case -1001: // 打开失败
            tip = i18next.t('麦克风打开失败，请检查系统设置');
            break;
          case -1002: // 找不到设备
            tip = i18next.t('找不到可用麦克风');
            break;
          case -1003: // 未授权
            tip = i18next.t('请前往系统设置开启权限');
            break;
          case -1004: // 设备被占用
            tip = i18next.t('麦克风被占用，请关闭其他应用后重试');
            break;
          case -1006: // 1204被占用
            tip = i18next.t('请检查麦克风访问权限、关闭其他应用或重启设备');
            break;
          case 2: // 已关闭
            tip = i18next.t('麦克风已关闭，请开启');
            break;
          default:
            break;
        }
        // android下如果异常，弹窗提示
        if (errCode < 0) {
          TCIC.SDK.instance.showMessageBox(i18next.t('无法使用麦克风'), tip, [i18next.t('确认')]);
        }
      } else if (type === 'camera') {
        let tip = i18next.t('设备不可用（损坏、未授权等）');
        const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
        switch (errCode) {
          case -1001: // 打开失败
            tip = i18next.t('摄像头打开失败，请检查系统设置');
            break;
          case -1002: // 找不到设备
            tip = i18next.t('找不到可用摄像头');
            break;
          case -1003: // 未授权
            tip = i18next.t('请前往系统设置开启权限');
            break;
          case -1004: // 设备被占用
            tip = i18next.t('相机被占用，请关闭其他应用后重试');
            break;
          case 2: // 已关闭
            tip = i18next.t('摄像头已关闭，请开启');
            break;
          default:
            break;
        }
        // android下如果异常，弹窗提示
        if (errCode < 0) {
          TCIC.SDK.instance.showMessageBox(i18next.t('无法使用相机'), tip, [i18next.t('确认')]);
        }
      }
    },
    // 打开关闭mic，reportPermission是否提示授权，默认初始化时不提示
    async enableMic(flag, { sendMsgIfFail = false, alertPermission = true } = {}) {
      console.log('VideoComponent::enableMic =>', this.userId, flag, `isSelf ${this.isSelf}`);
      if (flag && TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isTeacher()) {
        await TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_TRTC, true);     // 公开课学生打开设备需要等待进入音视频房间
      }

      const isIOSNative = TCIC.SDK.instance.isIOSNative();
      const isStudent = TCIC.SDK.instance.isStudent();
      const isDocumentAudioPlaying = TCIC.SDK.instance.isDocumentAudioPlaying();
      const isDocumentVideoPlaying = TCIC.SDK.instance.isDocumentVideoPlaying();
      const audioPlayerComponent = TCIC.SDK.instance.getComponent('audio-player-component');
      let currBoardAudioVolume = 0.5;
      try {
        if (audioPlayerComponent && audioPlayerComponent.getVueInstance) {
          const vc = audioPlayerComponent.getVueInstance();
          currBoardAudioVolume = vc.getBoardAudioVolume();
        }
      } catch (e) {
        console.error(e);
        TCIC.SDK.instance.reportLog('getBoardAudioVolume', e.toString());
      }

      if (!TCIC.SDK.instance.isTeacher() && TCIC.SDK.instance.isCoTeachingClass()) {  // 双师课学生端打开麦克风前要先申请资源
        if (flag) {
          try {
            await TCIC.SDK.instance.requireResource('mic');
          } catch (err) {
            window.showToast(i18next.t(
              '{{arg_0}}内打开麦克风数量超过限制，出于性能考虑，请您先联系{{arg_1}}，让其关闭部分{{arg_2}}的麦克风',
              { arg_0: this.roomInfo.name, arg_1: this.roleInfo.teacher, arg_2: this.roleInfo.student },
            ), 'error', { duration: 5000 });
            sendMsgIfFail && this.sendUICtrlFeedback('mic', 'mic_limit', {
              reason: err.errorMsg,
            });
            return;
          }
        } else {
          await TCIC.SDK.instance.releaseResource('mic');
        }
      }
      try {
        let micVolume = TCIC.SDK.instance.getState(Constant.TStateMicVolume, 100);
        if (flag && micVolume === 0) {
          // 打开mic时，如果音量是0，自动改成50
          micVolume = 50;
          this.$EventBus.$emit('update:micVolume', micVolume); // 这个会更新Setting里的音量
          TCIC.SDK.instance.setState(Constant.TStateMicVolume, micVolume);
        }
        // await TCIC.SDK.instance.setMicVolume(flag ? micVolume : 0);
        if (isStudent && isIOSNative) {
          if (flag && (isDocumentAudioPlaying || isDocumentVideoPlaying)) {
            window.showToast(i18next.t('开启麦克风可能会影响收听效果'));
          }
          TCIC.SDK.instance.reportLog('enableMic-muteLocalAudio', `${!flag} do nothing`);
        } else {
          TCIC.SDK.instance.reportLog('enableMic-muteLocalAudio', `${!flag}`);
          // 使用mute方法静音
          await TCIC.SDK.instance.muteLocalAudio(!flag);
        }
        if (!flag) {
          this.$el.classList.remove('focus');
        }
      } catch (err) {
        TCIC.SDK.instance.reportLog('setMicVolume', `[VideoComponent] setMicVolume error, code ${err.errorCode}, message ${err.errorMsg}`);
      }

      const actualMicState = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start
        ? TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture) && TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Publish)
        : TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
      TCIC.SDK.instance.reportLog('enableMic-actualMicState', `${actualMicState}`);
      if (flag && !actualMicState) {
        // startingLocal 时不能重复点，waitingStartLocal 时可以
        console.log(`[VideoComponent] enableMic ${flag} when ${this.loadingMicState?.loading}`);
        if (this.loadingMicState?.loading === 'startingLocal') {
          // loading中
          console.log(`[VideoComponent] enableMic ${flag} when ${this.loadingMicState?.loading}, return`);
          TCIC.SDK.instance.reportLog('enableMic-loadingMicState', `${this.loadingMicState?.loading}`);
          return;
        }

        try {
          const res = await DeviceUtil.toggleLocalDeviceWithLoadingEvent(
            'mic',
            flag,
            () => TCIC.SDK.instance.startLocalAudio(this.$refs.audio),
            {
              caller: 'VideoComponent',
              reason: `enableMic-${flag}`,
              reportAction: 'startLocalAudio',
            },
          );
          TCIC.SDK.instance.reportLog('enableMic-startLocalAudio', `${flag}`);
          if (!res) {
            this.checkAndShowAndroidErrorDialog('mic');
            // 更新控制栏状态
            this.updateCtrlStatus();
          }
        } catch (err) {
          TCIC.SDK.instance.reportLog('enableMic-startLocalAudio-error', `${flag}`);
          // 错误提示
          const errMsg = DeviceUtil.showErrorMessage('mic', err);
          this.checkAndShowAndroidErrorDialog('mic');
          // 更新控制栏状态
          this.updateCtrlStatus();
          const isOpenByOther = this.permissionOperatorId && this.permissionOperatorId !== TCIC.SDK.instance.getUserId();
          if ((!this.isControllerRole || isOpenByOther) && sendMsgIfFail) {
            // 如果是学生或者是被其他人打开的，通知回去
            this.sendUICtrlFeedback('mic', 'mic_fail', {
              deviceState: DeviceUtil.getReportDeviceState(false, TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status)),
              reason: errMsg || i18next.t('麦克风打开失败'),
            });
          }
          return Promise.reject(err);
        }
      } else {
        // ios 学生
        if (isStudent && isIOSNative) {
          if (!flag) {
            TCIC.SDK.instance.reportLog('enableMic-stopLocalAudio-ios', `${flag}`);
            await TCIC.SDK.instance.stopLocalAudio();
            // 如果学生侧的音频音量太小则给予提示
            if (currBoardAudioVolume < 0.25) {
              window.showToast(i18next.t('为保证通话体验，请适当调大音量'));
            }
          } else {
            TCIC.SDK.instance.reportLog('enableMic-startLocalAudio-ios', `${flag}`);
            await TCIC.SDK.instance.startLocalAudio();
          }
        }
        this.isMicOpen = flag;
        this.updateCtrlStatus();
        TCIC.SDK.instance.reportLog('enableMic-notifyDeviceLoadingEvent', `${flag}`);
        DeviceUtil.notifyDeviceLoadingEvent(
          this.userId,
          'mic',
          {
            loading: false,
            open: flag,
          },
          `VideoComponent-enableMic-${flag}-onlyVolume`,
        );
      }

      return Promise.resolve();
    },
    async action(actionType) {
      let deviceType;
      let flag;
      switch (actionType) {
        case TCIC.TMemberActionType.Camera_Open:
        case TCIC.TMemberActionType.Camera_Close:
          {
            const loadingState = this.loadingCameraState;
            if (!!loadingState?.loading) {
              console.log(`[VideoComponent] action ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast
              // window.showToast(this.isSelf ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'camera';
            flag = actionType === TCIC.TMemberActionType.Camera_Open;
          }
          break;
        case TCIC.TMemberActionType.Mic_Open:
        case TCIC.TMemberActionType.Mic_Close:
          {
            const loadingState = this.loadingMicState;
            if (!!loadingState?.loading) {
              console.log(`[VideoComponent] action ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast
              // window.showToast(this.isSelf ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'mic';
            flag = actionType === TCIC.TMemberActionType.Mic_Open;
          }
          break;
      }

      const promise = deviceType
        ? DeviceUtil.toggleRemoteDeviceWithLoadingEvent(
          this.userId, // 这个video节点的userId，不一定是自己
          deviceType,
          flag,
          () => TCIC.SDK.instance.memberAction({
            userId: this.userId,
            actionType,
          }),
          {
            caller: 'VideoComponent',
            reason: `memberAction-${actionType}`,
          },
        )
        : TCIC.SDK.instance.memberAction({
          userId: this.userId,
          actionType,
        });

      try {
        await promise;
      } catch (err) {
        // 请求失败
        window.showToast(err.errorMsg, 'error');
      }
    },
    toggleShareScreen() {
      if (this.isTeacher) {
        this.action(this.isShareScreenEnable ? TCIC.TMemberActionType.Screen_Share_Close : TCIC.TMemberActionType.Screen_Share_Open);
      }
    },
    // 检查当前用户是否断开了rtc链接导致声音或图像无法发布
    isUserRtcDisconnected() {
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start
        && this.isSelf && !TCIC.SDK.instance.getState(TCIC.TMainState.Joined_TRTC, false)) {    // 上课后尚未进入课堂RTC互动视频流
        return true;
      }
      return false;
    },
    toggleMic: Lodash.throttle(function () {
      // 上课后尚未进入课堂TRTC房间
      if (this.isUserRtcDisconnected() && !TCIC.SDK.instance.isRtmpMode()) {
        this.confirmReloadClass();
        return;
      }
      if (!!this.loadingMicState?.loading) {
        console.log(`[VideoComponent] toggleMic when ${this.loadingMicState.loading}, return`);
        window.showToast(this.isSelf ? i18next.t('稍等片刻，请勿重复操作') : i18next.t('正在授权，请稍等'));
        return;
      }

      if (this.isSelf && (this.isMicEnable || this.isPreviewMode)) {
        // 是自己且有权限
        const newVal = !this.isMicOpen;
        console.log('[VideoComponent] toggleMic, enableMic', newVal);
        this.isLocalMic = newVal;
        this.enableMic(newVal);
      } else if (this.isControllerRole) {
        // 是老师 toggle别人的摄像头
        const actionType = this.isMicOpen ? TCIC.TMemberActionType.Mic_Close : TCIC.TMemberActionType.Mic_Open;
        console.log('[VideoComponent] toggleMic, memberAction', actionType);
        this.action(actionType);
      } else {
        if (TCIC.SDK.instance.getState(TCIC.TMainState.Mute_All, false)) {
          window.showToast(i18next.t('{{arg_0}}已开启全员静音，你可以举手申请发言~', { arg_0: this.roleInfo.teacher }), 'error');
        } else {
          window.showToast(i18next.t('麦克风权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher }), 'error');
        }
      }
    }, 500, {
      leading: true,
      trailing: false,
    }),
    toggleCamera: Lodash.throttle(function (newStatus) {
      if (this.isUserRtcDisconnected() && !TCIC.SDK.instance.isRtmpMode()) {
        this.confirmReloadClass();
        return;
      }
      if (!!this.loadingCameraState?.loading) {
        console.log(`[VideoComponent] toggleCamera when ${this.loadingCameraState.loading}, return`);
        window.showToast(this.isSelf ? i18next.t('稍等片刻，请勿重复操作') : i18next.t('正在授权，请稍等'));
        return;
      }

      console.log('[VideoComponent] toggleCamera, newStatus', newStatus);
      /**
       * 本地遇到过几次，
       * newStatus == 'open' 时 this.isCameraOpen 为 true的情况，导致摄像头无法打开
       * 状态值过多，管理混乱，容易出现状态不对导致异常,
       * 业务逻辑上，用户操作摄像头open，就必须进入开启摄像头的逻辑，所以对应isCameraOpen只能为false，
       * 没搞明白为什么要再判断isCameraOpen
       */
      // if (newStatus === 'open') {
      //     this.isCameraOpen = false;
      //     TCIC.SDK.instance.reportLog('toggleCamera', `${this.isCameraOpen}:${newStatus}`);
      //   }
      if (this.isSelf && (this.isCameraEnable || this.isPreviewMode)) {
        // 是自己且有权限
        const newVal = !this.isCameraOpen;
        console.log('[VideoComponent] toggleCamera, enableCamera', newVal);
        this.isLocalCamera = newVal;
        this.enableCamera(newVal);
      } else if (this.isControllerRole) {
        // 是老师 toggle别人的摄像头
        const actionType = this.isCameraOpen ? TCIC.TMemberActionType.Camera_Close : TCIC.TMemberActionType.Camera_Open;
        console.log('[VideoComponent] toggleCamera, memberAction', actionType);
        this.manualVideo = true;
        this.action(actionType);
      } else {
        window.showToast(i18next.t('摄像头权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher }), 'error');
      }
    }, 500, {
      leading: true,
      trailing: false,
    }),
    rotate90: Lodash.throttle(function () {
      TCIC.SDK.instance.rotate90(this.userId, TCIC.TStreamType.Main);
    }),
    toggleBoard() {
      if (!this.isClassStarted) {
        window.showToast(i18next.t('还没开始上课'));
      } else if (this.isControllerRole) { // 是老师
        this.action(this.isBoardEnable ? TCIC.TMemberActionType.Board_Disable : TCIC.TMemberActionType.Board_Enable);
      }
    },
    // 视频墙展示
    toggleVideoWall() {
      const wraper = TCIC.SDK.instance.getComponent('videowrap-component') || TCIC.SDK.instance.getComponent('ooo-video-wrap-component');
      if (wraper && wraper.getVueInstance()) {
        wraper.getVueInstance().quickVideoWall(!this.isVideoWall);
      }
    },
    toggleStage() {
      if (this.isControllerRole) {
        if (TCIC.SDK.instance.getState(Constant.TStateCarousel)) {
          window.showToast(i18next.t('请先结束循环上台'), 'error');
        } else {
          this.action(TCIC.TMemberActionType.Stage_Down);
        }
      }
    },
    resetDrag() {
      let wrap = TCIC.SDK.instance.getComponent('videowrap-component');
      const videoDom = this.$el.parentElement.parentElement;
      if (wrap && videoDom) {
        wrap.getVueInstance()
          .resetDragDom(videoDom, false);
        wrap.getVueInstance()
          .toggleShow(true);   // 展示视频栏
      } else if (wrap = TCIC.SDK.instance.getComponent('ooo-video-wrap-component')) {
        wrap.getVueInstance()
          .resetDomPosition(videoDom, true);
      }
      this.hideCtrlComponent();
    },
    // 隐藏控制栏
    hideCtrlComponent() {
      if (this.ctrlCom) {
        // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] hideCtrlComponent`);
        this.ctrlCom.getVueInstance().hideCtrlComponent();
      }
    },
    // 加载控制栏
    showCtrlComponent() {
      if (!this.enableShowCtrlComponent) {
        // 单击或mouse hove时，是否显示控制栏
        return;
      }
      if (this.isCoTeachingClass) {
        return;
      }
      if (!this.isControllerRole && !this.isSelf && !this.isShowVodFooterCtrl) {
        // 没有控制权限的忽略
        return;
      }
      const permissionList = TCIC.SDK.instance.getPermissionList();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const onlyTeacher = permissionList.length === 1 && permissionList[0].userId === classInfo.teacherId;
      const videoInBoard = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode);
      if (this.isTeacherVideo && onlyTeacher && videoInBoard) { // 老师全屏到白板区域，隐藏控制栏
        return;
      }
      const padding = 2;
      const width = 150;
      const height = 36;
      const curRect = this.$el.getBoundingClientRect();
      const appRect = document.body.getBoundingClientRect();
      let top = 0; let left = 0;
      const marginTop = curRect.top - height - padding;
      if (this.controlDirect === 'top' && marginTop < height) {
        this.controlDirect = 'bottom';
      }
      if (this.controlDirect === 'hide') {
        return ;
      } if (this.controlDirect === 'left') {
        left = curRect.left - width - padding;
        top = curRect.top + Math.floor((curRect.height - height) / 2);
      } else if (this.controlDirect === 'top') {
        left = curRect.left + Math.floor((curRect.width - width) / 2);
        top = curRect.top - height - padding;
      } else if (this.controlDirect === 'right') {
        left = curRect.left + curRect.width + padding;
        top = curRect.top + Math.floor((curRect.height - height) / 2);
      } else {
        left = curRect.left + Math.floor((curRect.width - width) / 2);
        // 视频墙布局时不添加padding
        top = curRect.top + curRect.height + ([TCIC.TClassLayout.Video, TCIC.TClassLayout.Double].includes(TCIC.SDK.instance.getClassLayout()) ? 0 : padding);
      }
      // 边缘校正
      if (left < appRect.left || left + width > appRect.right) {
        left = curRect.left + Math.floor((curRect.width - width) / 2);
      }
      if (top < 0 || top + height > appRect.bottom) {
        top = curRect.top + Math.floor((curRect.height - height) / 2);
      }
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, this.userId);
      let needZoom = TCIC.SDK.instance.getState(Constant.TStateVideoDubbleLine, false);
      if (this.isTeacher && this.isSelf) {
        needZoom = false;
      } else {
        const stuCom = TCIC.SDK.instance.getComponent('student-component', this.userId);
        if (stuCom && stuCom.getAttribute('drag') === 'true') {   // 拖动状态也不缩放
          needZoom = false;
        }
      }
      const zoom = needZoom ? 0.49 : 1;
      const fixLeft = needZoom ? -40 : 0;
      let videoCtrlLeft =  `${left * zoom + fixLeft}px`;
      let videoCtrlTop = `${top * zoom}px`;
      let videoCtrlScale = 1;
      if (this.isMobile) {
        /* TODO 不确定之前为什么这么判断，会导致ipad和学生端定位不对，改了试试
        if (this.isPortrait && this.isTeacher && !TCIC.SDK.instance.isMobileNative()) {
          videoCtrlLeft = `${curRect.top + curRect.height * 0.5 - width * 0.5}px`;
          videoCtrlTop = `calc(100% - ${curRect.left + curRect.width * 0.5 - height * 0.5 + 45}px)`;
        } else if (this.isTeacher || this.isOneOnOneVideoClass) {
          videoCtrlLeft = `${curRect.left + curRect.width * 0.5 - width * 0.5}px`;
          videoCtrlTop = `${curRect.top + curRect.height * 0.5 - height * 0.5}px`;
        }
        */

        // 移动端默认都在头像中间
        videoCtrlLeft = `${curRect.left + curRect.width * 0.5 - width * 0.5}px`;
        videoCtrlTop = `${curRect.top + curRect.height * 0.5 - height * 0.5}px`;

        if (this.isSmallScreen) {
          // 手机竖屏的特殊处理，没看懂。。。沿用之前的逻辑
          if (this.isPortrait && !TCIC.SDK.instance.isMobileNative() && curRect.height > curRect.width) {
            videoCtrlLeft = `${curRect.top + curRect.height * 0.5 - width * 0.5}px`;
            videoCtrlTop = `calc(100% - ${curRect.left + curRect.width * 0.5 - height * 0.5 + 45}px)`;
          }
        }

        // 如果curRect太小，scale一下
        videoCtrlScale = Math.min(Math.max(curRect.width, curRect.height) / width, 1);
      }

      if (!this.ctrlCom) { // 加载控制栏组件
        // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] showCtrlComponent, loadComponent, left ${videoCtrlLeft}, top ${videoCtrlTop}`);
        TCIC.SDK.instance.loadComponent('video-ctrl-component', {
          left: videoCtrlLeft,
          top: videoCtrlTop,
          transform: `scale(${videoCtrlScale})`,
          width: `${width}px`,
          height: `${height}px`,
          zIndex: 401,
          display: 'block',
        }, null, this.userId)
          .then((ele) => {
            // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] showCtrlComponent, loadComponent res ${!!ele}`);
            if (ele) {
              this.ctrlCom = ele;
              this.initCtrlCom();
              // TCIC.SDK.instance.reportLog('updateVideoHover', `[VideoComponent][${this.userId}] showCtrlComponent, loadComponent success, isHover ${!!this.isHover}`);
              this.ctrlCom.getVueInstance().updateVideoHover(this.isHover);
            }
          });
      } else {
        // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] showCtrlComponent, updateComponent, show, left ${videoCtrlLeft}, top ${videoCtrlTop}`);
        TCIC.SDK.instance.updateComponent('video-ctrl-component', {
          left: videoCtrlLeft,
          top: videoCtrlTop,
          transform: `scale(${videoCtrlScale})`,
          display: 'block',
        }, this.userId);
        this.ctrlCom.getVueInstance().updateVideoHover(true);
      }
      this.showVideoShade(true);
    },
    showVideoShade(value) {
      const videoShade = document.getElementById(`video_shade_${this.userId}`);
      if (videoShade) {
        videoShade.style.display = value ? 'block' : 'none';
      }
    },
    // 加载大教学控制栏
    showCollegeClassCtrlComponent(event) {
      // console.log(`VideoComponent[${this.userId}]::loadCtrlComponent`);
      if (!this.isCollegeClass) {  // 不是大教学模式则忽略
        return;
      }
      if (!this.isTeacher && !this.isSelf) {  // 不是老师或自己则忽略
        return;
      }
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const layout = TCIC.SDK.instance.getState(Constant.TStateCollegeVideoLayout);
      let width = 142;
      if (classInfo.teacherId !== this.userId) {
        width = 164;
      }
      let height = 184;
      if (this.isFullScreen) {
        height += 70;
      }
      if (this.isShowCtrlVideo) {
        height += 70;
      } else {
        height += 26;
      }
      const curRect = this.$el.getBoundingClientRect();
      let top = (curRect.top + curRect.height / 2) - height / 2;
      let left = curRect.left - width - 26;
      if (this.isFullScreen || Constant.TConstantCollegeLayoutNormal !== layout) {
        top = Math.max(event.clientY + 20, 0);
        left = Math.max(event.clientX - width / 2, 0);
        top = Math.min(document.body.clientHeight - height, top);
        left = Math.min(document.body.clientWidth - width, left);
        if (layout === Constant.TConstantCollegeLayoutCombo) {    // 组合布局显示在视频栏下面
          top = Math.max(Constant.TConstantVideoHeight + 20, top);
        }
        if (layout === Constant.TConstantCollegeLayoutWall) {    // 宫格布局显示在视频栏下面
          if (curRect.bottom - top  <  height) {
            if (curRect.bottom > height) {
              top = curRect.bottom - height;
            } else {
              top = curRect.top;
            }
          }
        }
      }

      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, this.userId);
      const vodctrlElement = TCIC.SDK.instance.getComponent('collclass-vod-ctrl-component');

      TCIC.SDK.instance.updateComponent('collclass-vod-ctrl-component', {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        zIndex: 401,
        display: 'block',
      });
      this.ctrlCom = vodctrlElement;
      this.initCtrlCom();
      this.ctrlCom.getVueInstance().showInTime(this.userId, this.isFullScreen);
      this.ctrlCom.getVueInstance().updateShareScreen(this.isShareScreenEnable);
      this.ctrlCom.getVueInstance().updateShowVideo(this.isShowCtrlVideo);
    },
    // 鼠标事件
    onMouseOver(evt) {
      // TCIC.SDK.instance.reportLog('updateVideoHover', `[VideoComponent][${this.userId}] onMouseOver ${evt?.type}, isHover true`);
      this.isHover = true;
      const isShowCtrl = this.isShowCtrl;
      const needHide = isShowCtrl && this.isMobile;
      console.log('test: mouse over into', needHide);
      if (needHide) {
        this.hideCtrlComponent();
      } else {
        this.showCtrlComponent();
      }
    },
    onMouseLeave(evt) {
      // TCIC.SDK.instance.reportLog('updateVideoHover', `[VideoComponent][${this.userId}] onMouseLeave ${evt?.type}, isHover false, ctrlCom ${!!this.ctrlCom}`);
      this.isHover = false;
      if (this.ctrlCom) {
        this.ctrlCom.getVueInstance().updateVideoHover(false);
      }
    },
    onClick(event) {
      this.showCollegeClassCtrlComponent(event);
    },
    onTouchStart(evt) {
      console.log('test: touch Start mouse over hidden');
      this.onMouseOver(evt);
    },
    onTouchEnd(evt) {
      this.onMouseLeave(evt);
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
      if ((this.isLiveClass && !TCIC.SDK.instance.isClassLayoutHasDoc())
        || (this.isLiveClass && this.isTeacherVideo)
        || (TCIC.SDK.instance.isInteractClass() && classLayout === TCIC.TClassLayout.Three && this.isTeacherVideo)) {
        // - 公开课纯视频布局下、所有视频不能双击
        // - 公开课不能双击老师的视频
        // - 互动班课三分屏布局时，老师的视频不能双击
        return;
      }
      const isBigVideoMode = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
      if (TCIC.SDK.instance.isInteractClass() && isBigVideoMode && classLayout === TCIC.TClassLayout.Three && !this.isTeacherVideo) {
        return;
      }
      const curTime = new Date().getTime();
      if (curTime - this.touchTime < 800 && !this.isTeacher) {   // Android端不支持dbclick事件，自己模拟双击
        this.toggleMaxVideo();
      } else {
        this.touchTime = curTime;
      }
    },
    onBoundingClientRectChange(rect) {
      console.info(`=====>VideoComponent::onBoundingClientRectChange=>${JSON.stringify(rect)}, needRender ${this.needRender}, viewInited ${this.viewInited}`);
      if (!this.viewInited && rect && rect.width > 0 && rect.height > 0 && this.$refs.video) {
        this.viewInited = true;
        if (this.needRender && !this.hasRendered) {
          TCIC.SDK.instance.reportLog('bindRemoteVideoDom', `[VideoComponent] videoUserId: ${this.userId}, onBoundingClientRectChange`);
          this.videoSequence = TCIC.SDK.instance.bindRemoteVideoDom(
            this.userId,
            this.remoteVideoStreamType,
            this.$refs.video,
          );
          this.hasRendered = true;
        }
      }
      this.onVisibleChanged(rect);
    },
    onVisibleChanged(targetRect) {
      if (TCIC.SDK.instance.isLiveClass() || !this.isRTCMode) {
        // 快直播不播放没有声音，这些场景关闭懒加载
        return;
      }
      if (!this.pauseVideoHide) {
        // 不需要隐藏时暂停
        return;
      }
      const rect = targetRect || this.$el.getBoundingClientRect();
      if (this.isRectInViewport(rect)) {
        this.onResume();
      } else {
        this.onPause();
      }
    },
    startRenderVideo(reason) {
      this.needRender = true;
      if (!this.viewInited) {
        const rect = this.$el.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          this.viewInited = true;
        }
      }
      if (this.hasRendered) {
        return;
      }
      TCIC.SDK.instance.reportLog(
        'startRenderVideo',
        `[VideoComponent] videoUserId: ${this.userId}, viewInited ${this.viewInited}, hasVideoDom ${!!this.$refs.video}, reason: ${reason}`,
      );
      if (this.viewInited && (!this.audioFlag || this.manualVideo) && this.$refs.video) {   // 非音频上课且未手动开启才渲染
        TCIC.SDK.instance.reportLog('bindRemoteVideoDom', `[VideoComponent] videoUserId: ${this.userId}, startRenderVideo`);
        this.videoSequence = TCIC.SDK.instance.bindRemoteVideoDom(
          this.userId,
          this.remoteVideoStreamType,
          this.$refs.video,
        );
        this.hasRendered = true;
      }
    },
    stopRenderVideo(reason) {
      this.needRender = false;
      if (!this.hasRendered) {
        return;
      }
      TCIC.SDK.instance.reportLog(
        'stopRenderVideo',
        `[VideoComponent] videoUserId: ${this.userId}, viewInited ${this.viewInited}, hasVideoDom ${!!this.$refs.video}, reason: ${reason}`,
      );
      TCIC.SDK.instance.unbindRemoteVideoDom(this.userId, this.remoteVideoStreamType, this.videoSequence);
      this.hasRendered = false;
    },
    // 初始化控制栏
    initCtrlCom() {
      if (this.ctrlCom) {
        const vueObj = this.ctrlCom.getVueInstance();
        vueObj.setToggleMicListener(this.toggleMic);
        vueObj.setToggleCameraListener(this.toggleCamera);
        vueObj.setToggleBoardListener(this.toggleBoard);
        vueObj.setResetDragListener(this.resetDrag);
        vueObj.setToggleStageListener(this.toggleStage);
        if (TCIC.SDK.instance.isAndroid() || TCIC.SDK.instance.isIOSNative()) {
          if (vueObj.setInnerRotate) {
            vueObj.setInnerRotate(this.rotate90);
          }
        }
        // 大教学模式是老师将学生视频流放到屏幕中间来共享
        if (this.isCollegeClass) {
          vueObj.setToggleScreenListener(this.toggleShareScreen);
        }
        vueObj.setToggleVideoWallListener(this.toggleVideoWall);
      }
      this.updateCtrlStatus();
    },
    setFooterCtrlCom(com) {
      // 公开课pc/pad设置VodFooterCtrlComponent为视频控制栏
      // TCIC.SDK.instance.reportLog('updateVideoCtrl', `[VideoComponent][${this.userId}] setFooterCtrlCom ${!!com}`);
      if (com) {
        this.isShowVodFooterCtrl = true;
        this.ctrlCom = com;
        this.initCtrlCom();
      } else {
        this.isShowVodFooterCtrl = false;
        this.ctrlCom = null;
      }
    },
    // 更新控制栏状态
    updateCtrlStatus() {
      // eslint-disable-next-line no-nested-ternary
      const micStatus = this.isMicOpen ? 'open' : (
        TCIC.SDK.instance.isDeviceAbnormal(this.micDeviceStatus) ? 'error' : 'close'
      );
      // eslint-disable-next-line no-nested-ternary
      const cameraStatus = this.isCameraOpen ? 'open' : (
        TCIC.SDK.instance.isDeviceAbnormal(this.cameraDeviceStatus) ? 'error' : 'close'
      );
      // 记录mic是否异常
      this.isMicError = micStatus === 'error';
      let micCtrlStatus = micStatus;
      let cameraCtrlStatus = cameraStatus;
      if (this.isStudent) {
        if (this.isSelf) {
          micCtrlStatus = (this.isMicEnable || this.isPreviewMode) ? micStatus : 'disable';
          cameraCtrlStatus = (this.isCameraEnable || this.isPreviewMode) ? cameraStatus : 'disable';
        } else {
          micCtrlStatus = 'disable';
          cameraCtrlStatus = 'disable';
        }
      }
      if (this.isSelf) {
        console.log(`[VideoComponent] updateCtrlStatus, micCtrlStatus ${micCtrlStatus}, cameraCtrlStatus ${cameraCtrlStatus}`);
      }
      if (this.ctrlCom) {
        const vueObj = this.ctrlCom.getVueInstance();
        vueObj.updateMicStatus(micCtrlStatus);
        vueObj.updateCameraStatus(cameraCtrlStatus);
        if (this.isControllerRole) {
          if (this.isTeacherVideo || this.isAssistantVideo) {
            // 检测是否要显示视频墙
            const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout);
            const isShowPlayer = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible);
            const isShowVideoWall = !this.isVideoOnlyClass
              && !(classLayout === TCIC.TClassLayout.Three && !this.isOneOnOneClass)
              && !isShowPlayer
              && !Util.isScreenShare();
            const videoWallStatus = this.isVideoWall ? 'on' : 'off';
            vueObj.updateVideoWallStatus(isShowVideoWall ? videoWallStatus : 'hide');
          }
          if (this.isTeacherVideo) {  // 老师自己无需显示
            vueObj.updateBoardStatus('hide');
            vueObj.updateStageStatus('hide');
          } else {
            let boardStatus = this.isBoardEnable ? 'enable' : 'disable';
            if (!TCIC.SDK.instance.isClassLayoutHasDoc()) {
              boardStatus = 'hide';
            }
            if (TCIC.SDK.instance.isLiveClass()) {
              boardStatus = 'hide';
            }
            vueObj.updateBoardStatus(boardStatus);
            vueObj.updateStageStatus('on');
          }
          vueObj.updateEnableReset(this.isShowReset);
        } else {
          vueObj.updateBoardStatus('hide');
          vueObj.updateEnableReset(false);
        }
      }

      if (this.isSelf) {
        this.$EventBus.$emit('self-mic-ctrl-status', micCtrlStatus);
        this.$EventBus.$emit('self-camera-ctrl-status', cameraCtrlStatus);
      }
    },
    renderWaterMark() {
      if (!this.isRecordMode) {
        const waterMarkParams = TCIC.SDK.instance.getWaterMarkParam();
        WaterMark.render(this.$refs.component, waterMarkParams, 'video');
      }
    },
    async renderMarquee() {
      if (!this.isRecordMode) {
        const marqueeParam = TCIC.SDK.instance.getMarqueeParam();
        const userInfo = await TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId());
        Marquee.render(this.$refs.component, marqueeParam, userInfo);
      }
    },
    destroyWaterMark() {
      WaterMark.destroy(this.$refs.component, 'video');
    },
    destroyMarquee() {
      Marquee.destroy(this.$refs.component);
    },

    permissionChangeHandler() {
      const watchPermissionList = () => {
        // 权限变化
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Modify_Class, this.onClassInfoUpdate.bind(this));
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.updatePermissionList.bind(this));
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_Member_Action, ({ action, operatorId }) => {
          // 老师执行memberAction修改学生权限，但是学生之前权限已经是该状态时，触发本事件
          // 这里需要执行的操作是强制打开学生设备，因此重新处理一下权限列表即可
          // 先将自己的权限状态更新为false，方便后续判断状态变更
          if (action === TCIC.TMemberActionType.Camera_Open) {
            this.isCameraEnable = false;
          } else if (action === TCIC.TMemberActionType.Mic_Open) {
            this.isMicEnable = false;
          }
          if (operatorId !== this.userId && operatorId) {
            this.permissionOperatorId = operatorId;
            this.isTeacherOperator = TCIC.SDK.instance.isTeacher(operatorId);
            this.recvOperator = true;
          }
          this.updatePermissionList(TCIC.SDK.instance.getPermissionList(), TCIC.TPermissionUpdateReason.Action);
        });
        // 立即更新权限状态
        this.updatePermissionList(TCIC.SDK.instance.getPermissionList(), TCIC.TPermissionUpdateReason.Init);
      };
      if (TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin)) {
        watchPermissionList();  // 上课前直接打开
      } else {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
          .then(watchPermissionList);
      }
    },
    initCollegeClass() {
      // 课堂还未开始
      // 1. 老师打开本地麦克风及摄像头
      // 2. 学生打开麦克风
      // TODO 代码和注释不一致，但是现在没有 CollegeClass 无法确认，先不动。。。
      if (TCIC.SDK.instance.getClassInfo().status === TCIC.TClassStatus.Not_Start) {
        this.isLocalRendering = true;
        TCIC.SDK.instance.reportLog('startLocalVideo', '[VideoComponent] initCollegeClass');
        TCIC.SDK.instance.startLocalVideo(this.$refs.video);
        if (this.isTeacher) {
          this.isMicOpen = true;
          TCIC.SDK.instance.reportLog('startLocalAudio', '[VideoComponent] initCollegeClass, isTeacher');
          TCIC.SDK.instance.startLocalAudio(this.$refs.audio);
        }
      }
    },
    isRectInViewport(rect) {
      const isDoubleLine = TCIC.SDK.instance.getState(Constant.TStateVideoDubbleLine, false);
      if (isDoubleLine && TCIC.SDK.instance.isTeacher() && !this.isSelf) {  // 老师双排布局的学生视频位置需要计算zoom
        const zoom = 0.49;
        return (
          rect.bottom > 0
          && rect.right > 0
          && rect.top * zoom  < (window.innerHeight || document.documentElement.clientHeight)
          && rect.left * zoom < (window.innerWidth || document.documentElement.clientWidth)
        );
      }
      return (
        rect.bottom > 0
        && rect.right > 0
        && rect.top < (window.innerHeight || document.documentElement.clientHeight)
        && rect.left < (window.innerWidth || document.documentElement.clientWidth)
      );
    },
    initCoTechingClass() {   // 双师课初始逻辑
      if (!this.isSelf) return;   // 仅处理自己的视频组件
      if (!TCIC.SDK.instance.isTeacher()) {  // 学生端
        const taskUpdate = (taskInfo) => {
          if (taskInfo.taskId === Constant.TConstantCTVideoList) {  // 列表
            if (taskInfo.seq <= this.lastTaskId) return;    // 忽略过期的任务信息
            try {
              const jsonData = JSON.parse(taskInfo.content);
              this.coTeachingPublishMyVideo = jsonData.array.includes(TCIC.SDK.instance.getUserId());
              TCIC.SDK.instance.muteLocalVideo(!this.coTeachingPublishMyVideo);
              this.lastTaskId = taskInfo.seq;
            } catch (err) {
              console.warn(`VideoComponent::onTaskUpdate=>parse task ${taskInfo.taskId} fail: ${err.toString()}`);
            }
          } else if (taskInfo.taskId === Constant.TConstantCoTeachingMuteAll) { // 全员静音
            window.showToast(i18next.t('{{arg_0}}已开启全员静音，你可以举手申请发言~', { arg_0: this.roleInfo.teacher }), 'error');
            this.isMicOpen && this.enableMic(false);
          }
        };
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, taskUpdate);
        // 更新任务信息
        TCIC.SDK.instance.getTasks(0).then((tasks) => {
          if (tasks && tasks.tasks && tasks.tasks.length > 0) {
            tasks.tasks.forEach(task => taskUpdate(task));
          }
        });
        if (!TCIC.SDK.instance.isFeatureAvailable('CoTeachingAutoMic')) {
          // 学生连麦时更新自己麦克风
          this.addLifecycleTCICStateListener(Constant.TStateCoTeachingLinkUsers, (linkUsers) => {
            const isLink = linkUsers && linkUsers.includes(TCIC.SDK.instance.getUserId());
            if (!this.isCoTeachingLinkUser && isLink) { // 连麦时打开麦克风
              !this.isMicOpen && this.enableMic(true);
            } else if (this.isCoTeachingLinkUser && !isLink) {  // 结束连麦时关闭麦克风
              this.isMicOpen && this.enableMic(false);
            }
            this.isCoTeachingLinkUser = isLink;
          });
        }
      } else {  // 老师端始终上行视频
        this.coTeachingPublishMyVideo = true;
        TCIC.SDK.instance.muteLocalVideo(!this.coTeachingPublishMyVideo);
      }
    },
    addLinkCountObserver() {
      let videoStatus = this.$el.getElementsByClassName('video__status');
      if (videoStatus && videoStatus.length > 0) {
        videoStatus = videoStatus[0];
      }
      if (videoStatus && typeof window.ResizeObserver !== 'undefined') {
        const resizeObserver = new ResizeObserver(() => {
          const width = videoStatus.clientWidth;
          this.linkedCountLeftCSS = `left: ${width + 4}px`;
        });
        resizeObserver.observe(videoStatus);
        this.linkCountResizeObserver = resizeObserver;
        const width = videoStatus.clientWidth;
        this.linkedCountLeftCSS = `left: ${width + 4}px`;
      }
    },
    removeLinkCountObserver() {
      let videoStatus = this.$el.getElementsByClassName('video__status');
      if (videoStatus && videoStatus.length > 0) {
        videoStatus = videoStatus[0];
      }
      if (videoStatus && this.linkCountResizeObserver) {
        this.linkCountResizeObserver.unobserve(videoStatus);
      }
    },
    setVideoStatusBtnSmallSize(opt) {
      if (this.isSmallScreen) {
        this.isVideoStatusBtnSmallSize = !!opt;
      }
    },
  },
};

</script>

<style lang="less">
.video-component {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  pointer-events: all;
  background-image: linear-gradient(180deg,#343e5c,#283047);
  &.trans-style {
    transform: translateZ(0);
  }
  &.no-border-radius {
    background-color: #242A3E;
    border-radius: 1px;
  }
  &:after {
    content: "";
    position: absolute;
    z-index: 10;
    height: 100%;
    width: 100%;
    top: 0;
    box-sizing: border-box;
    border: solid 3px transparent;
    pointer-events: none;
  }
  &.focus {
    &:after {
      border: solid 3px #13a449;
    }
  }

  &.ios-layout {
    .video__content {
      .trtc__video {
        -webkit-transform: translate3d(0,0,0);      /** 修复iOS部分机型视频显示问题 */
      }
    }
  }
  /** 大教学模式样式 */
  &.big-class-mode {

    &.focus {
      &:after {
        border: none;
      }

      .video__content {
        .video__status {
          i.video__volume_bk {
            content: url('./assets/bc_mic_on.png');
          }
          i.video__volume {
            content: url('./assets/bc_mic_full.png');
          }
        }
      }
    }

    .video__content {
      .share-tips {
        z-index: 3;
        position:absolute;
        top:73px;
        left: calc((100% - 408px)/2);
        width: 408px;
        height: 70px;
        color: #FFFFFF;
        font-size: 16px;
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 10px;
        .share-items {
          margin-top: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          .share-icon{
            width: 24px;
            height: 24px;
            content: url('./assets/share_icon.png');
          }
          .share-name{
            display: flex;
            margin-left: 10px;
            font-size: 20px;
            line-height: 30px;
          }
        }
      }
      .video__mask {
        z-index: auto;
        background-position: center;
        background-color: #242A3E;
        filter: blur(18px);
      }

      .trtc__video {
        position: relative;
      }
      .video__status {
        width: 100%;
        max-width: 100%;
        height: 36px;
        margin-left: 0px;
        bottom: 0;
        background: linear-gradient(180deg, transparent, #000);
        border-radius: 0 0 4px 4px;
        align-items: center;

        /** 移除老师校正(没有讲师标志) */
        &.teacher-fix {
          margin-left: 0px;
        }

        &.in-whiteboard {
          width: auto;
          padding: 0 12px;
          background: #000;
          opacity: .4;
          margin-left: 16px;
          margin-bottom: 16px;

          .video__name {
            font-size: 20px;
          }
        }

        i {
          margin-left: 5px;
          padding: 3px;

          &.video__volume_bk {
            content: url('./assets/bc_mic_on.png');
          }

          &.video__silence {
            content: url('./assets/bc_mic_off.png');
          }
        }

        .video__name {
          margin-left: 30px;
          font-size: 14px;
          line-height: 20px;
        }
      }
      .handup_status {
        z-index: 2;
        position: absolute;
        max-width: 40px;
        max-height: 40px;
        margin-left: 4px;
        margin-top: 4px;
        top: 0;
        i.handup_icon {
          position: absolute;
          content: url('./assets/video_handup_icon.svg');
          width: 40px;
          height: 40px;
          }
      }
    }
  }
  .screen-share-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.85);
    z-index: 2;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    color: rgba(255,255,255, .75);
    font-size: 20px;
    .stop {
      margin-top: 20px;
      font-size: 16px;
    }
  }
  .video_auto_play {
    top: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #22262E;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 99;
    opacity: 0.8;
    .play_icon {
      width: 50px;
      height: 50px;
      max-width: 70%;
      max-height: 70%;
      background-image: url('./assets/auto_play.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50%;
    }
  }
  .video_close {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #22262E;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &.hidden{
      display: none;
    }
    .icon {
      width: 120px;
      height: 120px;
      max-width: 70%;
      max-height: 70%;
      background-image: url('./assets/no-video.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50%;
    }
  }
  .ct_video_close {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .icon {
      width: 96px;
      height: 96px;
      background-image: url("./assets/ic_video_close.svg");
      margin-bottom: 12px;
    }
    .text {
      width: 120px;
      height: 32px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 24px;
      color: #FFFFFF;
      letter-spacing: 0;
      text-align: center;
    }
  }
  .video__content {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
    background-size: cover;
    background-repeat: no-repeat;

    .tcplayer {
      width: 100% !important;
      height: 100% !important;
      pointer-events: none;
      video {
        width: 100% !important;
        height: 100% !important;
      }
      .vcp-bigplay {
        pointer-events: none; // 不需要点击暂停
      }
    }

    .video__avatar {
      position: absolute;
      width: 60px;
      height: 60px;
      left: calc(50% - 30px);
      top: calc(50% - 30px);
      border-radius: 30px;
      background-position: 50%;
      background-size: contain;
      background-repeat: no-repeat;
      color: #fff;
      font-size: 18px;
      line-height: 60px;
      text-align: center;
      background-color: rgba(51, 58, 83, .8);
    }

    &.no-border-radius {
      border-radius: 1px;
    }

    .video__mask {
      position: absolute;
      z-index: 1000;
      width: 100%;
      height: 100%;
    }

    .video__reset {
      z-index: 0;
      position: absolute;
      width: 100%;
      height: 100%;

      .reset_tips {
        display: flex;
        color: white;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
      }

      .reset_btn {
        position: absolute;
        top: 0px;
        right: 0px;
        padding: 5px;
        color: #88f;
        text-decoration: underline;
      }
    }

    .trtc__video {
      z-index: 1;
      position: absolute;
      // pointer-events: none;
      width: 100%;
      height: 100%;
      &.no-video {
          width: 1px;
          height: 1px;
          overflow: hidden;
        }
      &.hide-video-view {
        opacity: 0;
      }
    }

    .trtc__audio {
      width: 0;
      height: 0;
    }

    .video__status {
      z-index: 2;
      position: absolute;
      height: 24px;
      bottom: 0;
      width: auto;
      max-width: 100%;
      overflow: hidden;
      background: rgba(28, 33, 49, 0.7);
      border-radius: 0 4px 0 0;
      -webkit-transform: translate3d(0,0,0);      /** 修复iOS部分机型状态栏消息问题 */
      display: flex;

      i.video-role {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        padding: 0 4px;
        &.teacher {
          background: linear-gradient(153deg, #00a7ff, 0%, #006eff 95%);
        }
        &.assistant {
          background: linear-gradient(152deg, #23be82 0%, #08ae6e 94%);
        }
        .role-text {
          font-size: 12px;
          zoom: 0.7;
          color: #fff;
        }
      }

      .video__mic {
        flex-shrink: 0;
        margin-left: 2px;
        width: 24px;
        height: 24px;
      }

      i.video__volume {
        position: absolute;
        clip: rect(16px, 16px, 16px, 0px);
        content: url('./assets/volume_2.svg');
        width: 24px;
        height: 24px;
        padding: 4px;
      }

      i.video__volume_bk {
        position: absolute;
        content: url('./assets/bc_mic_on.png');
        width: 24px;
        height: 24px;
        padding: 4px;
      }

      i.video__volume_off {
        position: absolute;
        content: url('./assets/bc_mic_off.png');
        width: 24px;
        height: 24px;
        padding: 4px;
      }
      i.video__volume_error {
        position: absolute;
        content: url('./assets/bc_mic_error.png');
        width: 24px;
        height: 24px;
        padding: 4px;
      }
      i.video__screen {
        content: url('./assets/bc_screen.png');
        width: 24px;
        height: 24px;
        padding: 4px;
        margin-top: 3px;
        margin-left: -12px;
      }
      i.video__name {
        max-width: 80px;
        color: #DCEBEB;
        font-size: 12px;
        font-weight: 300;
        line-height: 24px;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 4px;
        height: 24px;

      }
      &.video__status-coteaching {
        top: 0;
        border-radius: 4px 0 4px 0;
        // backdrop-filter: blur(12px);
      }
      &.small-screen {
        background: rgba(0, 0, 0, 0.8);
        bottom: 5%;
        left: 3%;
        border-radius: 6px;
        i.video-role {
          &.teacher {
            width: auto;
            border-radius: 5px;
          }
          .role-text {
            font-size: 16px;
          }
        }
        .role-icon {
          background-image: url('./assets/role-teacher.svg');
          background-size: 100%;
          width: 11px;
          height: 14px;
          background-position: 50%;
          margin-right: 2px;
        }
        i.video__name {
          max-width: 60px;
        }
        i.video__volume {
          content: url('./assets/mic_speak.svg')
        }
        i.video__volume_bk {
          content: url('./assets/video-ctrl/mobile/mic-open.svg');
        }

        i.video__silence {
          content: url('./assets/video-ctrl/mobile/mic-close.svg');
        }
      }
      &.small-size {
        transform: scale(0.8);
        bottom: 0;
        left: 0;
      }
    }
  }
  &.mobile {
    background: none;
    border-radius: 4px;
    .video_close {
      .icon {
        border-radius: 15px;
      }
    }
    .video__content {
      cursor: pointer;
      pointer-events: auto;
    }
    .video_content_shade {
      width: 100%;
      height: 100%;
      background: black;
      opacity: .4;
      position: absolute;
      z-index: 200;
      display: none;
    }
  }

  &.big-class-full-screen {
    #video__status {
      top: 120px;
      width: auto;
      left: 30px;
      background: #1C2131;
      border-radius: 4px;
      opacity: 0.7;
      height: 26px;

      .video__name {
        margin-left: 26px;
        font-size: 14px;
        line-height: 18px;
        margin-right: 4px;
      }

      .video__mic {
        width: 22px;
        height: 22px;
      }
    }
  }
  .ct-link-count-wrap {
    pointer-events: none;
    position: absolute;
    z-index: 2;
    top: 2px;
    overflow: hidden;
    background-image: linear-gradient(161deg, #FDDF1F 0%, #FDCA1F 96%);
    border: 1px solid rgba(255,255,255,0.08);
    box-shadow: 0 4px 12px 0 rgba(0,0,0,0.20);
    border-radius: 10px;
    height: 20px;
    display: flex;
    .ct-link-count-icon {
      width: 25px;
      height: 20px;
      background: url('./assets/ct-link-count.png') no-repeat center center;
      border-radius: 4px;
      background-size: contain;
      margin-left: 8px;
      margin-top: -2px;
    }

    .ct-link-count {
      width: 16px;
      height: 36px;
      font-family: Arial-BoldMT;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      line-height: 16px;
      margin-right: 2px;
      margin-left: 1px;
    }
  }

  .debug-info-switch {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .debug-info {
    position: absolute;
    left: 0;
    bottom: 30px;
    max-height: calc(100% - 30px);
    overflow-y: auto;
    z-index: 3;
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 6px;

    &.small-screen {
      left: 3%;
      bottom: calc(5% + 30px);
      max-height: calc(90% - 30px);
    }

    div {
      white-space: nowrap;
    }
  }
  .video-touch-container{
    height: 100%;
    position: absolute;
    // background: #ccc;
    width: 100%;
    z-index: 2;
  }

}
</style>
