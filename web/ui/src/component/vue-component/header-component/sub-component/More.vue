<template>
  <div class="more-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      :disabled="isMobile || isShowMore"
      class="item"
      placement="bottom-end"
      :content="$t('更多')"
    >
      <el-popover
        ref="popover"
        v-model="isShowMore"
        placement="bottom-end"
        popper-class="more-popover"
        trigger="manual"
        @show="onMoreShow"
        @hide="onMoreHide"
      >
        <button
          slot="reference"
          ref="more"
          class="header__button button--secondary"
          :class="{active: isShowMore}"
          data-user-event="More-click"
          @click="isShowMore = !isShowMore"
        >
          <div class="tool-item-wrap">
            <i class="header__i i--menu icon-more" />
            <span class="header__btn-text">{{ $t('更多') }}</span>
          </div>
        </button>
        <ul
          v-show="isShowMore"
          :class="['header-more-box', { 'device-pad': isPad, 'wide-text': isWideText }]"
        >
          <template v-for="item in menu">
            <li
              v-if="item.isSub"
              :key="item.name"
              :data-user-event="`More-${item.name}`"
              :class="'tool-' + item.name"
            >
              <!-- popover组件 -->
              <el-popover
                v-if=" item.type === 'popover' "
                placement="bottom-end"
                :width="item.width"
                popper-class="header-component-popover"
                trigger="click"
                @show="() => { item.active = true }"
                @hide="() => { item.active = false }"
              >
                <Component
                  :is="item.name"
                  :component="item"
                />
                <button
                  slot="reference"
                  :ref="item.name"
                  class="header__button button--secondary"
                  :class="{active: item.active}"
                >
                  <div class="tool-item-wrap">
                    <el-badge
                      :value="item.badge"
                      :max="99"
                      class="badge"
                      :hidden="item.badge === 0"
                    >
                      <i :class="`header__i i--menu icon-${item.name}`" />
                    </el-badge>
                    <span class="header__btn-text">{{ $t('更多') }}</span>
                  </div>
                </button>
              </el-popover>
              <!-- 自定义组件 -->
              <Component
                :is="item.name"
                v-else
                :ref="item.name"
                class="inMoreComponent"
                :component="item"
              />
            </li>
            <li
              v-else
              :key="item.name+index"
              :data-user-event="`More-${item.name}`"
              :class="'tool-' + item.name"
            >
              <div class="inMoreComponent">
                <el-tooltip
                  class="item"
                  :disabled="!item.label"
                  :content="$t(item.label)"
                  placement="bottom"
                >
                  <button
                    :ref="item.name"
                    class="header__button button--secondary"
                    :class="[{active: item.active}, item.class || '']"
                    @click="item.action"
                  >
                    <div class="tool-item-wrap">
                      <el-badge
                        :value="item.badge"
                        :max="99"
                        class="badge"
                        :hidden="item.badge === 0"
                      >
                        <i
                          :class="`header__i i--menu`"
                          :style="{'background-image': `url(${item.icon})`}"
                        />
                      </el-badge>
                      <span class="header__btn-text">{{ $t('更多') }}</span>
                    </div>
                  </button>
                </el-tooltip>
              </div>
            </li>
          </template>
        </ul>
      </el-popover>
    </el-tooltip>
  </div>
</template>

<script>
import Quiz from './Quiz';
import PopoverComponent from '@core/PopoverComponent';
import Layout from './Layout';
import Member from './Member';
import AllMemberController from './AllMemberController';
import Setting from './Setting';
import Message from './Message';
import ClassDuration from './ClassDuration';
import Document from './Document';
import ToolBox from './ToolBox';
import Invite from './Invite';
import ScreenCapture from './ScreenCapture';
import Lodash from 'lodash';
import Notice from './Notice';
import Constant from '@/util/Constant';
import Camera from './Camera';
import Speaker from './Speaker';
export default {
  name: 'MoreSubComponent',
  components: {
    Quiz,
    AllMemberController,
    Setting,
    Layout,
    ToolBox,
    Member,
    Message,
    ClassDuration,
    Document,
    Invite,
    ScreenCapture,
    Notice,
    Camera,
    Speaker
  },
  extends: PopoverComponent,
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isPad: TCIC.SDK.instance.isPad(),
      isTeacher: false,
      isShowMore: false,
      menu: [],
      lang: TCIC.SDK.instance.getLanguage(),
    };
  },
  mounted() {
    this.initPopoverComponents();
    this.isTeacher = TCIC.SDK.instance.isTeacher();

    const clickEvent = this.isMobile ? 'touchend' : 'mousedown';
    document.addEventListener(clickEvent, (event) => {
      const path = event.path || (event.composedPath && event.composedPath());
      let isInComponent = false;
      path.forEach((item) => {
        if (item.classList && item.classList.contains('inComponent')) {
          isInComponent = true;
        }
      });
      Object.keys(this.$refs).forEach((key) => {
        const refItem = this.$refs[key];
        // eslint-disable-next-line no-underscore-dangle
        if (Array.isArray(refItem) && refItem[0]) {
          const ref = refItem[0];
          // eslint-disable-next-line no-underscore-dangle
          if (ref && ref._props) {
            // eslint-disable-next-line no-underscore-dangle
            const active = refItem.find(item => item._props.component.active === true);
            if (active && !isInComponent && !ref.$el.contains(event.target)) {
              active.onHide?.();
            }
          }
        }
      });
    }, true);
  },
  beforeDestroy() {
  },
  methods: {
    initPopoverComponents() {
      this.menu.forEach((item) => {
        if (item.type === 'popover' && item.enable === true && item.hiddenWhenMoreBtnShow === true) {
          this.addPopoverComponent(item);
        }
      });
    },
    update: Lodash.throttle(function (menu) {
      // 节流
      this.menu = menu;
      Object.keys(this.$refs).forEach((key) => {
        const ref = this.$refs[key];
        if (ref && ref[0] && ref[0].onHide) {
          ref[0].onHide();
        }
      });
    }, 200),
    onMoreShow() {
      this.isShowMore = true;
      this.$nextTick(() => {
        this.$refs.popover.updatePopper();
        // 抛出消息让气泡下移
        TCIC.SDK.instance.setState(Constant.TStateVideoTipsComponentDown, this.isShowMore);
        // 注册按钮
        const nodes = [];
        const menus = Array.from(document.querySelectorAll('ul.header-more-box li button.header__button'));
        menus.forEach((item) => {
          if (!item.disabled) nodes.push(window.tbm.generateNode(item));
        });
        window.tbm.updateTarget('header', nodes, 'subMenu');
      });
    },
    onMoreHide() {
      Object.keys(this.$refs).forEach((key) => {
        const refItem = this.$refs[key];
        if (Array.isArray(refItem) && refItem[0]) {
          const ref = refItem[0];
          // eslint-disable-next-line no-underscore-dangle
          if (ref && ref._props) {
            if (ref.onHide) {
              ref.onHide();
            }
            // eslint-disable-next-line no-underscore-dangle
            ref._props.component.active = false;
          }
        }
      });
      this.$refs.popover?.doClose();
      this.isShowMore = false;
      TCIC.SDK.instance.setState(Constant.TStateVideoTipsComponentDown, this.isShowMore);
      window.tbm.updateTarget('header', [], 'subMenu');
    },
  },
};
</script>
<style lang="less">
.more-sub-component {

  .icon-more {
    background-image: url('../assets/new-icons/ic_more.svg');
  }
}

.more-popover {
  position: relative;
  &.el-popper {
    background-color: rgba(29, 32, 41, 1);
    box-shadow: 0px 6px 20px rgba(0, 5, 19, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    border: none;
  }

  .header-more-box {
    display: flex;

    .el-tooltip {
      display: block;
    }

    li {
      width: 60px;
      text-align: center;
      color: #fff;

      > div {
        width: 100%;
      }
    }

    .button--secondary {
      border: none;
      padding: 2px 4px;
      color: var(--btn--secondary--text-color, #fff);
      line-height: 20px;
      font-size: 11px;
      border-radius: 2px;
      outline: none;
      cursor: pointer;
      display: flex;
      justify-content: start;
      min-width: 52px;
      width: 100%;
      padding: 4px 0;
      border: 1px solid transparent;
      &:hover,
      &.active {
        color: #006eff;
      }
      .tool-item-wrap {
        height: 44px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
        span {
          white-space: nowrap;
          font-size: 12px;
          line-height: 17px;
          text-align: center;
          color: #A3AEC7;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      // &.active {
      //   background: #2971ff !important;
      // }
    }

    .header__i.i--menu {
      width: 24px;
      height: 24px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      vertical-align: middle;
    }

    .header__i {
      display: inline-block;
    }
  }

  .header-more-box.wide-text li {
    width: 72px;
  }

  .header-more-box.device-pad li {
    width: 50px;
  }
}

@media screen and (max-width: 1200px){
  .more-popover {
    .header-more-box {
      li {
        width: auto;
      }
    }
  }
}
</style>
