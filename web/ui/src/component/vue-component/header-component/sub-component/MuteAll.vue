<template>
  <div class="whole-header-sub-component">
    <el-tooltip
      :manual="!disabled && interactiveIsTouchEvent()"
      class="item"
      :content="disabled ? translateTip.content : $t('全员操作')"
      placement="bottom"
      :hide-after="2000"
    >
      <el-popover
        popper-class="header-component-popover more-popover inComponent submenu-popover mute-all-component"
        placement="bottom"
        trigger="click"
      >
        <ul class="header-more-box flex-column">
          <li class="header-menu-li">
            <el-switch
              v-model="value"
              class="whole-switch"
              :active-text="$t('全员静音')"
              active-color="#13A449"
            />
          </li>
          <li
            class="header-menu-li"
            @click="dispatchTrophy"
          >
            <i class="header__i i--menu whole-ic-prize" />{{ $t('全员奖励') }}
          </li>
          <li
            class="header-menu-li"
            @click="resetVideoPosition"
          >
            <i class="header__i i--menu whole-ic-homing" />{{ $t('全员归位') }}
          </li>
          <!--          <li class="header-menu-li" @click="allStageDown">-->
          <!--            <i class="header__i i&#45;&#45;menu whole-ic-down"></i>-->
          <!--            全员下台-->
          <!--          </li>-->
        </ul>
        <button
          slot="reference"
          class="header__button button--secondary"
          :class="{active: isMuteAll}"
          :disabled="disabled"
          @click="controlAllMicStatus"
        >
          <i class="header__i i--menu ic_nav_whole" />
        </button>
      </el-popover>
    </el-tooltip>
    <!--V1.3.4更新备份<el-tooltip
        :manual="!disabled && interactiveIsTouchEvent()"
        class="item"
        :content="disabled ? '开始上课后才可开启全员静音' : (isMuteAll ? '解除静音' : '全员静音')"
        placement="bottom"
        :hide-after="2000"
    >
      <el-popover trigger="manual">
        <button
            class="header__button button&#45;&#45;secondary"
            :class="{active: isMuteAll}"
            :disabled="disabled"
            @click="controlAllMicStatus"
            slot="reference"
        >
          <i class="header__i i&#45;&#45;menu icon-mute"></i>
        </button>
      </el-popover>
    </el-tooltip>-->
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import Lodash from 'lodash';

export default {
  name: 'MuteAllSubComponent',
  extends: BaseComponent,

  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isMuteAll: false,
      disabled: true,
      value: true,
      roomInfo: {},
      roleInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        content: i18next.t('开始{{arg_0}}后才可开启全员操作', { arg_0: this.roomInfo.startRoom }),
      };
    },
  },

  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.disabled = false;
    });

    // 全员静音状态变更
    this.addLifecycleTCICStateListener(TCIC.TMainState.Mute_All, (value) => {
      this.isMuteAll = value;
    });
  },

  beforeDestroy() {
  },
  methods: {
    // 全员静音
    controlAllMicStatus: Lodash.throttle(function () {
      if (TCIC.SDK.instance.isCoTeachingClass()) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        TCIC.SDK.instance.updateTask(Constant.TConstantCoTeachingMuteAll, JSON.stringify({ timeStamp: new Date().getTime() }), -1, false, classInfo.teacherId)
          .then(() => {
            window.showToast(i18next.t('{{arg_0}}已开启全员静音', { arg_0: this.roleInfo.teacher }));
            this.isMuteAll = false;
          })
          .catch((error) => {
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
              arg_0: i18next.t('全员静音'),
              arg_1: error.errorCode, arg_2: error.errorMsg,
            }), 'error');
            this.isMuteAll = false;
          });
      } else {
        TCIC.SDK.instance.muteAll(this.isMuteAll)
          .catch((error) => {
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
              arg_0: this.isMuteAll ? i18next.t('解除全员静音') : i18next.t('全员静音'),
              arg_1: error.errorCode, arg_2: error.errorMsg,
            }), 'error');
            if (this.isMuteAll) {
              TCIC.SDK.instance.reportEvent('unmute_all_mic', error, -1);
            } else {
              TCIC.SDK.instance.reportEvent('mute_all_mic', error, -1);
            }
          });
      }
    }, 1000, {
      leading: false,
      trailing: true,
    }),

    // 全员归位
    resetVideoPosition: Lodash.throttle(() => {
      TCIC.SDK.instance.notify(Constant.TStateResetVideoPosition, { reset: true }, true);
    }, 1000, {
      leading: true,
      trailing: false,
    }),

    // 全员奖杯
    dispatchTrophy: Lodash.throttle(() => {
      const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
      trophyComponent.getVueInstance().distributeToEveryone();
    }, 1000, {
      leading: true,
      trailing: false,
    }),

    // 全员下台
    // allStageDown: Lodash.throttle(function () {
    //   // this.stageDown();
    // }, 1000, {
    //   leading: true,
    //   trailing: false,
    // }),

    // 下台
    // stageDown() {
    //   const classInfo = TCIC.SDK.instance.getClassInfo();
    //   const param = {
    //     classId: classInfo.classId,
    //     classType: classInfo.classType,
    //     userId: user.userId,
    //     actionType: TCIC.TMemberActionType.Stage_Down,
    //   };
    //   TCIC.SDK.instance.memberAction(param)
    //       .catch((err) => {
    //         window.showToast(err.errorMsg, 'error');
    //       });
    // },


  },
};
</script>
<style lang="less">
.el-switch {
  &.whole-switch {
    height: 24px;

    .el-switch__label {
      color: #fff;

      &.is-active {
        color: #fff;
      }

      span {
        font-size: 16px;
      }
    }

    &.is-checked .el-switch__core:after {
      margin-left: -11px !important;
    }

    .el-switch__core {
      border-width: 2px;
      width: 22px !important;
      height: 14px !important;

      &:after {
        top: -1px;
        left: -1px;
        width: 12px;
        height: 12px;
      }
    }
  }
}

.submenu-popover {
  .whole-ic-prize {
    background-image: url('../assets/ic_all_prize.svg');
  }

  .whole-ic-homing {
    background-image: url('../assets/ic_all_homing.svg');
  }

  .whole-ic-down {
    background-image: url('../assets/ic_all_down.svg');
  }
}

.whole-header-sub-component {

  .ic_nav_whole {
    background-image: url('../assets/ic_nav_whole.png');
  }

  .inactive {
    background: rgb(255, 255, 255)
  }
}

.mute-all-component {
  .header-menu-li {
    cursor: pointer;
    opacity: 0.85;

    &:hover {
      opacity: 1;
    }
  }

}
</style>
