<template>
  <div class="clock-tool-sub-component">
    <!--V1.3.4暂时去掉提示，有文字:content="component.label"-->
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      placement="bottom"
      popper-class="tooltip-no-content"
    >
      <button
        class="header__button button--secondary"
        :class="{active: component.active}"
        @click="open"
      >
        <i class="header__i i--menu icon-clock-tool" />{{ $t('计时器') }}
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'ClockToolSubComponent',
  components: {},
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      componentInstance: null,
    };
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    open() {
      this.componentInstance = this.componentInstance || TCIC.SDK.instance.getComponent('clock-tool-component');
      TCIC.SDK.instance.updateComponent('clock-tool-component', { display: 'block' });
    },
  },
};
</script>
<style lang="less">
.clock-tool-sub-component {
  .icon-clock-tool {
    background-image: url('../assets/ic_tool_clock.svg');
    background-size: 90% !important;
  }
}
</style>
