<template>
  <div :class="['network-quality-sub-component', {'small-screen': isSmallScreen}]">
    <MixedPopper
      ref="popper"
      :type="isSmallScreen ? 'drawer' : 'popover'"
      :visible.sync="visible"

      :popover-placement="isSmallScreen ? 'right-start' : 'top-start'"
      :popover-width="(!isClassStarted && 230) || (!statics && 260) || 355"
      :popover-trigger="isMobile ? 'manual' : 'hover'"
      :popover-offset="offset"
      :popover-visible-arrow="false"
      :popover-value="forceShow"
      :popover-close-delay="closeDelay"
      popover-popper-class="common-tooltip network-tooltip"
      :drawer-show-header-split-line="false"
      drawer-custom-class="common-tooltip network-tooltip network-quality-drawer"

      @popover-show="handleSelfPopoverShow"
    >
      <div slot="title">
        <p
          v-if="showNetworkTip && isSmallScreen"
          class="network-brief"
        >
          <i
            class="icon icon-network-status el-icon--left"
            :class="[GetNetworkTips(status).icon]"
          />
          {{ status | networkBriefFilter }}
        </p>
      </div>
      <div>
        <template v-if="showNetworkTip">
          <p
            v-if="!isSmallScreen"
            class="network-brief"
          >
            <i
              class="icon icon-network-status el-icon--left"
              :class="[GetNetworkTips(status).icon]"
            />
            {{ status | networkBriefFilter }}
          </p>
          <div class="header__tips">
            <div class="network-tips">
              <table>
                <tr>
                  <td class="item">
                    <span>{{ $t('延时') }}</span>
                  </td>
                  <td
                    v-if="status === 'break'"
                    class="value"
                  >
                    <span>-</span>
                  </td>
                  <td
                    v-else
                    class="value"
                  >
                    <span>{{ statics.rtt }}ms</span>
                  </td>
                </tr>
                <tr>
                  <td class="item">
                    <span>{{ $t('丢包率') }}</span>
                  </td>
                  <td
                    v-if="status === 'break'"
                    class="value"
                  >
                    <span>-</span>
                  </td>
                  <td
                    v-else
                    class="value"
                  >
                    <span>↑ {{ statics.upLoss }}% &nbsp;&nbsp;↓ {{ statics.downLoss }}%</span>
                  </td>
                </tr>
                <!-- 0118 需求需要隐藏-->
                <!--
                <tr>
                  <td class="item">
                    <span>{{ $t('状态') }}</span>
                  </td>
                  <td class="value">
                    <span>{{ status | statusDesc }}</span>
                  </td>
                </tr>
                -->
              </table>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="header__tips network-detect-invalid">
            {{ isClassStarted ? $t('网络状态检测中...') : translateTip.netWorkContent }}
          </div>
        </template>
      </div>
      <div
        v-if="showNetworkTip"
        slot="footer"
        class="btn-wrapper mobile-drawer__btn-group"
      >
        <el-button
          id="classid_copy"
          v-clipboard:copy="networkTips"
          v-clipboard:success="onCopySuccess"
          v-clipboard:error="onCopyError"
          size="medium"
          :type="isSmallScreen ? 'default' : 'primary'"
        >
          {{ $t('全部复制') }}
        </el-button>
        <el-button
          size="medium"
          :type="isSmallScreen ? 'default' : 'primary'"
          @click="detectNetwork"
        >
          {{ $t('检测网络') }}
        </el-button>
      </div>
      <div
        v-show="showNetworkQuality"
        slot="reference"
        class="network-box"
        @click="triggerPopover"
      >
        <template
          v-if="status === 'break'"
        >
          <div
            v-for="n in 5"
            :key="n"
            class="network-quality"
            :class="[ n <= signal ? 'active' : 'inactive', 'break']"
            :style="{height: `${n * 20}%` }"
          />
        </template>
        <template
          v-else
        >
          <div
            v-for="n in 5"
            :key="n"
            class="network-quality"
            :class="[ n <= 6 - networkQuality && networkQuality !== 0 ? 'active' : 'inactive' , GetNetworkTips(status).icon]"
            :style="{height: `${n * 20}%` }"
          />
        </template>
        <i
          v-if="status === 'break'"
          class="icon icon-break el-icon--left"
        />
        <el-tooltip
          v-if="showNetworkErrorTooltip"
          effect="dark"
          :content="networkErrorTooltipContent"
          placement="top-start"
          popper-class="network-error-tps"
          :value="showNetworkErrorTooltip"
        >
          <i class="icon icon-transparent el-icon--left" />
        </el-tooltip>
      </div>
    </MixedPopper>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import {
  NetworkMap,
  GetNetworkTips,
} from '@/util/NetworkQualityDefinition';
import { headerEventHub } from '../HeaderEventHub';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';
import Util from '@/util/Util';
import Constant from "@util/Constant";
export default {
  name: 'NetworkQualitySubComponent',
  components: {
    MixedPopper,
  },
  filters: {
    statusDesc(value) {
      return GetNetworkTips(value).label;
    },
    networkBriefFilter(value) {
      return GetNetworkTips(value).tips;
    },
  },
  extends: BaseComponent,
  data() {
    return {
      status: 'unknown',
      statics: null,
      ready: false,
      networkQuality: 0,
      offset: - 20,
      isMobile: TCIC.SDK.instance.isMobile(),
      isElectron: TCIC.SDK.instance.isElectron(),
      closeDelay: TCIC.SDK.instance.isMobile() ? 4000 : 500,
      isClassStarted: false,
      signalAnimationInterval: null,
      signal: 0,
      showNetworkQuality: false,
      roomInfo: {},
      roleInfo: {},
      visible: false,
      forceShow: false,
      showNetworkErrorTooltip: false,
      hasJoinedClass: false,
      messageHandler: null,
      weakMessageHandler: null,
    };
  },
  computed: {
    showNetworkTip() {
      return !!(this.ready && this.statics && this.statics.networkQuality > 0);
    },
    networkTips() {
      if (!this.statics) {
        return [];
      }
      const { rtt, upLoss, downLoss, networkQuality } = this.statics;
      const { classId } = TCIC.SDK.instance.getClassInfo();
      const cid = TCIC.SDK.instance.getCid();
      const uid = TCIC.SDK.instance.getUid();
      return [
        `RoomId: ${cid ? `${cid}(${classId})` : classId}`,
        `UserId: ${uid ? `${uid}(${TCIC.SDK.instance.getUserId()})` : TCIC.SDK.instance.getUserId()}`,
        `${i18next.t('延时')}: ${rtt}ms`,
        `${i18next.t('丢包率')}: ↑ ${upLoss}%   ↓ ${downLoss}%`,
        `${i18next.t('状态')}: ${GetNetworkTips(this.status).label} (quality:${networkQuality})`,
      ].join('\n');
    },
    translateTip() {
      return {
        netWorkContent: i18next.t('开始{{arg_0}}后才能进行网络检测', { arg_0: this.roomInfo.startRoom }),
      };
    },
    networkErrorTooltipContent() {
      if (this.networkQuality < 4) {
        return '';
      }
      if (this.networkQuality === 4) {
        return i18next.t('网络状况较差');
      }
      if (this.networkQuality === 5) {
        return i18next.t('网络状况很差');
      }
        return i18next.t('当前无网络，请检查网络设置');
    },

  },
  watch: {
    status(newVal, oldVal) {
      if (oldVal !== newVal) {
        clearInterval(this.signalAnimationInterval);
        if (newVal === 'break') {
          this.signal = 0;
          // cpu高的时候限制频率
          const interval = Util.getProperIntervalByCPUUsage(800, 3000);
          this.signalAnimationInterval = setInterval(() => {
            if (this.signal === 5) {
              this.signal = 1;
            } else {
              this.signal += 1;
            }
          }, interval);
        }
      }
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, this.staticsHandler);
    this.ready = true;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.isClassStarted = true;
    });

    TCIC.SDK.instance.subscribeState(TCIC.TMainState.Network_Broken, (status) => {
      if (status) {
        this.status = 'break';
      }
    }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

    headerEventHub.$on('header-left-popover-show', this.handlePopoverShow);
    // 确保进入课堂内
    this.$EventBus.$on('do-enter-class', () => {
      this.hasJoinedClass = true;
    });
  },
  beforeDestroy() {
    clearInterval(this.signalAnimationInterval);
    headerEventHub.$off('header-left-popover-show', this.handlePopoverShow);
  },
  methods: {
    showNetworkBrokenMessage(show) {
      if (this.messageHandler == null && show) {
        this.messageHandler =  this.$message({
            showClose: true,
            message: i18next.t('网络已断开，请检查设置'),
            type: 'warning',
            duration: 0,
        });
      }
      if (!show) {
        if (this.messageHandler) {
          this.messageHandler.close();
          this.messageHandler = null;
        }
      }
    },
    showNetworkWeakMessage(show) {
      const h = this.$createElement;
      if (this.weakMessageHandler == null && show) {
        this.weakMessageHandler =  this.$message({
            showClose: true,
            message: h('p', null, [
              i18next.t('当前网络状态不佳。'),
              h('button', {
                on: {
                  click: this.testNetwork
                },
                style: {
                  background: 'none',
                  border: 'none',
                  color: 'blue',
                  cursor: 'pointer',
                  textDecoration: 'underline'
                }
              }, i18next.t('检查网络'))
            ]),
            type: 'warning',
            duration: 0,
        });
      }
      if (!show) {
        if (this.weakMessageHandler) {
          this.weakMessageHandler.close();
          this.weakMessageHandler = null;
        }
      }
    },
    testNetwork() {
      TCIC.SDK.instance.getComponent('network-detector-component').getVueInstance()
              .toggle(true);
      if (this.weakMessageHandler) {
        this.weakMessageHandler.close();
        this.weakMessageHandler = null;
      }
    },
    translateQualityToStatus(networkQuality) {
      this.networkQuality = networkQuality;
      this.status = NetworkMap[networkQuality];
      if (this.networkQuality >= 4 && this.hasJoinedClass && !this.visible) {
        this.showNetworkErrorTooltip = true;
      } else {
        this.showNetworkErrorTooltip = false;
      }
      if (this.networkQuality == 6 && this.hasJoinedClass && !this.visible) {
        this.showNetworkBrokenMessage(true);
      } else {
        this.showNetworkBrokenMessage(false); // showNetwoekBrokenMessage
      }
      if ([4, 5].includes(this.networkQuality) && this.hasJoinedClass && !this.visible) {
        this.showNetworkWeakMessage(true);
      } else {
        this.showNetworkWeakMessage(false);
      }
    },
    async staticsHandler(statics)  {
      // test code
      // statics.networkQuality = 4;
      if (this.isClassStarted) {
        this.statics = statics;
        this.translateQualityToStatus(statics.networkQuality);
      }
    },
    detectNetwork() {
      TCIC.SDK.instance.getComponent('network-detector-component').getVueInstance()
        .toggle(true);
      this.visible = false;
    },
    // 复制成功时的回调函数
    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },

    // 复制失败时的回调函数
    onCopyError() {
      window.showToast(i18next.t('复制失败请重试'));
    },

    onJoinClass() {
      // 不用进trtc房间的不显示网络质量
      const onlyShowNetworkQualityOnStage = (TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isTeacher())
        || (TCIC.SDK.instance.isBigRoom() && !TCIC.SDK.instance.isTeacherOrAssistant());
      if (onlyShowNetworkQualityOnStage) {
        // 上麦后显示网络
        this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (val) => {
          this.showNetworkQuality = val;
        });
      } else {
        this.showNetworkQuality = true;
      }
    },
    GetNetworkTips(status) {
      return GetNetworkTips(status);
    },

    handleSelfPopoverShow() {
      this.showNetworkErrorTooltip = false;
      headerEventHub.$emit('header-left-popover-show', 'NetworkQuality');
    },

    handlePopoverShow(type) {
      if (type !== 'NetworkQuality') {
        this.$refs.popper.$refs.popover.doClose();
      }
    },

    triggerPopover() {
      if (this.isMobile && !this.isSmallScreen) {
        clearTimeout(this.popoverTask);
        this.$refs.popper.$refs.popover.doShow();
        this.popoverTask = setTimeout(() => {
          this.$refs.popper.$refs.popover.doClose();
        }, 3000);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.network-quality-sub-component {
  -webkit-app-region: no-drag;
  display: block;
  height: 16px;

  .network-box {
    height: 16px;
    line-height: 16px;
    cursor: pointer;
    margin-left: 18px;
    position: relative;
    width: 20px;

    span {
      vertical-align: top;
      color: rgba(250, 100, 0, 1)
    }

    .icon-break {
      position: absolute;
      width: 12px;
      height: 12px;
      background-image: url(../assets/icon-break.svg);
      background-repeat: no-repeat;
      background-size: contain;
      bottom: -2px;
      left: 8px;
    }

    .icon-transparent {
      position: absolute;
      width: 12px;
      height: 12px;
      bottom: -2px;
      left: 8px;
    }

  }

  .network-quality {
    width: 2px;
    margin-right: 2px;
    display: inline-block;
    vertical-align: bottom;
  }

  .active {
    background: rgb(39, 190, 76);

    &.poor {
      background: rgb(250, 100, 0);
    }

    &.good {
      background: rgb(39, 190, 76);
    }

    &.break {
      background: #fff;
    }
  }

  .inactive {
    background: rgb(138, 144, 153)
  }

}

.network-tooltip {
  background: #1C2131;
  border: none;
  border-radius: 8px;
  padding: 16px 20px;
  &:not(.mobile) {
    top: 54px !important;
  }
  &.mobile{
    top: calc(env(safe-area-inset-top) + 45px) !important;
  }


  .network-brief {
    font-size: 18px;
    color: #fff;
    padding-bottom: 20px;
    vertical-align: sub;
  }

  .icon-network-status {
    width: 24px;
    height: 24px;
    vertical-align: sub;

    &.poor {
      background-image: url(../assets/icon-network-status-poor.svg);
      background-repeat: no-repeat;
      background-size: contain;
    }

    &.good {
      background-image: url(../assets/icon-network-status-good.svg);
      background-repeat: no-repeat;
      background-size: contain;
    }

    &.break {
      background-image: url(../assets/icon-network-status-break.svg);
      background-repeat: no-repeat;
      background-size: contain;
    }
  }


  .network-tips {
    text-align: left;
    font-size: 14px;
    user-select: text;
    -webkit-user-select: text;
    color: #fff;
    padding-bottom: 10px;

    table {
      width: 100%;

      tr {
        height: 32px;
        line-height: 32px;

        td {
          color: #fff;

          &:first-child {

            color: #8A9099;
          }
        }
      }

      .value {
        text-align: right;
      }
    }
  }

  .btn-wrapper {
    text-align: center;
  }

  &.el-popper[x-placement^=bottom] {
    .popper__arrow:after {
      border-bottom-color: rgba(0, 0, 0, .7) !important
    }
  }
}

.network-detect-invalid {
  word-break: normal;
}

.small-screen {
  .network-box {
    margin-left: 5px;
    height: 12px;
    line-height: 13px;
  }
  .network-quality {
    margin-right: 1.5px;
  }
}

.network-quality-drawer {

  .network-tips, .network-detect-invalid {
    padding: 0 16px;
  }

  .network-detect-invalid {
    font-size: 16px;
    line-height: 24px;
    color: #fff;
    margin: 16px 0;
  }
}
</style>
