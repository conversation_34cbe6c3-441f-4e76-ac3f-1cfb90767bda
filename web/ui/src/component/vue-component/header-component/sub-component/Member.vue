<template>
  <div class="member-sub-component">
    <template v-if="!isSmallScreen">
      <el-tooltip
        :manual="interactiveIsTouchEvent()"
        :disabled="isMobile || localComponent.active"
        class="item"
        :content="roomInfo.memberList"
        placement="bottom"
      >
        <el-popover
          ref="popover"
          v-model="localComponent.active"
          placement="bottom-end"
          popper-class="header-component-popover content-fixed popover__member inComponent"
          trigger="manual"
          @show="localComponent.active = true"
          @hide="localComponent.active = false"
        >
          <MemberList
            v-if="!isCoTeachingClass"
            @hide="onHide"
          />
          <button
            slot="reference"
            class="header__button button--secondary"
            :class="{active: localComponent.active}"
            style="position: relative;"
            @click="onClick"
          >
            <div class="tool-item-wrap">
              <i :class="`header__i i--menu icon-${localComponent.name}`" />
              <span class="header__btn-text">{{ roomInfo.memberList }}</span>
            </div>
          </button>
        </el-popover>
      </el-tooltip>
    </template>
    <template v-else>
      <MixedPopper
        type="drawer"
        :title="roomInfo.memberList"
        :visible.sync="localComponent.active"

        drawer-btt-height="calc(100% - 45px)"
      >
        <FloatMemberList
          @hide="onHide"
        />
      </MixedPopper>
      <button
        class="side-bar-btn header__right-button"
        @click="onClick"
      >
        <div class="tool-item-wrap">
          <el-badge
            :value="localComponent.badge"
            :max="99"
            class="badge"
            :hidden="localComponent.badge === 0"
          >
            <i
              :class="[`icon-${localComponent.name}`, {'mobile': isMobile}]"
              :style="iconStyle"
            />
          </el-badge>
          <span
            v-if="showText"
            class="header__btn-text"
          >{{ roomInfo.memberList }}</span>
        </div>
      </button>
    </template>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import MemberList from '../../member-list-component/MemberList.vue';
import FloatMemberList from '../../member-list-component/FloatMemberList.vue';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';

export default {
  name: 'MemberSubComponent',
  components: {
    MemberList,
    FloatMemberList,
    MixedPopper,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
    showText: {
      type: Boolean,
      default: true,
    },
    iconStyle: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isCoTeachingClass: false,
      coteachingListInstance: null,
      localComponent: this.component,
      roomInfo: {},
    };
  },
  watch: {
    'localComponent.active'(val) {
      TCIC.SDK.instance.setState(Constant.TStateHeaderMemberActive, val);
    },
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getNameConfig().roomInfo;
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
    TCIC.SDK.instance.registerState(Constant.TStateHeaderMemberActive, '菜单栏的成员列表是否处于激活态', false);
  },
  beforeDestroy() {
    this.unbindEvent();
  },
  methods: {
    onJoinClass() {
      // 绑定事件
      this.bindEvent();
      this.isCoTeachingClass = TCIC.SDK.instance.isCoTeachingClass();
      // 注册popover组件
      // this.addPopoverComponent(this.component);
    },

    bindEvent() {
      // 举手人数
      this.addLifecycleTCICStateListener(TCIC.TMainState.HandUp_Count, this.handUpCountChangeHandler);
    },

    unbindEvent() {
    },

    handUpCountChangeHandler(count) {
      this.localComponent.badge = count;
    },
    async onClick() {
      if (this.isCoTeachingClass) {
        if (!this.coteachingListInstance) {
          const headerHeight = 45;
          const dom = await TCIC.SDK.instance.loadComponent('ct-member-list-component', {
            top: `${headerHeight}px`,
            left: '0',
            width: '100%',
            height: `calc(100% - ${headerHeight}px)`,
            display: 'block',
            zIndex: 2000,
          });
          this.coteachingListInstance = dom.getVueInstance();
        }
        if (!this.coteachingListInstance.visible) {
          this.coteachingListInstance.show();
          this.localComponent.active = true;
        } else {
          this.coteachingListInstance.close();
          this.localComponent.active = false;
        }
      } else {
        this.localComponent.active = !this.localComponent.active;
      }
    },
    onHide() {
      this.localComponent.active = false;
    },
  },
};
</script>
<style lang="less">
.member-sub-component {
  .icon-member {
    background-image: url('../assets/new-icons/ic_attendees.svg')
  }

  .active {
    background: #2971ff;
  }

  .inactive {
    background: rgb(255, 255, 255)
  }
  .side-bar-btn {
    background: transparent;

    .icon-member {
      width: 40px;
      height: 40px;
      background-size: 50%;
      &.mobile {
        background-size: 60%;
      }
    }
  }
  .badge {
    .el-badge__content {
      background-color: #FA6400;
      border: 0;
    }
  }
}
</style>
