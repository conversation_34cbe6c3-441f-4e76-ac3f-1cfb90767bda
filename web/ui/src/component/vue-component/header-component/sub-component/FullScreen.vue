<template>
  <div
    class="fullscreen-sub-component"
    @click="requestFullscreen"
  >
    <i class="header__i icon-fullscreen" />
  </div>
</template>

<script>
import screenfull from 'screenfull';
export default {
  deta() {
    return {

    };
  },
  methods: {
    requestFullscreen() {
      // document.documentElement.requestFullscreen();
      if (!screenfull.isEnabled) {
        window.showToast('当前浏览器不支持全屏');
        return;
      }
      screenfull.toggle();
    },
  },
};
</script>

<style lang="less">
.fullscreen-sub-component {
  display: flex;
  align-items: center;
  .header__i.icon-fullscreen {
    background-image: url('../assets/ic_fullscreen.svg');
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-size: 60%;
    background-position: center;
  }
}
</style>
