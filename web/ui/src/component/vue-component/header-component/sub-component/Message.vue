<template>
  <div class="im-sub-component">
    <template v-if="!isSmallScreen">
      <el-tooltip
        class="item"
        :disabled="(isMobile && !btnDisabled) || localComponent.active"
        :content="chatDisabled ? chatDisabledTooltip : toolTips"
        placement="bottom"
        :manual="isPad || showCustomToolTips || interactiveIsTouchEvent()"
        :value="showCustomToolTips"
      >
        <el-popover
          ref="popover"
          :value="localComponent.active"
          placement="bottom"
          :width="localComponent.width"
          popper-class="header-component-popover popover__message inComponent"
          trigger="manual"
          @show="onPopOverShow"
          @hide="onPopOverHide"
        >
          <button
            slot="reference"
            :ref="localComponent.name"
            :disabled="btnDisabled"
            class="header__button button--secondary header__right-button"
            :class="{active: localComponent.active}"
            @click="showIM"
          >
            <div class="tool-item-wrap">
              <el-badge
                :value="localComponent.badge"
                :max="99"
                class="badge"
                :hidden="localComponent.badge === 0 || isHiddenMessageCount"
              >
                <i :class="`header__i i--menu icon-${localComponent.name}`" />
              </el-badge>
              <span class="header__btn-text">{{ $t('消息') }}</span>
            </div>
          </button>
        </el-popover>
      </el-tooltip>
    </template>

    <template v-else>
      <button
        :disabled="btnDisabled"
        class="header__button button--secondary header__right-button"
        @click="loadIM"
      >
        <div class="tool-item-wrap">
          <el-badge
            :value="localComponent.badge"
            :max="99"
            class="badge"
            :hidden="localComponent.badge === 0 || isHiddenMessageCount"
          >
            <i
              :class="['icon-message', {'mobile': isMobile, 'is-portrait': isPortrait}]"
              :style="iconStyle"
            />
          </el-badge>
          <span
            v-if="showText"
            class="header__btn-text"
          >{{ $t('消息') }}</span>
        </div>
      </button>
    </template>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import { headerEventHub } from '../HeaderEventHub';

export default {
  name: 'ImSubComponent',
  extends: BaseComponent,
  props: {
    showText: {
      type: Boolean,
      default: true,
    },
    iconStyle: {
      type: Object,
      default: () => ({}),
    },
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      floatIMComponent: null,
      toolTips: i18next.t('消息'),
      showCustomToolTips: false,
      chatDisabled: null,
      chatDisabledTooltip: null,
      isPad: TCIC.SDK.instance.isPad(),
      isMobile: TCIC.SDK.instance.isMobile(),
      isHiddenMessageCount: true, // 是否隐藏消息计数
      localComponent: this.component,
      isPortrait: false,
      isIMFloat: false,
    };
  },
  computed: {
    btnDisabled() {
      return this.chatDisabled;
    },
  },
  mounted() {
    this.addLifecycleTCICStateListener(Constant.TStateChatBoxTips, (toolTips) => {
      this.showCustomToolTips = !!toolTips;
      if (this.showCustomToolTips) {
        this.toolTips = toolTips;
      } else {
        setTimeout(() => {
          this.toolTips = i18next.t('消息');
        }, 500);  // 延迟修改提示文本，因为tips消失需要一定时长
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
    headerEventHub.$on('float-im-hide', this.onHide);
    this.$EventBus.$on('im-folat', (val) => {
      this.isIMFloat = val;
      // 同时更新popover的状态
      this.localComponent.active = !val;
    });
  },
  beforeDestroy() {
    headerEventHub.$off('float-im-hide', this.onHide);
    this.$EventBus.$off('im-folat');
  },
  methods: {

    onJoinClass() {
      // 绑定事件
      this.bindEvent();
      this.floatIMComponent = TCIC.SDK.instance.loadComponent('float-im-component', { zIndex: 350 });
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const lastActive = window.localStorage.getItem(`${classInfo.classId}_${TCIC.SDK.instance.getUserId()}_quick_im_active`);
      if (lastActive === null || lastActive === undefined) {
        this.isHiddenMessageCount = true;
      } else {
        this.isHiddenMessageCount = lastActive;
      }
    },

    /**
     * 绑定事件
     */
    bindEvent() {
      // 消息未读
      this.addLifecycleTCICStateListener(TCIC.TMainState.Message_Unread_Count, this.messageUnreadCountChangeHandler);
      this.addLifecycleTCICStateListener(Constant.TStateDisableChat, (value) => {
        this.chatDisabled = value;
      });
      this.addLifecycleTCICStateListener(Constant.TStateDisableChatTips, (value) => {
        this.chatDisabledTooltip = value;
      });
      this.addLifecycleTCICStateListener(Constant.TStateChatTipsEnable, (show) => {
        this.isHiddenMessageCount = show;
      });
    },

    /**
     * 未读
     */
    messageUnreadCountChangeHandler(count) {
      console.debug('messageUnreadCountChangeHandler => ', count);
      this.localComponent.badge = count;
    },

    onPopOverShow() {
      this.isIMFloat = false;
      this.localComponent.active = true;
      const headerHeight = 64;
      const layout = {
        top: `${headerHeight}px`,
        left: 'unset',
        width: '40%',
        height: 'calc(100vh - 64px)',
        display: 'block',
        style: `right: 0;
                bottom: 0;
                min-width: 310px;
                max-width: 503px;
        `,
        zIndex: 350,
        transform: 'none',
      };
      TCIC.SDK.instance.updateComponent('float-im-component', layout).then(() => {
        TCIC.SDK.instance.setState(Constant.TStateShowChatBox, true);
      });
    },

    onPopOverHide() {
      // floatIM在悬浮状态下，popover触发hide不隐藏floatIM组件
      if (this.isIMFloat) {
        return;
      }
      // TCIC.SDK.instance.setState(Constant.TStateShowChatBox, false);
      this.localComponent.active = false;
      TCIC.SDK.instance.updateComponent('float-im-component', {
        display: 'none',
        transform: 'none',
      }).then(() => {
        TCIC.SDK.instance.setState(Constant.TStateShowChatBox, false);
      });
    },

    // 大屏幕展示聊天框
    showIM() {
      /* TODO 冲突，待验证
      let showState = TCIC.SDK.instance.getState(Constant.TStateShowChatBox);
      // 展示或收起
      showState = !showState;
      // if (!showState) this.onPopOverHide(); // 如果是收起，需要收起聊天框
      const layout = {
        top: '45px',
        left: 'unset',
        width: '340px',
        height: 'calc(100vh - 100px)',
        display: showState ? 'block' : 'none',
        style: `right: 100px;
                  bottom: 30px;
                  `,
        zIndex: 350,
      };
      TCIC.SDK.instance.updateComponent('float-im-component', layout).then(() => {
        TCIC.SDK.instance.setState(Constant.TStateShowChatBox, showState);
      });
      this.visible = !this.visible;
      */
      this.$EventBus.$emit('float-im-is-float', false);
      this.localComponent.active = !this.localComponent.active;
      TCIC.SDK.instance.setState(Constant.TStateShowChatBox, this.localComponent.active);
    },
    // 小屏幕展示聊天框
    loadIM() {
      let showState = TCIC.SDK.instance.getState(Constant.TStateShowChatBox);
      // 展示或收起
      showState = !showState;

      const layout = {
        top: this.isPortrait ? 45 : 0,
        left: 'unset',
        width: this.isPortrait ? '100%' : '50%',
        height: this.isPortrait ? 'calc(100% - 45px)' : '100%',
        display: showState ? 'block' : 'none',
        style: 'right: 0; bottom: 0;',
        zIndex: 1501,
      };
      TCIC.SDK.instance.updateComponent('float-im-component', layout).then(() => {
        TCIC.SDK.instance.setState(Constant.TStateShowChatBox, showState);
      });
    },
    onHide() {
      this.localComponent.active = false;
      // TCIC.SDK.instance.setState(Constant.TStateShowChatBox, false);
    },
  },
};
</script>
<style lang="less">
.im-sub-component {
  .icon-message,
  .icon-im {
    background-image: url('../assets/new-icons/ic_chat.svg');
  }

  .side-bar-btn {
    background: transparent;

    .icon-message {
      width: 40px;
      height: 40px;
    }
  }

  .header__right-button {
    .icon-message {
      width: 40px;
      height: 40px;
      &.mobile {
        background-repeat: no-repeat;
        background-position: center;
        background-size: 60%;
      }
    }
  }
}

.mobile-ui-layout {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: calc(80vh);
  z-index: 1000;
}

.popper__arrow {
  display: none !important;
}

.el-popper[x-placement^=bottom] {
  margin-top: 18px !important;
}

.el-popover.header-component-popover {
  background: #1C2131;

  &.popover__message {
    padding: 0;
    &.mobile {
      top: 0 !important;
    }
    .float-im-component {
      padding: 0px;
      height: calc(100vh - 100px);
      max-height: 791px;
    }
  }
}
</style>
