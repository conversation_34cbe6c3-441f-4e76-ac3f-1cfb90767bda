<template>
  <div class="quiz-sub-component">
    <!--V1.3.4暂时去掉提示，有文字:content="component.label"-->
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      placement="bottom"
      popper-class="tooltip-no-content"
    >
      <button
        class="header__button button--secondary"
        :class="{active: component.active}"
        @click="open"
      >
        <i class="header__i i--menu icon-quiz" />{{ $t('答题器') }}
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'QuizSubComponent',
  components: {},
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      componentInstance: null,
    };
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    // 随堂测
    open() {
      const quizComponent = TCIC.SDK.instance.getComponent('quiz-component');
      quizComponent.getVueInstance().render();
    },
  },
};
</script>
<style lang="less">
.quiz-sub-component {
  .icon-quiz {
    background-image: url('../assets/ic_tool_answer.svg');
  }
}
</style>
