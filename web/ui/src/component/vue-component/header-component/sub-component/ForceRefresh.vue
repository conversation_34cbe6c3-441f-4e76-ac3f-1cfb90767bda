<template>
  <el-tooltip
    effect="dark"
    :content="$t('强制刷新')"
    :disabled="isMobile"
    placement="bottom"
  >
    <button :class="`icon-refresh-button ${isSmallScreen ? 'mobile' : ''}`">
      <i
        :class="`icon-refresh ${isSmallScreen ? 'mobile' : ''}`"
        @click="refresh()"
      />
    </button>
  </el-tooltip>
</template>

<script>
import BaseComponent from '@core/BaseComponent';

export default {
  extends: BaseComponent,
  data() {
    return {};
  },
  methods: {
    refresh() {
      window.customReload();
    },
  },
};
</script>

<style lang="less">
 .icon-refresh-button {
    -webkit-app-region: no-drag;
 }

  .icon-refresh-button.mobile {
    height: 18px;
  }

  .icon-refresh {
    display: inline-block;
    width: 21px;
    height: 21px;
    margin: 0 8px 0 12px;
    background-image: url('../assets/ic_force_refresh.svg');
    background-size: contain;
    &.mobile {
      width: 16px;
      height: 16px;
      margin: 0 4px 0 8px;
    }
  }

</style>
