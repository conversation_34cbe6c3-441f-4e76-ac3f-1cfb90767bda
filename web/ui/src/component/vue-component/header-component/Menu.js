/*
*   菜单管理
 * @property name         备进课堂事件
 * @property enable       备进课堂事件
 * @property label        备进课堂事件
 * @property component    备进课堂事件
 * @property role         角色
 * @property type         备进课堂事件
 * @property hiddenWhenMoreBtnShow  是否在不可见收入更多按钮
 * @property popover      popover实例
 * @property active       当前是否被点击
 * @property width        宽度
 * @property height       默认根据内容自适应，可以配置默认高度
 * @property badge        数字上标
 * @property placement    popover出现的位置,参考 https://element.eleme.io/#/zh-CN/component/popover  【placement】参数
 */
import i18next from 'i18next';
import screenCaptureIcon from './assets/new-icons/ic_screen_share.svg';

export default () => [
  {
    name: 'speaker',
    enable: TCIC.SDK.instance.isElectron() || TCIC.SDK.instance.isWeb(),
    component: 'header-mic-switch-component',
    label: i18next.t('麦克风'),
    role: {
      teacher: {
        classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      assistant: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      student: {
        classType: ['interactive', 'oneonone', 'unitedrtc'],
      },
    },
    isSub: true,
    hiddenWhenMoreBtnShow: false,
    popover: null,
    active: false,
    width: 820,
    badge: 0,
    placement: 'bottom',
  }, {
    name: 'camera',
    enable: TCIC.SDK.instance.isElectron() || TCIC.SDK.instance.isWeb(),
    component: 'header-camera-switch-component',
    label: i18next.t('摄像头'),
    role: {
      teacher: {
        classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      assistant: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      student: {
        classType: ['interactive', 'oneonone', 'unitedrtc'],
      },
    },
    isSub: true,
    hiddenWhenMoreBtnShow: false,
    popover: null,
    active: false,
    width: 820,
    badge: 0,
    placement: 'bottom',
  },
  {
  name: 'invite',
  enable: false,
  component: 'invite-dialog-component',
  label: i18next.t('邀请学员'),
  role: {
    teacher: {
      classType: ['interactive', 'live'],
    },
    assistant: {
      classType: ['interactive'],
    },
    student: {
      classType: ['interactive', 'live', 'coteaching'],
    },
  },
  isSub: true,
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
}, {
  name: 'externalCourseware',
  // 默认隐藏，可以通过自定义 UI `TCICCustomUI.menus.setVisible('externalCourseware', true);` 开启
  enable: false,
  label: '在线课件',
  // eslint-disable-next-line max-len
  icon: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSI+PHBhdGggc3Ryb2tlPSIjRDFEOUVDIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgZD0iTTQgMjBWNWMwLTEuNjU3IDEuNDMzLTMgMy4yLTNIMjB2MTZINy4yYy0yLjUxOSAwLTMuMi4zNDItMy4yIDJaIi8+PHBhdGggc3Ryb2tlPSIjRDFEOUVDIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNNiAyMmgxNHYtNEg2YTIgMiAwIDEgMCAwIDRaIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiLz48cGF0aCBzdHJva2U9IiNBNUZFMzMiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS4yIiBkPSJNNy41IDkuMzIzQTIgMiAwIDAgMSA5IDZoNGEyIDIgMCAxIDEgMCA0aC0xLjYxNyIvPjxwYXRoIHN0cm9rZT0iIzAwNkNGRiIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSIxLjIiIGQ9Ik0xNi41IDguNjc3QTIgMiAwIDAgMSAxNSAxMmgtNGEyIDIgMCAxIDEgMC00aDEuNjE3Ii8+PC9zdmc+',
  role: {
    teacher: {
      classType: ['unitedrtc'],
    },
    student: {
      classType: ['unitedrtc'],
    },
    supervisor: {
      classType: ['unitedrtc'],
    },
    assistant: {
      classType: ['unitedrtc'],
    },
  },
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
  action: () => {
    TCIC.SDK.instance.getComponent('external-courseware-component').getVueInstance()
      .toggle();
  },
  badge: 0,
}, {
  name: 'rtmp',
  enable: TCIC.SDK.instance.isRtmpMode(),
  label: i18next.t('推流'),
  icon: screenCaptureIcon,
  role: {
    teacher: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    student: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
    assistant: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
  },
  isSub: false,
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
  width: 820,
  badge: 0,
  placement: 'bottom',
  action: () => {
    TCIC.SDK.instance.getComponent('rtmp-component').getVueInstance()
      .toggle();
  },
}, {
  name: 'screenCapture',
  enable: TCIC.SDK.instance.isElectron() || TCIC.SDK.instance.isWeb() || TCIC.SDK.instance.isPad() || TCIC.SDK.instance.isMobile(),
  component: 'share-sources-component',
  label: i18next.t('屏幕共享'),
  role: {
    teacher: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    assistant: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    student: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
  },
  isSub: true,
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
  width: 820,
  badge: 0,
  placement: 'bottom',
}, {
  name: 'member',
  enable: true,
  component: 'member-list-component',
  label: TCIC.SDK.instance.getNameConfig().roomInfo.memberList,
  role: {
    teacher: {
      classType: ['interactive', 'coteaching', 'oneonone', 'unitedrtc'],
    },
    assistant: {
      classType: ['interactive', 'coteaching', 'oneonone', 'unitedrtc'],
    },
    supervisor: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
  },
  isSub: true,
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
  width: 702,
  height: 825,
  badge: 0,
}, {
  name: 'document',
  label: TCIC.SDK.instance.getNameConfig().roomInfo.courseware,
  enable: true,
  component: 'document-component', // 可为空
  isSub: true,
  role: {
    teacher: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    assistant: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    // 巡课没有课件权限
    // supervisor: {
    //   classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    // },
  },
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false, // 当前是否激活态
  width: 588,
  badge: 0,
}, {
  name: 'message',
  label: i18next.t('消息'),
  enable: true,
  isSub: true,
  role: {
    teacher: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
    assistant: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
    student: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
    supervisor: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
  },
  hiddenWhenMoreBtnShow: false,
  active: false,
  width: 400,
  height: 791,
  badge: 0,
}, {
  name: 'notice',
  enable: true,
  component: 'notice-sub-component',
  label: i18next.t('公告'),
  role: {
    teacher: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    assistant: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    student: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    supervisor: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
  },
  isSub: true,
  hiddenWhenMoreBtnShow: true,
  popover: null,
  active: false,
}, {
  name: 'tool-box',
  label: i18next.t('工具箱'),
  enable: true,
  isSub: true,
  role: {
    teacher: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    assistant: {
      classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
  },
  width: 100,
  hiddenWhenMoreBtnShow: false,
  popover: null,
  active: false,
  children: [{
    name: 'clock-tool',
    label: i18next.t('计时器'),
    enable: true,
    isSub: true,
    role: {
      teacher: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      assistant: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
    },
    hiddenWhenMoreBtnShow: false,
    popover: null,
    active: false,
    badge: 0,
  }, {
    name: 'timer-tool',
    label: i18next.t('定时器'),
    enable: true,
    isSub: true,
    role: {
      teacher: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
      assistant: {
        classType: ['interactive', 'live', 'oneonone', 'unitedrtc', 'unitedlive'],
      },
    },
    hiddenWhenMoreBtnShow: false,
    popover: null,
    active: false,
    badge: 0,
  }, {
    name: 'quiz',
    label: i18next.t('随堂测'),
    enable: true,
    isSub: true,
    role: ['teacher'],
    hiddenWhenMoreBtnShow: true,
  },
  // {
  //   name: 'random-choose-tool',
  //   label: i18next.t('随机选人'),
  //   enable: true,
  //   isSub: true,
  //   role: {
  //     teacher: {
  //       classType: ['interactive', 'oneonone', 'unitedrtc'],
  //     },
  //     assistant: {
  //       classType: ['interactive', 'oneonone', 'unitedrtc'],
  //     },
  //   },
  //   hiddenWhenMoreBtnShow: false,
  //   popover: null,
  //   active: false,
  //   badge: 0,
  // },
  // ,
  // {
  //   name: 'seize-answer',
  //   label: i18next.t('抢答器'),
  //   enable: true,
  //   isSub: true,
  //   role: {
  //     teacher: {
  //       classType: ['interactive', 'oneonone', 'unitedrtc'],
  //     },
  //     assistant: {
  //       classType: ['interactive', 'oneonone', 'unitedrtc'],
  //     },
  //   },
  //   hiddenWhenMoreBtnShow: true,
  //   popover: null,
  //   active: false,
  //   badge: 0,
  // }
  {
    name: 'sub-camera-preview',
    label: i18next.t('辅助摄像头'),
    enable: true,
    isSub: true,
    role: ['teacher'],
    hiddenWhenMoreBtnShow: true,
  }],
}, {
  name: 'allMemberController',
  label: i18next.t('全员操作'),
  enable: true,
  isSub: true,
  role: {
    teacher: {
      classType: ['interactive', 'coteaching', 'oneonone', 'unitedrtc'],
    },
    assistant: {
      classType: ['interactive', 'coteaching', 'oneonone', 'unitedrtc'],
    },
    supervisor: {
      classType: ['interactive', 'oneonone', 'unitedrtc'],
    },
  },
  hiddenWhenMoreBtnShow: true,
}, {
  name: 'layout',
  label: i18next.t('布局'),
  enable: true,
  isSub: true,
  role: {
    teacher: {
      classType: ['interactive', 'coteaching', 'unitedrtc'],
    },
    assistant: {
      classType: ['interactive', 'coteaching', 'unitedrtc'],
    },
    student: {
      classType: ['coteaching'],
    },
  },
  width: 100,
  hiddenWhenMoreBtnShow: true,
  popover: null,
  active: false,
}, {
  name: 'setting',
  component: 'setting-component',
  label: i18next.t('设置'),
  enable: true,
  isSub: true,
  active: false,
  popover: null,
  role: {
    teacher: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    assistant: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    student: {
      classType: ['interactive', 'live', 'coteaching', 'oneonone', 'unitedrtc', 'unitedlive'],
    },
    supervisor: {
      classType: ['oneonone', 'unitedrtc', 'unitedlive'], // 旧版本没处理，新版本才处理了巡课只有常规tab
    },
  },
  hiddenWhenMoreBtnShow: true,
  closable: false,
},
];

