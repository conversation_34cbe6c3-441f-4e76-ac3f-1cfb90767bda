<template>
  <div
    ref="headerRef"
    class="header-mobile-component"
    :class="{fullscreen: fullScreen || isUnitedLiveClass}"
  >
    <div
      v-show="topBarIsVisible"
      class="top-bar"
    >
      <div :class="['header__left', {'abs': showAbsolute}]">
        <div class="header__logout">
          <span @click="logout" />
        </div>
        <!-- <div v-loading="!logo"
             element-loading-background="transparent"
             :style="logo ? `background-image:url(${logo})` : ''" class="header__logo"/> -->
        <!-- 课堂信息 -->
        <div class="header-title-mobile">
          <div
            class="header-title-classinfo"
            :style="{'display': 'flex'}"
          >
            <ClassInfo />
          </div>
          <!-- 当前上课时长 -->
          <ClassDuration />
        </div>
        <MarqueeBanner
          v-if="isShowFeedbackQr && !isPortrait"
          :text="$t('该平台专注提供线上音视频互动服务。为保护你的权益与安全，请警惕网络诈骗。')"
        />
      </div>
      <div
        v-show="!isAIRoom"
        ref="headerRightRef"
        :class="['header__right', isPortrait && 'is-portrait']"
      >
        <Fullscreen v-if="isMobile && isWeb && showFullscreenIcon" />
        <template v-for="item in menuLayout.primary">
          <Component
            :is="item.name"
            v-if="item.isSub"
            :key="item.name"
            :ref="item.name"
            :component="item"
          />
          <el-tooltip
            v-else
            :key="item.name+item.label"
            class="item"
            :disabled="!item.label"
            :content="$t(item.label)"
            placement="bottom"
          >
            <button
              :ref="item.name"
              class="header__button button--secondary header__button-custom"
              :class="[{ active: item.active }, item.class || '']"
              @click="item.action"
            >
              <el-badge
                :value="item.badge"
                :max="99"
                class="badge"
                :hidden="item.badge === 0 ? true : false"
              >
                <i
                  :class="['header__i i--menu', { 'is-mobile': isMobile, 'small-screen': isSmallScreen }]"
                  :style="{ 'background-image': `url(${item.icon})` }"
                />
              </el-badge>
            </button>
          </el-tooltip>
        </template>
        <el-tooltip
          v-if="menuLayout.secondary.length > 0"
          v-model="tooltipVisible"
          placement="bottom"
          :visible-arrow="false"
          :manual="true"
          popper-class="header__right__more"
        >
          <div
            slot="content"
            class="sidebar-btn-list"
          >
            <template v-for="item in menuLayout.secondary">
              <Component
                :is="item.name"
                v-if="item.isSub"
                :key="item.name"
                :ref="item.name"
                :component="item"
              />
              <el-tooltip
                v-else
                :key="item.name+item.label"
                class="item"
                :disabled="!item.label"
                :content="$t(item.label)"
                placement="bottom"
              >
                <button
                  :ref="item.name"
                  class="header__button button--secondary"
                  :class="[{ active: item.active }, item.class || '']"
                  @click="item.action"
                >
                  <el-badge
                    :value="item.badge"
                    :max="99"
                    class="badge"
                    :hidden="item.badge === 0 ? true : false"
                  >
                    <i
                      :class="`header__i i--menu`"
                      :style="{ 'background-image': `url(${item.icon})` }"
                    />
                  </el-badge>
                </button>
              </el-tooltip>
            </template>
          </div>
          <div
            class="side-bar-btn more-btn"
            @click="tooltipVisible = !tooltipVisible"
          >
            <i class="icon-more" />
          </div>
        </el-tooltip>
        <el-button
          v-if="canStartClass"
          :disabled="isStartClassButtonDisabled"
          class="header__button--start"
          :type="'primary'"
          @click="startClass"
        >
          {{ roomInfo.startRoom }}
        </el-button>
      </div>
    </div>
    <MarqueeBanner
      v-if="isShowFeedbackQr && isPortrait"
      class="header-mobile-component-demo-limitation"
      :text="$t('该平台专注提供线上音视频互动服务。为保护你的权益与安全，请警惕网络诈骗。')"
    />
  </div>
</template>

<script>
import Lodash from 'lodash';
import Util from '@util/Util';
import ClassInfo from '@/component/vue-component/header-component/sub-component/ClassInfo';
import ClassDuration from './sub-component/ClassDuration';
import Setting from './sub-component/Setting';
import HeaderBase from './HeaderBase';
import Constant from '@util/Constant';
import Message from './sub-component/Message';
import Fullscreen from './sub-component/FullScreen';
import Member from './sub-component/Member.vue';
import Notice from './sub-component/Notice';
import Document from './sub-component/Document';
import Invite from './sub-component/Invite';
import ScreenCapture from './sub-component/ScreenCapture';
import MarqueeBanner from '@/component/vue-component/header-component/sub-component/MarqueeBanner.vue';

export default {
  name: 'HeaderComponent',
  components: {
    MarqueeBanner,
    ClassInfo,
    Setting,
    ClassDuration,
    Message,
    Member,
    Notice,
    Document,
    Invite,
    Fullscreen,
    ScreenCapture,
  },
  extends: HeaderBase,
  data() {
    return {
      className: '', // 课堂名称
      classId: '', // 课堂ID
      classTime: '00:00:00',
      showMessage: undefined, // 是否显示消息按钮
      fullScreen: false,
      showFullscreenIcon: true,
      menuHideDelayTask: null,
      classType: null,
      topBarIsVisible: true, // header组件显示与否
      sidebarIsVisible: true, // 组件显示与否
      tooltipVisible: false,  // 更多下来出现设置
      hasBoardPermission: false, // 是否有白板操作权限
      showAbsolute: false, // 是否显示完整菜单
      isUnitedLiveClass: false, // 是否CDN课
      isStartClassButtonDisabled: false,
      roomInfo: {},
      isPortrait: false,
      isMobile: false,
      classJoined: false, // 是否已经加入课堂
      classLayout: null, // 课堂布局类型
      isVideoDoc: false, // 是否含白板视频
      isMobileNative: false,
      isMiniProgram: false,
      isWeb: false,
      menuWrapperWidth: 0,
      menuItemWidth: 48,
      isAIRoom: false,
      isShowFeedbackQr: false,
    };
  },
  computed: {
    isVideoPortraitClass() {
      // 移动端大班课需要展示花名册，所以竖屏课需要排除大班课
      return TCIC.SDK.instance.isPortraitClass() && !TCIC.SDK.instance.isBigRoom();
    },
    showMoreBtn() {
      return this.menu.find(item => (item.name === 'invite') && item.enable === true);
    },
    showHandUp() {
      return !this.isTeacher              // 非老师才显示
          && this.isClassStarted;       // 上课后才显示
    },
    menuLayout() {
      if (!this.classJoined || this.menuWrapperWidth === 0) {
        return {
          primary: [],
          secondary: [],
        };
      }

      // 根据角色过滤菜单
      const filteredMenu = this.menu.filter((item) => {
        if (!item.enable) return false;
        const role = (this.isTeacher && 'teacher') || (this.isSupervisor && 'supervisor') || (this.isAssistant && 'assistant') || 'student';
        if (!(item.role[role] && item.role[role].classType.includes(this.classType))) return false;
        switch (item.name) {
          case 'message': {
            // 优先级最高
            if (this.showMessage === false) {
              return false;
            }
            return !this.isPortrait || !this.isMobile || (this.isVideoPortraitClass && this.isStudent);
          }
          case 'member':
            return !this.isStudent && !this.isVideoPortraitClass;
          case 'document':
            return !this.isStudent && this.isVideoDoc;
          case 'notice':
            return (this.isVideoPortraitClass && !this.isTeacher) || !this.isVideoPortraitClass;
          case 'invite':
            return true;
          case 'setting':
            return (this.isVideoPortraitClass && !this.isTeacher) || !this.isVideoPortraitClass;
          case 'screenCapture':
            return !this.isPortrait && !this.isWeb;
          case 'tool-box':
          case 'allMemberController':
          case 'layout':
            return false;
          default:
            return true;
        }
      });

      const primaryMenu = filteredMenu.slice();
      const secondaryMenu = [];

      // 开始上课按钮
      const opBtnWidth = this.canStartClass ? 80 : 0;
      const primaryAvailableCount = Math.max(0, Math.floor((this.menuWrapperWidth - opBtnWidth) / this.menuItemWidth));

      if (primaryMenu.length > primaryAvailableCount) {
        const toSecondaryItemNum = primaryMenu.length - primaryAvailableCount + 1; // 会多出来一个 更多[...] 按钮，需要再挤压一个 item 进去 secondary
        const toSecondaryItems = primaryMenu.splice(-toSecondaryItemNum);
        secondaryMenu.unshift(...toSecondaryItems);
      }

      return {
        primary: primaryMenu,
        secondary: secondaryMenu,
      };
    },
  },
  watch: {
    menu(val) {
      // 如果用户通过自定义js 实现了全屏功能，就不要再显示全屏按钮了
      if (val.find(menu => menu.name === 'custom-fullscreen')) {
        this.showFullscreenIcon = false;
      }
    },
  },
  created() {
    if (this.menu.find(menu => menu.name === 'custom-fullscreen')) {
      this.showFullscreenIcon = false;
    }
  },
  mounted() {
    TCIC.SDK.instance.registerState(Constant.TStateHeaderVisible, '顶部菜单栏是否可见', true);
    this.makeSureClassJoined(this.onJoinClass);
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isMobileNative = TCIC.SDK.instance.isMobileNative();
    this.isMiniProgram = TCIC.SDK.instance.isMiniProgram();
    this.isWeb = TCIC.SDK.instance.isWeb();
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.isAIRoom = TCIC.SDK.instance.isAiRoom();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    const resizeObserver = new ResizeObserver(() => {
      this.onHeaderRightResize();
    });
    resizeObserver.observe(this.$refs.headerRightRef);
    this.cleanupResizeObserver = () => {
      resizeObserver.disconnect();
    };
    this.isShowFeedbackQr = Util.shouldShowQrcode();
    if (this.isShowFeedbackQr) {
      this.$nextTick(() => {
        this.observerWechatQrDom();
      });
    }
  },
  beforeDestroy() {
    this.unbindEvent();
    this.cleanupResizeObserver?.();
  },
  methods: {
    // 上下课
    startClass() {
      // this.$parent.startClass();
      const remindCom = TCIC.SDK.instance.getComponent('interact-reminder-component');
      remindCom.getVueInstance().startClass();
    },
    onJoinClass() {
      this.classJoined = true;
      this.classLayout = TCIC.SDK.instance.getClassLayout();
      // 绑定事件
      this.bindEvent();

      // 初始化状态
      this.initData();

      // 初始化popovers组件
      // this.initPopoverComponents();

      // 纯视频课默认全屏
      if (TCIC.SDK.instance.isVideoOnlyClass()) {
        TCIC.SDK.instance.setState(Constant.TStateFullScreen, true);
      }
    },

    initData() {
      const { className, classId, status } = TCIC.SDK.instance.getClassInfo();
      this.className = className;
      this.classId = classId;
      // classStatus 已经在 HeaderBase 里处理了
      // @TODO 待ClassSubType统一修改后，再统一使用枚举值修改
      this.classType = TCIC.SDK.instance.isInteractClass() ? 'interactive' : 'live';
      if (TCIC.SDK.instance.isUnitedClass()) {
        this.classType = (TCIC.SDK.instance.isUnitedRTCClass() || TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) ? 'unitedrtc' : 'unitedlive';
      }
      console.log(`[HeaderMobile] classType ${this.classType}`);

      // 纯视频是不需要加载白板和课件控制的
      this.isVideoDoc = TCIC.SDK.instance.isClassLayoutHasDoc();
      this.isUnitedLiveClass = TCIC.SDK.instance.isUnitedLiveClass();
    },

    bindEvent() {
      // 监听全屏状态变更
      this.addLifecycleTCICStateListener(Constant.TStateFullScreen, this.fullScreenStateChangeHandler);

      TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
        .then(() => {
          this.hasBoardPermission = TCIC.SDK.instance.getState(TCIC.TMainState.Board_Permission, false);
        });

      // 监听白板授权状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (value) => {
        this.hasBoardPermission = value;
      });
      // 是否在倒计时中
      this.addLifecycleTCICStateListener(Constant.TStateClassStartCountDowning, (val) => {
        this.isStartClassButtonDisabled = val;
      });
      // 点击整个窗口，导航隐藏倒计时
      // document.body.addEventListener('touchstart', this.menuToggle, true);
    },

    unbindEvent() {
      // 点击整个窗口，导航隐藏倒计时
      // document.body.removeEventListener('touchstart', this.menuToggle, true);
    },

    onHeaderRightResize: Lodash.throttle(function () {
      this.menuWrapperWidth = this.$refs.headerRightRef.clientWidth;
    }, 200),

    fullScreenStateChangeHandler(flag) {
      this.fullScreen = flag;
    },

    // 切换全屏状态
    changeFullScreen() {
      this.toggleFullScreen(!this.fullScreen);
    },

    toggleFullScreen(flag, isSync) {
      if (this.fullScreen !== flag) {
        this.fullScreen = flag;
        if (!isSync) {
          TCIC.SDK.instance.setState(Constant.TStateFullScreen, this.fullScreen);
        }
      }
    },

    // 退出
    logout() {
      // 请求离开课堂
      TCIC.SDK.instance.leaveClass();
    },

    menuToggle(event) {
      if (this.tooltipVisible && !event.target.classList.contains('sidebar-btn-list')) {
        setTimeout(() => {
          this.tooltipVisible = false;
        }, 100);
      } else if (event && (
        event.target.classList.contains('board-component') // 白板空白区
          || event.target.classList.contains('video-wall-component') // 视频墙
          || (event.target.classList.contains('tiw-sketch__canvas') && !this.hasBoardPermission) // 白板且不具备操作权限
          || event.target.classList.contains('video-list')   // 视频区
          || event.target.classList.contains('o-video__container')   // 1v1课视频区
          || event.target.classList.contains('live-component')    // CDN课视频区
          || event.target.classList.contains('tic_board_bg') // 操作栏
          || event.target.classList.contains('video__mask') // 视频
          || event.target.classList.contains('video-wrap') // 视频
          || event.target.tagName.toLowerCase() === 'video' // 视频
          || event.target.tagName.toLowerCase() === 'video-wall-component'      // 纯视频课视频组件
      )) {
        if (!this.topBarIsVisible) {
          TCIC.SDK.instance.setState(Constant.TStateHeaderVisible, true);
          this.topBarIsVisible = true;
          this.menuHideDelay();
        } else {
          TCIC.SDK.instance.setState(Constant.TStateHeaderVisible, false);
          this.topBarIsVisible = false;
          clearTimeout(this.menuHideDelayTask);
        }
      }
    },

    menuHideDelay() {
      clearTimeout(this.menuHideDelayTask);
      this.menuHideDelayTask = setTimeout(() => {
        TCIC.SDK.instance.setState(Constant.TStateHeaderVisible, false);
        this.topBarIsVisible = false;
        this.tooltipVisible = false;
      }, 3000);
    },
  },
};
</script>

<style lang="less">
.header-mobile-component {
  transition: all 0.3s;
  pointer-events: auto !important;
  background: var(--header-background-color, rgb(28, 33, 49));
  // background: black;
  width: 100%;
  padding-left: 10px;

  .header-mobile-component-demo-limitation{
    position: absolute;
    bottom: -50px;
    background-color: rgba(43, 46, 51, 0.5);
    .fade-mask-right{
      display: none;
    }
  }

  // 这个会导致展开/收起视频区时顶部按钮移动，不知道之前为什么加，UI已经改版了，先去掉
  // &.fullscreen {
  //   padding-right: 15px;
  // }

  .header__logout {

    span {
      display: block;
      width: 30px;
      height: 30px;
      background-image: url('./assets/icon-back-mobile.png');
      background-repeat: no-repeat;
    }
  }

  .top-bar {
    width: 100%;
    height: 45px;
    font-size: 13px;
    z-index: 10;
    display: flex;
    color: #fff;
  }
  .header__button--start {
    margin-left: 10px;
    border-radius: 6px;
  }

  .header__left {
    flex: 1;
    &.abs {
      width: 100%;
    }
  }
  .header__right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 5px;
    overflow: hidden;
    flex-grow: 1;

    .header__btn-text {
      color: #fff;
      display: none;
    }

    // 手机都不显示文字
    // &.is-portrait {
    //   .header__btn-text {
    //     display: none;
    //   }
    // }

    .header__right-button {
       margin-left: 8px;
    }
  }
  .class-info-sub-component {
    height: auto;
  }

  .header__logo {
    display: inline-block;
    vertical-align: middle;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: cover;

    .el-loading-spinner {
      margin-top: 0;
      top: 0;
      opacity: .5;
      height: 100%;

      .circular {
        width: 80%;
        height: 80%;
        top: 0;
        bottom: 0;
        margin: auto;
        position: absolute;
        left: 0;
        right: 0;
      }
    }
  }

  .header__logout {
    height: calc(100% - 16px);
  }

  .header__logout > span {
    height: 100% !important;
    background-position-y: center;
  }

  .header__span--duration {
    flex: 1;
    word-break: keep-all;
    white-space: nowrap;
    min-width: unset;
    justify-content: left;
  }

  .header__span--duration i {
    vertical-align: bottom;
  }

  .header__span--duration span {
    // position: absolute;
    // top: 15px;
  }

  .class-id {
    & > i {
      width: 20px;
      height: 20px;
      display: inline-block;
      background-image: url('./assets/icon-home.png');
      background-repeat: no-repeat;
      background-size: contain;
      vertical-align: bottom;
    }
  }

  .header__i.i--menu {
    width: 24px;
    height: 24px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
  }

  .setting-sub-component .more-text .header__i.i--menu {
    width: 50px;
    height: 40px;
    margin: 0px 3px;
    &.is-portrait {
      width: 24px;
      height: 24px;
      background-size: contain;
    }
  }

  .screen-capture-sub-component {
    .button--secondary {
      .icon-screen-capture{
        width: 40px !important;
        height: 40px !important;
        margin: 0;
      }
    }
  }

  .side-bar-btn {
    &.more-btn {
      margin-top: 0px;
    }

    i {
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 75%;
    }

    .icon-invite {
      background-image: url('./assets/ic_navigation_invite.svg');
    }

    .icon-more {
      position: relative;
      width: 40px;
      height: 40px;
      background-image: url('./assets/ic_navigation_more.svg');
      background-size: 65%;
      vertical-align: middle;
    }

    .icon-handdown {
      background-image: url('./assets/icon-handdown.png');
    }

    .icon-handup {
      background-image: url('./assets/icon-handup.png');
    }

    .icon-message {
      background-image: url('./assets/new-icons/ic_chat.svg');
      background-size: 50%;
    }

    .icon-scale__full {
      background-image: url('./assets/icon-scale__full.png');
    }

    .icon-scale__full_exit {
      background-image: url('./assets/icon-scale__unfull.png');
    }

    .msg-count {
      position: absolute;
      top: 2px;
      right: -3px;
      height: 15px !important;
      border-radius: 8px;
      background-color: #FA6400;
      text-align: center;
      line-height: 15px;
      display: inline-block;
    }

    .handup-countdown {
      display: block;
      text-align: center;
      background: #006EFF;
      border-radius: 50%;
      width: 100%;
      height: 100%;
      font-size: 16px;
      line-height: 40px;
      font-weight: 500;
      color: #FFFFFF;
    }
  }

  .badge {
    .el-badge__content {
      background-color: #FA6400;
      border: 0;

      &.is-fixed {
        right: 0;
        transform: scale(0.8);
        transform-origin: right;
      }
    }
  }
}

.sidebar-btn-list {
  display: flex;

  li {
    width: 60px;
    text-align: center;
    color: #fff;
  }

  .header__i.i--menu {
    width: 40px;
    height: 40px;
    background-size: 60%;
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
  }

  .header__i {
    display: inline-block;
  }

  .member-sub-component {
    .icon-member {
      background-repeat: no-repeat;
      background-position: center;
    }
  }

  .im-sub-component {
    .icon-message {
      width: 40px !important;
      height: 40px !important;
      margin: 0;
      background-size: 60% !important;
    }
  }
  .notice-sub-component {
    .button--secondary {
      .icon-notice {
        width: 40px !important;
        height: 40px !important;
        margin: 0;
        background-size: 60% !important;
      }
    }
  }

  .setting-sub-component {
    .ic_nav_setting {
      width: 40px !important;
      height: 40px !important;
      margin: 0;
      background-size: 60% !important;
    }
  }

  .header__btn-text {
    display: none;
  }
}

.header-component .header-mobile-component .header__right .header__button-custom {
  width: 40px;
  .header__i.i--menu {
    background-size: contain;
  }
}

@media (max-width: 530px) {
    .header-title-mobile {
      display: flex;
      flex-direction: column;
    }
}
</style>
