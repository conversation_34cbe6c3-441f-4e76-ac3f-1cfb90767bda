<template>
  <div
    class="big-class-desktop"
  >
    <div class="left-area">
      <div
        ref="boardBodyRef"
        :style="{height:`calc(100% - ${footerHeight}px)`}"
        class="white-board-container"
      />
      <div
        ref="fullVideoAreaRef"
        :style="{height:`calc(100% - ${footerHeight}px)`}"
        class="desktop-full-video-area"
      />
      <div
        class="desktop-footer-area"
        ref="footerAreaRef"
      >
        <Footer />
        <DocThumbnail />
        <!-- <Thumbnail v-show="showThumbNail" /> -->
      </div>
    </div>

    <div class="right-area">
      <SideToggleButton
        :style="{
          width: '40px',
          height: '40px',
          position: 'absolute',
          bottom: '72.5px',
          zIndex: 999,
          left: '-40px'
        }"
      />
      <CollegeVideoSwitchComponent
        v-show="!studentFullState.isFull && !teacherFullState.isFull && !isStudent && !isFullScreen"
        class="board-switch"
        :throttle="300"
      />
      <div
        v-show="!isFullScreen"
        class="right-area-inner"
      >
        <div
          ref="videoAreaRef"
          class="desktop-video-area"
          :class="{'board-place': isBigVideoMode}"
        />
        <div
          ref="imAreaRef"
          class="im-area"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import SideToggleButton from '@/component/vue-component/im-component/SideToggleButton.vue';
import CollegeVideoSwitchComponent from '@/component/vue-component/college-video-switch-component/CollegeVideoSwitchComponent.vue';
import Constant from '@/util/Constant';
import Footer from './components/Footer.vue';
import DocThumbnail from './components/DocThumbnail.vue';
import { useResizeObserver, useWindowSize } from '@vueuse/core';
import { useBoardTool } from './hooks';
import { watch, ref, onMounted, nextTick, computed } from 'vue';
import { $EventBus } from '@core/BaseComponent';
import { useScreenShare, useRole, useTask } from '../hooks';

const boardMounted = ref(false);
// 白板是否全屏
const isFullScreen = ref(false);
// 老师学生视频是否放大
const teacherFullState = ref({ isFull: false, domId: '' });
const studentFullState = ref({ isFull: false, domId: '' });

const boardWidth = ref(0);
// 高度会因为PPT 缩略图发生变化
const footerHeight = ref(60);
const isBigVideoMode = ref(false);
const { isStudent } = useRole();
const { width: windowWidth } = useWindowSize();

// 一堆 DOM Ref
const boardBodyRef = ref(null);
const videoAreaRef = ref(null);
const fullVideoAreaRef = ref(null);
const imAreaRef = ref(null);
const footerAreaRef = ref(null);

const { isScreenShareAdvanceMode, isScreenShareOnElectron } = useScreenShare();

const boardWidthCss = computed(() => `${boardWidth.value}px`);
const fullVideoWidth = computed(() => {
  const fullVideoCount = Math.max(Number(studentFullState.value.isFull) + Number(teacherFullState.value.isFull), 1);
  return `${boardWidth.value / fullVideoCount}px`;
});
const videoWidth = computed(() => {
  if (windowWidth.value < 1440) {
    return 286;
  }
  if (windowWidth.value < 1920) {
    return 322;
  }
  return 452;
});

const percentVideoBoard = computed(() => videoWidth.value / boardWidth.value);

const { updateTask } = useTask('bigClass-video-sync', (data, taskInfo) => {
  /**
   * data.type: bigVideoMode - 视频白板互换 videoCoverMode - 视频覆盖在白板上
   * data.payload: type 对应的视频状态
   */
  console.log('bigClass-video-sync::taskUpdate', data, taskInfo);
  const { type, payload } = data;
  if (type === 'bigVideoMode') {
    isBigVideoMode.value = payload.isBigVideoMode;
    TCIC.SDK.instance.setState(Constant.TStateBigVideoMode, payload.isBigVideoMode);
  } else if (type === 'videoCoverMode') {
    teacherFullState.value = payload.teacherFullState;
    studentFullState.value = payload.studentFullState;
  }
});

const props = defineProps({
  teacherVideo: Object,
  studentVideos: Array,
});

const videos = computed(() => ({
  teacherVideo: props.teacherVideo,
  studentVideos: props.studentVideos,
}));

watch(videos, ({ studentVideos }) => {
  if (studentVideos.length === 0) {
    studentFullState.value.isFull = false;
  }
});

const initDesktopLayout = () => {
  const boardComponent = TCIC.SDK.instance.getComponent('board-component');
  const screenPlayerComponent = TCIC.SDK.instance.getComponent('screen-player-component');

  TCIC.SDK.instance.updateComponent('board-component', {
    display: 'block',
    // position: 'relative', !!不敢用 relative,否则窗口缩放时有问题
  });
  boardBodyRef.value.appendChild(boardComponent);
  boardMounted.value = true;
  boardBodyRef.value.appendChild(screenPlayerComponent);
  // 放好 im
  TCIC.SDK.instance.updateComponent('introduction-discuss-component', {
    display: 'block',
  }).then(() => {
    imAreaRef.value.appendChild(TCIC.SDK.instance.getComponent('introduction-discuss-component'));
  });
};

const dbClickHandler = (ele) => {
  // 双击放大和白板切换两种模式互斥，屏幕分享下不支持双击切换
  if (isBigVideoMode.value || isScreenShareOnElectron.value) {
    return;
  }
  // 学生不支持双击
  if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() || TCIC.SDK.instance.isSupervisor()) {
    const isTeacherDom = ele.tagName.toLowerCase() === 'teacher-component';
    if (isTeacherDom) {
      teacherFullState.value.isFull = !teacherFullState.value.isFull;
      teacherFullState.value.domId = ele.id;
    } else {
      studentFullState.value.isFull = !studentFullState.value.isFull;
      studentFullState.value.domId = ele.id;
    }
    updateTask({
      type: 'videoCoverMode',
      payload: {
        teacherFullState: teacherFullState.value,
        studentFullState: studentFullState.value,
      },
    });
  }
};

const moveVideo = ({ isFull, domId }) => {
  const dom = document.getElementById(domId);
  if (!dom) {
    console.warn('bigClass-video-sync::movevideo dom not found', domId);
    return;
  }
  if (isFull) {
    fullVideoAreaRef.value.appendChild(dom);
    dom.getVueInstance().setControlDirect('bottom');
  } else {
    videoAreaRef.value.appendChild(dom);
    dom.getVueInstance().setControlDirect('left');
  }
  if (TCIC.SDK.instance.isPad() && TCIC.SDK.instance.isWeb()) {
    // fix https://tapd.woa.com/tapd_fe/70068217/bug/detail/1070068217137438521
    dom.style.display = 'none';
    setTimeout(() => {
      dom.style.display = 'block';
    }, 300);
  }
};

const initVideos = ({ teacherVideo, studentVideos }) => {
  console.warn('bigClass-video-sync::initVideos', teacherVideo, studentVideos);
  const promiseArr = [];
  if (teacherVideo) {
    promiseArr.push(TCIC.SDK.instance.updateComponent('teacher-component', {
      left: '0',
      top: '0',
      width: '100%',
      height: 'var(--video-height)',
      display: 'block',
      position: 'relative',
    }).then(() => {
      const ele = TCIC.SDK.instance.getComponent('teacher-component');
      const teacherVue = ele.getVueInstance();
      teacherVue.setControlDirect('left');
      teacherVue.setDbListener(() => dbClickHandler(ele));
      if (ele && ele.parentElement !== videoAreaRef.value) {
        videoAreaRef.value.appendChild(ele);
      }
    }));
  }
  studentVideos?.forEach((info) => {
    promiseArr.push(TCIC.SDK.instance.updateComponent('student-component', {
      left: '0',
      top: '0',
      width: '100%',
      height: 'var(--video-height)',
      display: 'block',
      position: 'relative',
    }, info.userId).then(() => {
      const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
      const studentVue = studentDom.getVueInstance();
      studentVue.setDbListener(() => dbClickHandler(studentDom));
      if (studentDom && studentDom.parentElement !== videoAreaRef.value) {
        console.log('initVideos appendChild');
        studentDom.getVueInstance().setControlDirect('left');
        videoAreaRef.value.appendChild(studentDom);
      }
    }));
  });
  return Promise.all(promiseArr);
};

// 填充videos
watch(videos, (val)  => {
  console.warn('bigClass-video-sync::videos change', val.studentVideos);
  nextTick(() => {
    console.warn('isScreenShareOnElectron', isScreenShareOnElectron);
    if (isScreenShareOnElectron.value) {
      const ele = TCIC.SDK.instance.getComponent('screen-videowrap-component');
      ele.getVueInstance().loadVideos(val);
    } else {
      initVideos(val).then(() => {
        // 同步远端布局
        console.warn('bigClass-video-sync::initVideos success', teacherFullState.value, studentFullState.value);
        if (isBigVideoMode.value) {
          switchTeacherAndBoard(true);
        }
        if (teacherFullState.value.isFull) {
          moveVideo(teacherFullState.value);
        }
        if (studentFullState.value.isFull) {
          moveVideo(studentFullState.value);
        }
      });
    }
  });
});

const switchTeacherAndBoard = (val) => {
  console.log('bigClass-video-sync switchTeacherAndBoard', val);
  const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
  if (val) {
    fullVideoAreaRef.value.appendChild(teacherDom);
    TCIC.SDK.instance.setState(Constant.TStateIsShowThumbnailComponent, false);
    TCIC.SDK.instance.updateComponent('board-component', {
      width: 'var(--board-width)',
      height: 'calc(var(--board-width) / 16 * 9)',
      transform: 'scale(var(--percent-video-board))',
      transformOrigin: 'left top',
      zIndex: 10,
      position: 'absolute',
    }).then(() => {
      videoAreaRef.value.appendChild(TCIC.SDK.instance.getComponent('board-component'));
    });
    TCIC.SDK.instance.getComponent('board-tool-component').getVueInstance().showBoardTool = false;
  } else {
    videoAreaRef.value.appendChild(teacherDom);
    TCIC.SDK.instance.updateComponent('board-component', {
      width: 'var(--board-width)',
      height: 'calc(var(--board-width) / 16 * 9)',
      zIndex: 1,
      transform: 'none',
      position: 'static',
    }).then(() => {
      boardBodyRef.value.appendChild(TCIC.SDK.instance.getComponent('board-component'));
    });
    TCIC.SDK.instance.getComponent('board-tool-component').getVueInstance().showBoardTool = true;
  }
  if (TCIC.SDK.instance.isPad() && TCIC.SDK.instance.isWeb()) {
    // fix https://tapd.woa.com/tapd_fe/70068217/bug/detail/1070068217137438521
    teacherDom.style.display = 'none';
    setTimeout(() => {
      teacherDom.style.display = 'block';
    }, 300);
  }
};

watch(teacherFullState, val => moveVideo(val), { deep: true });
watch(studentFullState, val => moveVideo(val), { deep: true });
// 切换白板和 teacher
watch(isBigVideoMode, switchTeacherAndBoard);

useBoardTool(boardBodyRef);

onMounted(() => {
  nextTick(() => {
    boardWidth.value = boardBodyRef.value.getBoundingClientRect().width;
    useResizeObserver(footerAreaRef, (entries) => {
      const entry = entries[0];
      const { height } = entry.contentRect;
      footerHeight.value = height;
    });
    useResizeObserver(boardBodyRef, (entries) => {
      const entry = entries[0];
      const { width } = entry.contentRect;
      boardWidth.value = width;
    });
    initDesktopLayout();
    // 通常这里的videos 为空，这里初始化一下，因为 AV_ADD 还没触发
    initVideos(videos.value);
  });
  TCIC.SDK.instance.subscribeState(Constant.TStateFullScreen, (fullScreen) => {
    isFullScreen.value = fullScreen;
    if (fullScreen) {
      TCIC.SDK.instance.setState(Constant.TStateIsShowThumbnailComponent, false);
    }
  });
  TCIC.SDK.instance.subscribeState(Constant.TStateBigVideoMode, (val) => {
    if (isBigVideoMode.value === val) {
      return;
    }
    if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() || TCIC.SDK.instance.isSupervisor()) {
      updateTask({ type: 'bigVideoMode', payload: { isBigVideoMode: val } });
    }
    isBigVideoMode.value = val;
  }, {
    noEmitWhileSubscribe: true,
  });
  $EventBus.$on('screen-component-ready', () => {
    TCIC.SDK.instance.updateComponent('screen-component', {
      zIndex: 3,
    }).then(() => {
      boardBodyRef.value.appendChild(TCIC.SDK.instance.getComponent('screen-component'));
    });
  });
});

const resetVideoLayout = () => {
  if (isBigVideoMode.value) {
    TCIC.SDK.instance.setState(Constant.TStateBigVideoMode, false);
  }
  teacherFullState.value.isFull = false;
  studentFullState.value.isFull = false;
  updateTask({
    type: 'videoCoverMode',
    payload: {
      teacherFullState: teacherFullState.value,
      studentFullState: studentFullState.value,
    },
  });
  updateTask({ type: 'bigVideoMode', payload: { isBigVideoMode: false } });
  // 确保 dom 在 videoAreaRef 中， screen-videowrap-component loadvideos 不会失败
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 0);
  });
};

// 监听electron屏幕分享状态
watch(isScreenShareOnElectron, async (val) => {
  if (val) {
    await resetVideoLayout();
    console.warn('isScreenShareAdvanceMode', isScreenShareAdvanceMode.value);
    const boardToolTop = isScreenShareAdvanceMode.value ? TCIC.SDK.instance.getComponent('board-tool-component').style.top : '100px';
    TCIC.SDK.instance.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode.value ? '50px' : '64px',  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: 'block',
      });
    if (isScreenShareAdvanceMode.value) {
      // 全屏共享时展示弹幕聊天
      TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().hasSideIM = false;
    }
    // 更新屏幕分享视频容器布局
    TCIC.SDK.instance.updateComponent('screen-videowrap-component', {
      display: 'block',
      width: '176px',
      top: boardToolTop,
      left: 'calc(100% - 176px - 90px)',
      style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
    })
      .then((success) => {
        if (success) {
          const ele = TCIC.SDK.instance.getComponent('screen-videowrap-component');
          if (ele) {
            console.log('bigClass-video-sync loadVideos');
            ele.getVueInstance().loadVideos(videos.value);
          }
        }
      });
    if (isScreenShareAdvanceMode.value) {
      document.body.style.backgroundColor = 'transparent';
    }
  } else {
    TCIC.SDK.instance.updateComponent('share-toolbar-component', {
      display: 'none',
    });
    TCIC.SDK.instance.updateComponent('screen-videowrap-component', {
      display: 'none',
      style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
    });
    TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().hasSideIM = true;
    document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    console.warn('initVideos isScreenShareAdvanceMode', videos);
    initVideos(videos.value);
  }
});
</script>

<style lang="less">
.big-class-desktop{
  width: 100%;
  height: 100%;
  --board-width: v-bind(boardWidthCss);
  --percent-video-board: v-bind(percentVideoBoard);
  --video-width: v-bind(videoWidth + 'px') ;
  position: relative;
  display: flex;
  .left-area{
    position: relative;
    flex: 1;
    width: calc(100% - var(--video-width));
    .desktop-footer-area{
      pointer-events: none;
      position: absolute;
      align-items: center;
      justify-content: center;
      // bottom: 10px;
      z-index: 99;
      left: 0;
      width: 100%;
    }
    .desktop-full-video-area{
      position: absolute;
      display: flex;
      align-items: center;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      --full-video-width: v-bind(fullVideoWidth);
      --video-height: calc(var(--full-video-width) * 9 / 16);
      teacher-component{
        order: 0;
        width: var(--full-video-width)!important;
      }
      student-component{
        order: 1;
        width: var(--full-video-width)!important;
      }
    }
  }
  .white-board-container{
    position: relative;
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }
  /* 对应 Util.getRightSideWidth */
  .right-area{
    --video-height: calc(var(--video-width) * 9 / 16);
    display: flex;
    position: relative;
    flex-direction: column;
    &-inner{
      width: var(--video-width);
      display: flex;
      flex-direction: column;
      height: 100%;
      .desktop-video-area.board-place::before{
        content: '';
        width: 100%;
        height: var(--video-height);
      }
    }
    .board-switch{
      position: absolute;
      top: calc(var(--video-height) - 40px);
      right: 10px;
      z-index: 999;
    }
    .desktop-video-area{
      position: relative;
      display: flex;
      flex-direction: column;
      teacher-component{
        order: 0;
      }
      student-component{
        order: 1;
      }
    }
    .im-area{
      flex: 1;
      position: relative;
      width: var(--video-width);
    }
  }
}
// @media (min-width: 1440px) and (max-width: 1920px) {
//     .big-class-desktop{
//       --video-width: 322px;
//     }
//   }
//   @media (min-width: 1920px){
//     .big-class-desktop{
//       --video-width: 452px;
//     }
//   }
</style>
