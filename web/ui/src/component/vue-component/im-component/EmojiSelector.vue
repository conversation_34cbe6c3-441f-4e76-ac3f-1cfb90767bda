<template>
  <el-popover
    ref="popover"
    :popper-class="popoverClass"
    placement="top-start"
    width="226"
    trigger="click"
    :disabled="disabled"
  >
    <i
      slot="reference"
      class="ic-emoji"
      :class="disabled ? 'is-disabled' : ''"
    ><img
       src="./assets/ic_emoji.svg"
       alt=""
     >
      <span v-if="false && !isMobile && !isPad">{{ $t('表情') }}</span></i>
    <div class="emoji-popper-content">
      <i
        class="ic-emoji-01"
        @click="sendEmoji('[鼓掌]')"
      />
      <i
        class="ic-emoji-02"
        @click="sendEmoji('[强]')"
      />
      <i
        class="ic-emoji-03"
        @click="sendEmoji('[玫瑰]')"
      />
      <i
        class="ic-emoji-04"
        @click="sendEmoji('[爱心]')"
      />
    </div>
  </el-popover>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import Util from '@util/Util';

export default {
  name: 'EmojiSelector',
  components: {
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isPad: TCIC.SDK.instance.isPad(),
      isMobile: TCIC.SDK.instance.isMobile(),
    };
  },
  computed: {
    popoverClass() {
      if (!this.isPad && !this.isMobile) return 'emoji-popper client-black';
      if (this.isPad) return 'emoji-popper pad-black';
      return 'emoji-popper';
    },
  },
  watch: {
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    sendEmoji(emoji) {
      this.$emit('send-emoji', emoji);
    },
  },
};
</script>
<style lang="less">
.ic-emoji.is-disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}
</style>
