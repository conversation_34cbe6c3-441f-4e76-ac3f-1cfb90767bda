// IMMsg 和 DiscussMsg 共用

.im-component-msg {
  .im-component-msg-control {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: flex-start;
    margin-left: 8px;

    div {
      width: 16px;
      height: 16px;
      margin: 0 4px;
      background-repeat: no-repeat;
      background-position: 50% 50%;

      &:hover {
        border-radius: 2px;
        border: 1px solid #306ff6;
      }

      &:active {
        border-radius: 2px;
        border: 1px solid #306ff6;
      }
    }
  }

  .control-reply {
    background-image: url('./assets/ic_msg_reply.svg');
  }

  .control-delete {
    background-image: url('./assets/ic_msg_delete.svg');
  }

  .control-silence {
    background-image: url('./assets/silence.svg');
  }

  .control-silence-cancel {
    background-image: url('./assets/silence_selected.svg');
  }
}