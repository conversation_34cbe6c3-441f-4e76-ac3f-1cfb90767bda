<template>
  <div
    v-if="msg && msg.seq"
    class="im-component-msg-control"
  >
    <div
      v-if="showReply"
      class="control-reply"
      :title="$t('回复')"
      @click="$emit('click-reply')"
    />

    <div
      v-if="hasFullPermission"
      class="control-delete"
      :title="$t('删除')"
      @click="$emit('click-delete')"
    />
    <div
      v-if="!msg.ownMsg && !isTeacher(msg.from) && hasFullPermission"
      :class="msg.silenceMsg ? 'control-silence-cancel' : 'control-silence'"
      :title="msg.silenceMsg ? $t('取消禁言') : $t('禁言')"
      @click="$emit('click-toggle-silence')"
    />
  </div>
</template>

<script>

export default {
  props: {
    msg: {
      type: Object,
      default: null,
    },
    showReply: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
  data() {
    return {
      isSmallScreen: TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad(),
      isSupervisor: TCIC.SDK.instance.isSupervisor(),
      msgForShow: null,
    };
  },
  computed: {
    hasFullPermission() {
      return TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() || TCIC.SDK.instance.isSupervisor();
    },
  },
  methods: {
    isTeacher(userId) {
      return TCIC.SDK.instance.isTeacher(userId);
    },
    isAssistant(userId) {
      return TCIC.SDK.instance.isAssistant(userId);
    },
  },
};
</script>

<style lang="less">
@import './MsgControl.less';
</style>
