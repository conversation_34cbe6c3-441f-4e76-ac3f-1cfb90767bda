<template>
  <div
    :class="['portrait-im-component', {
      'tcic': isTCICComponent,
      'small-screen': isSmallScreen,
    }]"
    divider
    @hide="hide"
  >
    <IMComponent
      :need-camera-foot="!isTeacher"
      :showIMFooter="showIMFooter"
    />
  </div>
</template>

<script>
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import IMComponent from './IM.vue';

export default {
  name: 'PortraitIMComponent',
  components: {
    IMComponent,
  },
  extends: BaseComponent,

  data() {
    return {
      isTeacher: false,
      showIMFooter: true,
    };
  },

  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
    });

    if (TCIC.SDK.instance.isInteractClass()) {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
        // 有sideIM时关闭消息盒子
        const hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(layout).sideIM;
        if (hasSideIM) {
          this.hide();
        }
      });
    }
  },

  methods: {
    toggleDraggable(draggable) {
      this.toggleComponentDrag(draggable, '.float-im-component .ui-box-header');
    },
    hide() {
      this.toggleDraggable(false);
      TCIC.SDK.instance.setState(Constant.TStateShowChatBox, false);
      // 通知父组件该组件即将被隐藏（主要供Header里使用）
      if (!this.isTCICComponent) {
        this.$emit('hide');
      } else {
        this.updateComponent({
          display: 'none',
        }).then();
      }
    },
  },
};
</script>

<style lang="less">
.portrait-im-component {
  width: 100%;
  height: 100%;
  transition: 0.5s all;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  padding: 12px 0;
  background: black !important;
  border-radius: 4px;
  border-top: .5px solid #211d1d;
  box-shadow: 1px 1px grey;
  &.tcic {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
  }

  &.small-screen {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 6px 0;

    .component-header-floatim {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 8px;
      height: 30px;

      .header-title-floatim {
        font-size: 14px;
        font-weight: 500;
        color: #8A9099;
        line-height: 14px;
        height: 14px;
      }

      .header-close-floatim {
        width: 14px;
        height: 14px;
        border-radius: 2px;
      }
    }
  }

  .float-im-component-divider-wrapper {
    padding: 0px;
    height: 1px;
    width: 400px;
    background-color: rgba(184, 184, 184, 0.1);
  }

  .component-header-floatim {
    //cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 16px 24px 16px;
    height: 76px;
  }

  .el-popover.header-component-popover {
    background: #1C2131 !important;
  }
}

</style>
