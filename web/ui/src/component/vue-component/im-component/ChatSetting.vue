<template>
  <div
    class="chat-setting"
    :class="{ disabled: disabled, 'small-screen': isSmallScreen }"
  >
    <div
      class="chat-setting-area"
      @click="toggleDropdown"
    >
      <span class="chat-setting-icon" />
      <span v-if="false" class="chat-setting-text">{{ $t('设置') }}</span>
    </div>
    <!-- 弹出框，搜索框和用户列表 -->
    <div
      v-if="showDropdown"
      class="dropdown"
      :class="{'small-screen': isSmallScreen }"
    >
      <ul class="setting-list">
        <li
          v-for="setting in settingsList"
          :key="setting.id"
          :class="{ selected: setting.id === silenceMode }"
          @click="selectSilenceMode(setting)"
        >
          <span>{{ setting.label }}</span>
          <span
            class="selected-label"
            :class="{ noneSelected: setting.id !== silenceMode }"
          />
        </li>
      </ul>
    </div>
  </div>
</template>


<script>

export default {
  name: 'ChatSetting',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    silenceMode: {
      type: Number,
      default: 0,
    },
    settingsList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      showDropdown: false,
      settingList: [],
    };
  },
  watch: {
    disabled(newVal) {
      if (newVal && this.showDropdown) {
        this.showDropdown = false;
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      if (this.$el && !this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    toggleDropdown() {
      if (this.disabled) return;
      this.showDropdown = !this.showDropdown;
    },
    selectSilenceMode(silenceMode) {
      this.$emit('update:silenceMode', silenceMode.id);
      this.showDropdown = false;
    },
  },
};
</script>

<style lang="less">
.chat-setting {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;

  .chat-setting-area{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: auto;
    height: 32px;
    padding: 2px 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    &:hover {
      background-color: #3a3f4b;
    }

    .chat-setting-icon {
      width: 16px;
      height: 16px;
      margin: 0;
      background-image: url('./assets/chat_setting.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }

    .chat-setting-text {
      font-size: 12px;
      color: #C5CCDB;
      margin-left: 8px;
      @media (max-width: 1920px) {
        display: none;
      }
    }
  }

  &.small-screen {
  }

  .dropdown {
    position: absolute;
    bottom: 130%;
    left: -104px;
    background-color: #2a2d3d;
    border-radius: 5px;
    padding: 10px 4px;
    margin-top: 10px;
    width: 160px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 10;
    &.small-screen{
      left: 0;
    }
    @media (max-width: 1920px) {
      left: -137px;
    }
    .setting-list {
      max-height: 200px;
      overflow-y: auto;
      span {
        color: #CFD4E5;
      }

      li {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 5px;
        transition: background-color 0.2s;

        &:hover {
          background-color: #383c51;
        }

        &.selected {
          background-color: #41475f;
        }

        .selected-label {
          margin-left: auto;

          width: 16px;
          height: 16px;
          background-image: url('./assets/yes.svg');
          background-repeat: no-repeat;
          background-size: contain;
          &.noneSelected {
            display: none;
            width: 0;
          }
        }
      }
    }
  }
}
.chat-setting.disabled {
  opacity: 0.7;
  pointer-events: none;
}
</style>
