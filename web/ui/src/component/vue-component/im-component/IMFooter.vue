<template>
  <div
    :class="['im-component-footer', {
      'small-screen': isMobile,
      'small-screen-low': isSmallScreen,
      'show-reply': !!replyMsg
    }]"
  >
    <div
      class="im-component-top-tools-line-first"
    >
      <div style="display: flex; flex-direction: row">
        <ReceiverSelector
          v-if="showReceiverSelector"
          :disabled="replyMsg && !!replyMsg.dataExt?.IsPrivateMsg"
          :current-receiver="currentPrivateChatReceiver"
          :private-chat-receiver-list="privateChatReceiverList"
          @update:currentReceiver="handleReceiverUpdate"
        />
        <ChatSetting
          v-if="(isTeacher || isAssistant || isSupervisor)"
          :settings-list="silenceModeList"
          :silence-mode="currentSilenceMode"
          style="margin-left: 8px"
          @update:silenceMode="handleSilenceModeUpdate"
        />
      </div>
      <div
        class="im-component-top-tools-line-first-right"
      >
        <div
          v-if="false && (!isStudent || isMobile) && (isTeacher || isAssistant || isSupervisor)"
          :class="['im-component-tool',
                   {'small-screen': isMobile},
                   !silenceAll ? 'silence': 'silence-selected']"
          :title="silenceAll ? $t('解除全员禁言'): $t('全员禁言')"
          @click="switchAllSilence()"
        />
        <a
          v-if="!isStudent || isMobile"
          :class="['im-component-img-tool',
                   {'small-screen': isMobile},
                   isMobile ? '' : 'im-component-img-tool-mobile',
                   !imageSelected ? 'image': 'image-selected']"
          @click.capture="onInputFile"
        >
          <input
            id="imagePicker"
            ref="imagePicker"
            :key="imagePickerIndex"
            type="file"
            :accept="supportFileMessage ? '' : acceptExt"
            @change="handleMobileChange"
          >
        </a>
        <TranslatorSelector
          v-if="(!isStudent || isMobile) && showTranslator"
          :class="{ 'small-screen': isMobile }"
        />
        <!-- 举手相关操作-->
        <div
          v-if="canMemberHandup"
          :class="['im-component-handup', {'portrait-handup_icon': isPortrait && isMobile }]"
        >
          <HandUpIcon
            :is-class-start="isClassStart"
            class="im-footer-handup-icon"
          />
        </div>
        <CameraFootComponent v-if="needCameraFoot && isStageUp" />
      </div>
    </div>
    <IMInputReplyMobile
      v-if="replyMsg"
      :msg="replyMsg"
      @click-close="setReplyMsg(null)"
    />
    <div class="im-component-top-tools">
      <div
        :style=" isClassStart ? {'width': '100%', 'background-color':isMobile ? '#292d38' : 'rgb(120 120 122 / 50%)'}
          : { 'width': '80%', 'background-color':isMobile ? '#292d38' : 'rgb(120 120 122 / 50%)'}"
        class="im-component-tools-chat"
      >
        <div class="im-component-tools-left">
          <el-popover
            v-if="showEmojiButton"
            ref="popover"
            :popper-class="isMobile ?
              (isSmallScreen ? 'im-component-meme-popover-smallscreen' : 'im-component-meme-popover-mobile')
              : 'im-component-meme-popover'"
            placement="top"
            trigger="click"
            :disabled="isSupervisor"
            @show="showEmoji"
            @hide="hideEmoji"
          >
            <div
              slot="reference"
              :class="[
                'im-component-tool',
                {
                  'small-screen': isMobile,
                  'is-disabled': isSupervisor,
                } ,
                !emojiSelected ? 'meme': 'meme-selected',
              ]"
            />
            <Emoji
              ref="emoji"
              class="inComponent"
              :is-closed="emojiClosed"
              @send-emoji="sendEmoji"
            />
          </el-popover>
        </div>
        <InputElement
          ref="input"
          :class="[
            isMobile ? 'im-component-input-mobile-smallscreen' : 'im-component-input-mobile'
          ]"
          @enter="textInputEnter"
          @change="textChange"
          @click="textInputClick"
        />
      </div>
      <div
        v-if="imgSend && !isStudent"
        :class="[
          isMobile ? 'im-component-input-mobile-smallscreen' : 'im-component-input-mobile'
        ]"
      >
        <img :src="imgFile">
      </div>
    </div>
  </div>
</template>

<script>

import Emoji from './Emoji.vue';
import { IMBaseComponent } from '@vueComponent/im-component/IMBase';
import CameraFootComponent from '../video-component/CameraFootComponent.vue';
import TranslatorSelector from './TranslatorSelector';
import HandUpIcon from '../hand-up-member-component/HandUpIcon.vue';
import InputElement from './InputElement.vue';
import i18next from 'i18next';
import ReceiverSelector from '@/component/vue-component/im-component/ReceiverSelector.vue';
import IMInputReplyMobile from '@/component/vue-component/im-component/IMInputReplyMobile.vue';
import ChatSetting from '@/component/vue-component/im-component/ChatSetting.vue';
const VALID_IMG_TYPES = ['JPG', 'JPEG', 'PNG', 'GIF', 'BMP'];
export default {
  components: {
    ChatSetting,
    IMInputReplyMobile,
    ReceiverSelector,
    CameraFootComponent,
    TranslatorSelector,
    HandUpIcon,
    InputElement,
    Emoji,
  },
  extends: IMBaseComponent,
  props: {
    needCameraFoot: {
      type: Boolean,
      default: false,
    },
    showEmojiButton: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isLiveClass: TCIC.SDK.instance.isLiveClass(),
      showTranslator: false,
      inputEmpty: true, // 输入内容的长度 >0 false
      emojiSelected: false,
      emojiClosed: false,
      imgSend: false,
      imageSelected: false,       // 图片按钮处于选中状态
      isClassStart: false,
      imagePickerIndex: 0,
      isPortrait: false,
      canMemberStageUp: TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0,
      isStageUp: false,
      supportFileMessage: false,
    };
  },
  computed: {
    canMemberHandup() {
      // 举手可用
      return !this.isLiveClass && this.canMemberStageUp;
    },
    showReceiverSelector() {
      if (!this.supportPrivateChat) {
        return false;
      }
      if (this.isStudent && !this.isAssistant) {
        return this.currentSilenceMode === 0 || this.currentSilenceMode === 2;
      }
      return true;
    },
  },
  watch: {
    replyMsg(val) {
      this.$emit('update-reply-msg', val);
    },
  },
  mounted() {
    this.showTranslator = TCIC.SDK.instance.isFeatureAvailable('IMTranslator') && TCIC.SDK.instance.isOneOnOneClass();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_All, (silenceAll) => {
      this.silenceAll = silenceAll;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_Mode, (silenceMode) => {
      this.silenceAll = silenceMode === TCIC.TClassSilenceMode.All_Mute;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      if (status > TCIC.TClassStatus.Not_Start) {
        this.isClassStart = true;
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (val) => {
      this.isStageUp = val;
    });
    this.supportFileMessage = TCIC.SDK.instance.isFeatureAvailable('FileMessage');
  },
  methods: {
    handleReceiverUpdate(receiver) {
      this.currentPrivateChatReceiver = receiver;
    },
    async handleMobileChange() {
      if (this.supportFileMessage) {
        const checkRes = this.getAllKindsOfFile(this.$refs.imagePicker);
        if (!checkRes) {
          return false;
        }
        const checkValidateImageByLoading = await this.validateImageByLoadingWithoutToast(checkRes);
        const size = TCIC.SDK.instance.isMac() ? (checkRes.size / 1000 / 1000).toFixed(2) : (checkRes.size / 1024 / 1024).toFixed(2);
        if (checkValidateImageByLoading && size < 20) {
          this.sendImgMsg(this.file, this.imgUrl, null);
        } else {
          this.sendFileMsg(this.file, this.imgUrl, null);
        }
        // this.$refs.imagePicker.value = null;
        this.imagePickerIndex += 1;
      } else {
        const checkRes = this.getImageFile(this.$refs.imagePicker);
        if (!checkRes) {
          return false;
        }
        const checkValidateImageByLoading = await this.validateImageByLoading(checkRes);
        if (!checkValidateImageByLoading) {
          return false;
        }

        this.sendImgMsg(this.file, this.imgUrl, null);
        // this.$refs.imagePicker.value = null;
        this.imagePickerIndex += 1;
      }
    },
    getAllKindsOfFile(imgFile) {
      console.log('sendAllKindsOfFile, inputRes', imgFile);
      TCIC.SDK.instance.reportLog(
          'sendAllKindsOfFile',
          `getImageFile, inputRes.files.length ${imgFile?.files?.length}, inputRes.name ${imgFile?.name}, inputRes.size ${imgFile?.size}`,
      );
      // 统一格式
      if (imgFile?.files) {
        if (imgFile?.files.length > 0) {
          this.file = imgFile;
        } else if (imgFile.name) {
          this.file = {
            files: [imgFile],
          };
        } else {
          this.file = imgFile;
        }
      } else {
        this.file = {
          files: [imgFile],
        };
      }
      const singleFile = this.file.files[0];
      console.log('sendImgMsg, singleFile', singleFile);
      TCIC.SDK.instance.reportLog(
          'sendAllKindsOfFile',
          singleFile ? `singleFile, name ${singleFile?.name}, type ${singleFile?.type}, size ${singleFile?.size}` : 'singleFile, no file',
      );
      if (!singleFile) {
        // 没选文件不用提示
        return false;
      }
      // 加个校验，以防 accept 没生效
      const tmpIndex = singleFile.name.lastIndexOf('.');
      const fileType = tmpIndex >= 0 ? singleFile.name.slice(tmpIndex + 1) : '';
      const bytes = singleFile?.size;
      if (!bytes) {
        window.showToast(i18next.t('选取的图片文件不能为空'), 'error');
        return false;
      }
      const size = TCIC.SDK.instance.isMac()
          ? (bytes / 1000 / 1000).toFixed(2)
          : (bytes / 1024 / 1024).toFixed(2);
      const isImage = VALID_IMG_TYPES.indexOf(fileType.toUpperCase()) < 0;
      if (isImage) {
        if (size >= 20) {
          window.showToast(i18next.t('选取的图片文件大小不能超过20M'), 'error');
          return false;
        }
      } else {
        if (size >= 100) {
          window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
          return false;
        }
      }
      try {
        window.URL.revokeObjectURL(this.imgUrl);
      } catch (err) {
        // 注意这里不用 return false
        TCIC.SDK.instance.reportLog('sendImgMsg', `revokeObjectURL error, ${err?.message}`);
      }
      try {
        this.imgUrl = window.URL.createObjectURL(singleFile);
      } catch (err) {
        TCIC.SDK.instance.reportLog('sendImgMsg', `createObjectURL error, ${err?.message}`);
        return false;
      }
      return singleFile;
    },

    getImageFile(imgFile) {
      console.log('sendImgMsg, inputRes', imgFile);
      TCIC.SDK.instance.reportLog(
        'sendImgMsg',
        `getImageFile, inputRes.files.length ${imgFile?.files?.length}, inputRes.name ${imgFile?.name}, inputRes.size ${imgFile?.size}`,
      );
      // 统一格式
      if (imgFile?.files) {
        if (imgFile?.files.length > 0) {
          this.file = imgFile;
        } else if (imgFile.name) {
          this.file = {
            files: [imgFile],
          };
        } else {
          this.file = imgFile;
        }
      } else {
        this.file = {
          files: [imgFile],
        };
      }
      const singleFile = this.file.files[0];
      console.log('sendImgMsg, singleFile', singleFile);
      TCIC.SDK.instance.reportLog(
        'sendImgMsg',
        singleFile ? `singleFile, name ${singleFile?.name}, type ${singleFile?.type}, size ${singleFile?.size}` : 'singleFile, no file',
      );
      if (!singleFile) {
        // 没选文件不用提示
        return false;
      }
      // 加个校验，以防 accept 没生效
      const tmpIndex = singleFile.name.lastIndexOf('.');
      const fileType = tmpIndex >= 0 ? singleFile.name.slice(tmpIndex + 1) : '';
      if (VALID_IMG_TYPES.indexOf(fileType.toUpperCase()) < 0) {
        window.showToast(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件'), 'error');
        return false;
      }
      const bytes = singleFile?.size;
      if (!bytes) {
        window.showToast(i18next.t('选取的图片文件不能为空'), 'error');
        return false;
      }
      const size = TCIC.SDK.instance.isMac()
        ? (bytes / 1000 / 1000).toFixed(2)
        : (bytes / 1024 / 1024).toFixed(2);
      if (size >= 20) {
        window.showToast(i18next.t('选取的图片文件大小不能超过20M'), 'error');
        return false;
      }
      try {
        window.URL.revokeObjectURL(this.imgUrl);
      } catch (err) {
        // 注意这里不用 return false
        TCIC.SDK.instance.reportLog('sendImgMsg', `revokeObjectURL error, ${err?.message}`);
      }
      try {
        this.imgUrl = window.URL.createObjectURL(singleFile);
      } catch (err) {
        TCIC.SDK.instance.reportLog('sendImgMsg', `createObjectURL error, ${err?.message}`);
        return false;
      }
      return singleFile;
    },
    showEmoji() {
      this.emojiSelected = true;
      this.emojiClosed = false;
    },
    hideEmoji() {
      this.emojiSelected = false;
      this.emojiClosed = true;
    },
    closeEmoji() {
      if (this.$refs.popover) {
        this.$refs.popover.doClose();
        this.emojiClosed = true;
      }
    },
    sendEmoji(emojiText) {
      // 巡课角色不允许发送消息
      if (this.isSupervisor) {
        return;
      }

      this.sendMsg(emojiText);
      this.closeEmoji();
    },
    async textInputEnter() {
      // 巡课角色不允许发送消息
      if (this.isSupervisor) {
        return;
      }

      if (this.imgSend) {
        const judgeImageSizeRes = await this.judgeImageSize();
        if (!judgeImageSizeRes) {
          return;
        }
        if (this.pasteFile !== null) {
          this.sendImgMsg(this.pasteFile, this.imgUrl, null);
        } else {
          this.sendImgMsg(this.file, this.imgUrl, null);
        }
        this.$refs.editor.innerHTML = '';
        this.$refs.imagePicker.value = null;
        this.imgSend = false;
        this.inputEmpty = true;
        this.$nextTick(() => {
          this.$refs.input.setText(this.imgInputText);
          if (this.$refs.input.inputText.length > 0) {
            this.inputEmpty = false;
          }
        });
        return;
      }
      this.sendMsg(this.$refs.input.getPureText())
          .then(() => {
            this.inputEmpty = true;
            this.$refs.input.clearText();
          });
    },

    textChange() {
      this.inputText = this.$refs.input.inputText;
      if (this.inputText.length > 0) {
        this.inputEmpty = false;
      } else {
        this.inputEmpty = true;
      }
    },

    textInputClick() {
      if (this.isMobile && !this.isWeb) {
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component')
            .getVueInstance()
            .showTextInput(this);
      }
    },
    onInputFile(event) {
      const isSilenceAll = this.currentSilenceMode === 3;
      const chatEnable = TCIC.SDK.instance.getState(TCIC.TMainState.Chat_Permission, true);
      if (isSilenceAll && this.isStudent) {
        window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }));
        event.preventDefault();
        return;
      }

      if (!chatEnable) {
        window.showToast(i18next.t('你已被禁言'));
        event.preventDefault();
        return;
      }
      if (!this.inputEmpty && !this.imgSend) {
        window.showToast(i18next.t('请先发送文字消息，再发送图片'));
        event.preventDefault();
      } else if (this.imgSending) {
        window.showToast(i18next.t('请等待当前图片上传完成'));
        event.preventDefault();
        return;
      }
    },
  },
};
</script>

<style lang="less">
.im-component-footer {
  margin-bottom: 10px;
  .im-component-input-mobile-smallscreen {
    resize: none;
    outline: none;
    border: none;
    background: none;
    width: 100%;
    font-weight: 400;
    color: #8A9099;
    text-align: center;
  }
  &.small-screen {
    margin: 10px 0 0 0;
    width: 100%;
    height: 93px;
    position:fixed;
    bottom:0;
    z-index: 1;
    .im-component-input-mobile-smallscreen {
      text-align: left;
    }
    .im-component-top-tools {
      height: 55px;
      padding: 10px;
      .im-component-tools-chat {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        border-radius: 10px;
      }
    }
  }
  &.show-reply {
    height: 124px;
  }

  .im-component-tool {
    margin-right: 4px;
  }
  &.small-screen{
    .im-component-top-tools{
      margin: 0;
    }

    .im-component-top-tools-line-first {
      display: flex;
      justify-content: space-between;
      padding: 10px 10px 0 10px;
      background-color: rgba(0, 0, 0, .3);
      .im-component-top-tools-line-first-right{
        flex-grow: 1;
        display: flex;
        flex-direction: row;
        justify-content: end;
        align-items: center;

        .im-component-img-tool {
          width: 20px;
          height: 20px;
          background-size: 20px 20px;
          cursor: pointer;
          position: relative;
          &.image.small-screen{
            margin: 0;
            width: 27px;
            height: 27px;
            background-size: 70% 70%;
          }
        }
      }
    }
  }
  .im-component-top-tools {
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(0, 0, 0, .3);
    border-radius: 2px;

    .im-component-tools-left {
      display: flex;
      align-items: center;
      flex-direction: row;
      padding-left: 8px;

      .im-component-tool {
        margin-right: 8px;

        &.small-screen {
          width: 20px;
          height: 20px;
          background-size: 20px 20px;
        }
      }
    }
    .im-component-img-tool {
      width: 20px;
      height: 20px;
      background-size: 20px 20px;
      cursor: pointer;
      &.image.small-screen{
        width:35px;
        height:35px;
        background-size: 70% 70%;
      }
    }

    .im-component-img-tool input{
      width: 20px;
      height: 20px;
      opacity: 0;
      background-size: 20px 20px;
      cursor: pointer;
    }

    .im-component-img-tool-mobile{
      width: 32px !important;
      height: 32px !important;
      background-size: 32px 32px !important;
      cursor: pointer;
    }

    .im-component-img-tool-mobile input{
      width: 32px !important;
      height: 32px !important;
      opacity: 0;
      background-size: 32px 32px !important;
      cursor: pointer;
    }

    .im-component-tools-right {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      padding-right: 0;
      &.small-screen {
        height: 100%;
      }
      .im-component-tool {
        margin-left: 8px;
      }
    }


  }

  .im-component-input-bar {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    // padding: 4px 4px;
    background-color: #515455;
    border-radius: 2px;

    .im-component-input-mobile {
      flex: 1;
      text-align: center;
      height: 36px;
      margin: 0 8px;
      text-indent: 28px;
      width: 100%;
    }

    .im-component-tool {
      width: 28px;
      height: 28px;
      background-size: 28px 28px;
    }
  }
}
.im-component-body {
  width: 100%;
}
.meme {
      background-image: url('./assets/ic_emoji.svg');
      &.small-screen{
        background-image: url('./assets/smile.svg');
      }
      opacity: .9;

      &:hover, &:active {
        border: none;
        opacity: 1;
      }
      &.small-screen {
        background-image: url('./assets/ic-smile.svg');
      }
    }

    .meme-selected {
      background-image: url('./assets/ic_emoji.svg');
      &.small-screen {
        background-image: url('./assets/ic-smile.svg');
      }
    }
.image {
  &.small-screen{
    background-image: url('./assets/plus.svg');
    margin-left: 10px;
    background-color: #292d38;
    border-radius: 50%;
  }
  &:hover {
    border-radius: 2px;
    border: 1px solid #c0c4cc57;
  }

  background-image: url('./assets/image.svg');
  background-size: 85%;

  &:hover {
    background-image: url('./assets/image_hover.svg');
    border: none
  }
}
.im-component-img-tool {
    width: 20px;
    height: 20px;
    background-size: 20px 20px;
    cursor: pointer;
    &.image.small-screen{
      width:35px;
      height:35px;
      background-size: 70% 70%;
    }
  }
.im-component-img-tool {
  width: 40px;
  height: 40px;
  margin: 4px;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  cursor: pointer;
}
.im-component-img-tool input{
  width: 40px;
  height: 40px;
  opacity: 0;
  cursor: pointer;
}
.im-component-meme-popover-mobile {
  margin-right: -110px !important;
  margin-top: -130px !important;
  padding: 0px !important;
  width: 324px;
  height: 77px;
  border-radius: 2px !important;
  border-color: rgb(28, 33, 49) !important;
  background-color: rgb(28, 33, 49) !important;

  .popper__arrow {
    display: none !important;
  }
}
.silence {
      &.small-screen{
        display: none;
      }
      &:hover {
        border-radius: 2px;
        border: 1px solid;
        border-color: #c0c4cc57;
      }

      background-image: url('./assets/ic_msg_disable.svg');
      background-size: 85%;

      &:hover {
        background-image: url('./assets/ic_msg_disable_hover.svg');
        border: none
      }

    }


    .silence-selected {
      &.small-screen {
        display: none;
      }
      &:hover {
        border-radius: 2px;
        border: 1px solid;
        border-color: #c0c4cc57;
      }

      background-image: url('./assets/ic_msg_enable.svg');

      &:hover {
        background-image: url('./assets/ic_msg_enable_hover.svg');
        border: none
      }
    }
</style>
