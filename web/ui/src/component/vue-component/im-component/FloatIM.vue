<template>
  <Box
    :class="['float-im-component inComponent', {
      'tcic': isTCICComponent,
      'small-screen': isSmallScreen,
    }]"
    :header="showHeader"
    :divider="showHeader"
    :closable="!isSmallScreen"
    :floatable="!isSmallScreen"
    :float-tip="[$t('移回侧边栏'), $t('独立弹出')]"
    :is-float="isFloat"
    :style="isFloat && {
      width: `${width}px`,
      height: `${height}px`,
      transform: `translate(${resizingOffsetX}px, ${resizingOffsetY}px)`,
    }"
    @float="toFloat"
    @fixed="toFixed"
    @hide="hide"
  >
    <template #title>
      <section
        v-if="isSmallScreen"
        class="small-title"
      >
        <div class="operations-left">
          <div class="tool-tips">
            {{ toolTips }}
          </div>
          <div
            :style="{visibility: isTeacher ? 'hidden' : 'hidden'}"
            :class="['mute-operation', isMuteAll? 'cancel-mute': 'mute']"
            @click="triggerSwitchSilence"
          >
            {{ isMuteAll ? $t('解除全员禁言'): $t('全员禁言') }}
          </div>
        </div>
        <div class="operations">
          <div
            class="ui-box-header-close"
            @click="hide"
          >
            <i class="el-icon-close icon" />
          </div>
        </div>
      </section>
      <span v-else>{{ toolTips }}</span>
    </template>
    <template #content>
      <div class="float-im-component-body">
        <IMComponent ref="imRef" />
      </div>
      <template v-if="isFloat">
        <div
          class="float-im-resize-handle top"
          :data-active="resizeParams.x === 0 && resizeParams.y === -1"
          @mousedown="event => startResize(event, {x:0, y: -1})"
        />
        <div
          class="float-im-resize-handle right-top"
          :data-active="resizeParams.x === 1 && resizeParams.y === -1"
          @mousedown="event => startResize(event, {x:1, y: -1})"
        />
        <div
          class="float-im-resize-handle right"
          :data-active="resizeParams.x === 1 && resizeParams.y === 0"
          @mousedown="event => startResize(event, {x:1, y: 0})"
        />
        <div
          class="float-im-resize-handle right-bottom"
          :data-active="resizeParams.x === 1 && resizeParams === 1"
          @mousedown="event => startResize(event, {x:1, y: 1})"
        />
        <div
          class="float-im-resize-handle bottom"
          :data-active="resizeParams.x === 0 && resizeParams.y === 1"
          @mousedown="event => startResize(event, {x:0, y: 1})"
        />
        <div
          class="float-im-resize-handle left-bottom"
          :data-active="resizeParams.x === -1 && resizeParams.y === 1"
          @mousedown="event => startResize(event, {x: -1, y: 1})"
        />
        <div
          class="float-im-resize-handle left"
          :data-active="resizeParams.x === -1 && resizeParams.y === 0"
          @mousedown="event => startResize(event, {x: -1, y: 0})"
        />
        <div
          class="float-im-resize-handle left-top"
          :data-active="resizeParams.x === -1 && resizeParams.y === -1"
          @mousedown="event => startResize(event, {x: -1, y: -1})"
        />
      </template>
    </template>
  </Box>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import IMComponent from './IM.vue';
import Box from '@/component/ui-component/box-component/Box';
import { headerEventHub } from '../header-component/HeaderEventHub';
export default {
  components: {
    IMComponent,
    Box,
  },
  extends: BaseComponent,
  props: {
    showHeader: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      toolTips: i18next.t('消息'),
      isFloat: false,
      width: 450,
      height: 600,
      minWidth: 300,
      minHeight: 482,
      resizeParams: { x: 0, y: 0 },
      isResizing: false,
      resizingOffsetX: 0, // 调整窗口尺寸时对左上角坐标产生的偏移量
      resizingOffsetY: 0,
    };
  },
  computed: {
    isTeacher() {
      return  TCIC.SDK.instance.isTeacher();
    },
    isMuteAll() {
      return this.$refs?.imRef?.silenceAll || this.$refs?.imRef?.currentSilenceMode === 3;
    },
  },
  created() {
    this.offsetX = 0;
    this.offsetY = 0;
  },
  mounted() {
    this.$EventBus.$on('float-im-is-float', (val) => {
      this.isFloat = val;
      this.toggleDraggable(false);
    });
    if (TCIC.SDK.instance.isInteractClass() || TCIC.SDK.instance.isUnitedClass()) {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
        // 有sideIM时关闭消息盒子
        const hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(layout).sideIM;
        if (hasSideIM) {
          this.hide();
        }
      });
    }

    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      // 切换至竖屏时关闭强制消息盒子
      if (this.isSmallScreen && orientation === TCIC.TDeviceOrientation.Portrait) {
        this.hide();
      }
    });
  },

  methods: {
    triggerSwitchSilence() {
      this.$refs?.imRef?.switchAllSilence && this.$refs?.imRef?.switchAllSilence();
    },
    toggleDraggable(draggable) {
      this.toggleComponentDrag(draggable, '.float-im-component .ui-box-header');
    },
    hide() {
      this.toggleDraggable(false);
      TCIC.SDK.instance.setState(Constant.TStateShowChatBox, false);
      // 通知父组件该组件即将被隐藏（主要供Header里使用）
      if (!this.isTCICComponent) {
        this.$emit('hide');
      } else {
        headerEventHub.$emit('float-im-hide');
        this.updateComponent({
          display: 'none',
          transform: 'none',
        }).then();
      }
    },

    resetData() {
      this.offsetX = 0;
      this.offsetY = 0;
      this.width = 450;
      this.height = 600;
    },
    toFloat() {
      this.isFloat = true;
      this.resetData();
      const layout = {
        width: 'auto',
        height: 'auto',
        top: '14%',
        left: '32%',
        style: 'overflow: visible',
      };
      TCIC.SDK.instance.updateComponent('float-im-component', layout).then();
      this.toggleDraggable(true);
      this.$EventBus.$emit('im-folat', true);
    },
    toFixed() {
      this.isFloat = false;
      this.resetData();
      const headerHeight = document.getElementById('header-component-default').clientHeight;
      const layout = {
        width: '40%',
        height: `calc(100% - ${headerHeight}px)`,
        top: `${headerHeight}px`,
        left: 'unset',
        transform: 'none',
        style: 'right: 0;',
      };
      TCIC.SDK.instance.updateComponent('float-im-component', layout).then();
      this.toggleDraggable(false);
      this.$EventBus.$emit('im-folat', false);
    },
    startResize(e, params) {
      if (this.isResizing) {
        return;
      }
      this.isResizing = true;
      this.resizeParams = params;

      this.startX = e.clientX;
      this.startY = e.clientY;
      this.startWidth = this.width;
      this.startHeight = this.height;

      document.documentElement.addEventListener('mousemove', this.updateResize, {
        capture: true,
      });
      document.documentElement.addEventListener('mouseup', this.stopResize, {
        capture: true,
      });
    },
    updateResize(e) {
      const deltaX = e.clientX - this.startX;
      const deltaY = e.clientY - this.startY;

      const beforeWidth = this.width;
      const beforeHeight = this.height;

      this.width = Math.max(this.minWidth, this.startWidth + deltaX * this.resizeParams.x);
      this.height = Math.max(this.minHeight, this.startHeight + deltaY * this.resizeParams.y);

      const deltaWidth = this.width - beforeWidth;
      const deltaHeight = this.height - beforeHeight;

      // 如果是向负坐标拉伸，盒子宽度变大的同时需要调整盒子位置，防止向左拉伸，宽度增在在右边
      this.resizingOffsetX += this.resizeParams.x < 0 ? -deltaWidth : 0;
      this.resizingOffsetY += this.resizeParams.y < 0 ? -deltaHeight : 0;
    },
    stopResize() {
      this.isResizing = false;
      const { resizingOffsetX, resizingOffsetY } = this;
      console.log(`offsetX: ${this.offsetX}, resizingOffsetY: ${this.resizingOffsetX}`);
      if (resizingOffsetX !== 0 || resizingOffsetY !== 0) {
        this.offsetX += resizingOffsetX;
        this.offsetY += resizingOffsetY;
        TCIC.SDK.instance.updateComponent('float-im-component', {
          transform: `translate(${this.offsetX}px, ${this.offsetY}px)`,
        });
      }

      this.resizingOffsetX = 0;
      this.resizingOffsetY = 0;
      this.resizeParams = { x: 0, y: 0 };

      document.documentElement.removeEventListener('mousemove', this.updateResize, {
        capture: true,
      });
      document.documentElement.removeEventListener('mouseup', this.stopResize, {
        capture: true,
      });
    },
  },
};
</script>

<style lang="less">
.float-im-component {
  width: 100%;
  height: 100%;
  // 这个过渡不知道有什么用，对聊天框resize有影响先注掉
  // transition: 0.5s all;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  padding: 12px 0;
  padding-top: 0;
  background: #1C2131 !important;
  border-radius: 4px;
  position: relative;
  overflow: visible !important;
  .float-im-resize-handle {
    position: absolute;
    z-index: 1;
    user-select: none;
    --handle-size: 16px;

    &[data-active] {
      left: -100vw !important;
      right: -100vw !important;
      bottom: -100vw !important;
      top: -100vw !important;
      width: auto !important;
      height: auto !important;
      z-index: 3;
    }

    &.bottom, &.top {
      width: 100%;
      height: var(--handle-size);
      left: 0;
    }
    &.left, &.right {
      width: var(--handle-size);
      height: 100%;
      top: 0;
    }
    &.right-top, &.right-bottom, &.left-top, &.left-bottom {
      width: calc(var(--handle-size) * 2);
      height: calc(var(--handle-size) * 2);
      z-index: 2;
    }
    &.top {
      top: calc(var(--handle-size) / 2 * -1);
      cursor: row-resize;
    }
    &.right {
      right: calc(var(--handle-size) / 2 * -1);
      cursor: col-resize;
    }
    &.bottom {
      bottom: calc(var(--handle-size) / 2 * -1);
      cursor: row-resize;
    }
    &.left {
      left: calc(var(--handle-size) / 2 * -1);
      cursor: col-resize;
    }
    &.right-top {
      right: calc(var(--handle-size) * -1);
      top: calc(var(--handle-size) * -1);
      cursor: ne-resize;
    }
    &.right-bottom {
      right: calc(var(--handle-size) * -1);
      bottom: calc(var(--handle-size) * -1);
      cursor: se-resize;
    }
    &.left-bottom {
      left: calc(var(--handle-size) * -1);
      bottom: calc(var(--handle-size) * -1);
      cursor: sw-resize;
    }
    &.left-top {
      left: calc(var(--handle-size) * -1);
      top: calc(var(--handle-size) * -1);
      cursor: nw-resize;
    }
  }
  &.tcic {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
  }

  &.small-screen {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 6px 0;
    color: #fff;
    .component-header-floatim {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 8px;
      height: 30px;

      .header-title-floatim {
        font-size: 14px;
        font-weight: 500;
        color: #8A9099;
        line-height: 14px;
        height: 14px;
      }

      .header-close-floatim {
        width: 14px;
        height: 14px;
        border-radius: 2px;
      }
    }
    .small-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .operations {
        display: flex;
        align-items: center;
      }
      .operations-left {
        display: flex;
        align-items: center;
        justify-content: start;
      }
      .mute-operation {
        line-height: 24px;
        margin-left: 8px;
      }
      .ui-box-header-close{
        background-color: transparent;
      }
      .tool-tips {
        font-size: 16px;
        color: #fff;
      }
      .cancel-mute {
        color: #0566fb;
      }
      .mute {
        color: #a23639;
      }
    }
  }

  .float-im-component-divider-wrapper {
    padding: 0px;
    height: 1px;
    width: 400px;
    background-color: rgba(184, 184, 184, 0.1);
  }

  .float-im-component-body {
    padding: 0px;
    flex: 1;
    overflow: hidden;
    .im-component.mobile{
      .im-component-body {
        width: 100%;
      }
      .im-component-footer.small-screen {
        position: absolute;
      }
    }
  }

  .component-header-floatim {
    //cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 16px 24px 16px;
    height: 76px;
  }

  .el-popover.header-component-popover {
    background: #1C2131 !important;
  }
}
</style>
