<template>
  <!-- “互动课 + 公开课的PC/Pad端 + 巡课” 弹出的通知对话框-->
  <MixedPopper
    :title="$t('公告')"
    :visible="dialogVisible"
    :type="isSmallScreen ? 'drawer' : 'dialog'"

    dialog-custom-class="notice-dialog"
    :dialog-modal-append-to-body="true"
    :dialog-append-to-body="true"
    :dialog-close-on-click-modal="false"
    :dialog-top="marginTop"
    :dialog-show-close="false"
    :dialog-center="true"

    drawer-custom-class="notice-drawer"

    @update:visible="(value) => visible = value"
  >
    <div
      v-if="!isSmallScreen"
      slot="title"
    >
      <div class="component-header dialog">
        <div class="header-title">
          {{ $t('公告') }}
        </div>
        <div
          class="header-close"
          @click="hidden"
        >
          <i class="el-icon-close icon" />
        </div>
      </div>
    </div>
    <!-- 老师端 助教端 -->
    <template v-if="canEdit && isEditMode">
      <el-input
        id="noticeInput"
        v-model="notice"
        type="textarea"
        resize="none"
        :show-word-limit="true"
        :autosize="isSmallScreen ? { minRows: 8, maxRows: 8 } : { minRows: 4, maxRows: 8 }"
        :placeholder="$t('请输入公告内容...')"
        :maxlength="maxLength"
        :clearable="true"
        @input="inputTextChanged"
      />
      <div
        slot="footer"
        class="mobile-drawer__btn-group"
      >
        <el-button
          :class="{'footer-button__disabled':btnDisable}"
          type="primary"
          class="footer-button"
          @click="announceNotice"
        >
          {{ $t('发布') }}
        </el-button>
      </div>
    </template>
    <!-- 学生端 -->
    <template v-else>
      <div :class="contentText ? 'content' : 'empty'">
        <div class="el-textarea">
          <div
            class="el-textarea__inner"
            :class="studentClass"
          >
            <span v-if="contentText">{{ contentText }}</span>
            <span
              v-else
              class="no-notice"
            >
              {{ defaultText }}
            </span>
          </div>
        </div>
      </div>
      <div
        slot="footer"
        class="mobile-drawer__btn-group"
      >
        <el-button
          type="primary"
          class="footer-button"
          @click="hidden"
        >
          {{ $t('我知道了') }}
        </el-button>
      </div>
    </template>
  </MixedPopper>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';

export default {
  name: 'NoticeDialog',
  components: {
    MixedPopper,
  },
  extends: BaseComponent,
  data() {
    return {
      maxLength: 500,
      isMobile: TCIC.SDK.instance.isMobile(),
      isLiveClass: false, // 是否公开课
      isPortrait: false, // 是否为竖屏
      canEdit: false,
      isEditMode: false,
      isRecordMode: false,    // 是否录制模式
      visible: false,
      isDeviceDetect: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect),
      inUpdateTask: false,
      notice: '',
      defaultText: `${i18next.t('暂无公告')}`,
      contentText: '',
      taskSeq: -1,
      classId: '',
      taskId: 'classNotice',
      keyboardHeight: 0,
      storeInfo: {
        lastNotice: '',  // 上次发布的公告
        lastInputNotice: '',  // 老师上次输入的公告
        readTaskSeq: -1,
        userId: '',
        classId: '',
      },
      roleInfo: {},
    };
  },
  computed: {
    marginTop() {
      if (TCIC.SDK.instance.isPad() && this.canEdit && this.keyboardHeight > 0) {
        const input = document.getElementById('noticeInput');
        let top = 0.2 * document.body.clientHeight;
        if (input) {
          const total = 68 + (input.clientHeight + 24) + 56 + 28;  // header + body + footer + gap
          const newTop = document.body.clientHeight - this.keyboardHeight - total;
          if (newTop < top) {
            top = newTop;
          }
        }
        if (top < 45) {
          top = 45;
        }
        return `${top}px`;
      }
      return this.isSmallScreen ? '12%' : '20%';
    },
    dialogVisible() {
      if (this.canEdit) {
        return this.visible && !this.isDeviceDetect;
      }

      if (this.isSmallScreen && this.isLiveClass) {
        // 手机端公开课不展示
        return false;
      }
      return this.visible && !this.isDeviceDetect && !this.isRecordMode;
    },
    btnDisable() {
      return (this.notice.length === 0 && this.storeInfo.lastNotice.length === 0) || this.inUpdateTask || this.notice.length > this.maxLength;
    },
    studentClass() {
      return this.isSmallScreen ? 'textarea-small-screen' : 'textarea-student';
    },
  },
  watch: {
    notice(newVal) {
      if (newVal && newVal.length > 0) {
        const urlPattern = /(https?|http|HTTP|HTTPS|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
        this.contentText = newVal.replace(urlPattern, match => `<span style="color:#006EFF;" class="select">${match}</span>`);
      } else {
        this.contentText = '';
      }
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.canEdit = TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant();
    this.isDeviceDetect = TCIC.SDK.instance.getState(Constant.TStateDeviceDetect);
    this.makeSureClassJoined(this.onJoinClass);
    const DeviceOrientation = TCIC.TMainState.Device_Orientation;
    const Portrait = TCIC.TDeviceOrientation.Portrait;

    this.isPortrait = TCIC.SDK.instance.getState(DeviceOrientation, Portrait) === Portrait;
    // 设备检测完成后才显示
    this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);
    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (recordMode) => {
      this.isRecordMode = recordMode;
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Soft_Keyboard, (event) => {
      const webScale = TCIC.SDK.instance.getState(Constant.TStateWebScale, 1);
      // 键盘展开时，高度值大于0
      if (event.kbHeight > 0) {
        this.keyboardHeight = event.kbHeight;
      } else {
        this.keyboardHeight = 0;
      }
      this.keyboardHeight = this.keyboardHeight / webScale;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === Portrait;
    });
  },

  methods: {
    onJoinClass() {
      this.canEdit = TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.classId = `${classInfo.classId}`;
      this.loadStoreInfo();
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
      const seqId = this.storeInfo.readTaskSeq >= 0 ? this.storeInfo.readTaskSeq : 0;
      TCIC.SDK.instance.getTasks(seqId).then((result) => {
        if (result.tasks !== null && Array.isArray(result.tasks)) {
          for (let index = result.tasks.length - 1; index >= 0; index--) {
            const taskInfo = result.tasks[index];
            if (taskInfo.taskId === this.taskId && taskInfo.status !== 0) {
              TCIC.SDK.instance.reportLog('received-notice', `[taskInfo] content: ${taskInfo.content}, seq: ${taskInfo.seq}, localSeq: ${this.storeInfo.readTaskSeq}`);
              this.storeInfo.lastNotice = taskInfo.content;
              if (!this.canEdit) {
                this.notice = taskInfo.content;
              }
              this.taskSeq = taskInfo.seq;
              break;
            }
          }
        }
        // 学生发现未阅读公告， 需弹出公告
        if (!this.canEdit && this.taskSeq > this.storeInfo.readTaskSeq && this.notice.length > 0) {
          this.saveStoreInfo();
          this.show();
        }
      });
    },

    storekey() {
      return `noticeStoreInfo${this.classId}`;
    },

    loadStoreInfo() {
      const json = localStorage.getItem(this.storekey());
      if (json && json.length > 0) {
        const storeInfo = JSON.parse(json);
        if (storeInfo.userId === TCIC.SDK.instance.getUserId() && storeInfo.classId === this.classId) {
          this.notice = storeInfo.lastInputNotice;
          this.taskSeq = storeInfo.readTaskSeq;
          this.storeInfo = storeInfo;
        }
      }
    },

    saveStoreInfo() {
      this.storeInfo.lastInputNotice = this.notice;
      this.storeInfo.userId = TCIC.SDK.instance.getUserId();
      this.storeInfo.classId = `${this.classId}`;
      this.storeInfo.readTaskSeq = this.taskSeq;
      if (this.storeInfo.classId.length > 0 && this.storeInfo.userId.length > 0) {
        const jsonStr = JSON.stringify(this.storeInfo);
        localStorage.setItem(this.storekey(), jsonStr);
      }
    },

    beforeDestroy() {
      TCIC.SDK.instance.off(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    },

    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.taskId || taskInfo.status === 0) {
        return;
      }
      TCIC.SDK.instance.reportLog('received-notice', `[taskInfo] content: ${taskInfo.content}, seq: ${taskInfo.seq}, localSeq: ${this.storeInfo.readTaskSeq}`);
      if (taskInfo.seq <= this.taskSeq) {
        console.warn(`[NoticeDialog] received-notice but seq is old ${taskInfo.seq} <= ${this.taskSeq}`);
        return;
      }
      this.notice = taskInfo.content;
      this.storeInfo.lastNotice = taskInfo.content;
      this.taskSeq = taskInfo.seq;
      if (!this.canEdit || !taskInfo.isSelfUpdate) {
        // 学生需要弹出弹窗，不是自己写的也要弹出
        let editMode = undefined;
        if (this.canEdit) {
          if (this.visible && this.isEditMode) {
            // 编辑中收到其他老师/助教发布的公告，弹个提示
            window.showToast(i18next.t('公告已更新'));
          } else {
            // 不可见或非编辑中，只是显示
            editMode = false;
          }
        }
        if (this.storeInfo.readTaskSeq < taskInfo.seq && taskInfo.content.length > 0) {
          this.show(editMode);
        } else if (taskInfo.content.length === 0 && this.visible && !this.isEditMode) {
          // 老师清空公告，关闭弹窗
          this.hidden();
        }
      }
      this.saveStoreInfo();
    },

    announceNotice() {
      if (this.btnDisable) {
        return;
      }
      const noticeContent = this.notice.trim();
      if (noticeContent.length > this.maxLength) {
        window.showToast(i18next.t('您输入的内容超过限定长度'));
        return;
      }
      TCIC.SDK.instance.reportLog('announce-notice', `[content]: ${noticeContent}`);
      if (noticeContent.length <= 0) {
        TCIC.SDK.instance.showMessageBox('', i18next.t('确定要清除公告？清除后{{arg_0}}不会收到公告。', { arg_0: this.roleInfo.student }), [i18next.t('是'), i18next.t('否')], (index) => {
          if (index === 0) {
            this.inUpdateTask = true;
            TCIC.SDK.instance.updateTask(this.taskId, '', - 1, 0, '').then((task) => {
              this.notice = '';
              this.storeInfo.lastNotice = '';
              this.inUpdateTask = false;
              window.showToast(i18next.t('已清除'));
              this.saveStoreInfo();
              this.hidden();
            })
              .catch((error) => {
                this.inUpdateTask = false;
                let message = error.errorMsg;
                if (error.errorCode && error.errorCode === 1008) {
                  message = i18next.t('发布失败，公告内容包含非法内容(如色情，恐怖，政治等)');
                }
                window.showToast(message);
              });
          }
        });
        return;
      }
      this.inUpdateTask = true;
      TCIC.SDK.instance.updateTask(this.taskId, noticeContent, - 1, 0, '').then((task) => {
        window.showToast(i18next.t('已发布'));
        this.storeInfo.lastNotice = this.notice;
        this.inUpdateTask = false;
        this.saveStoreInfo();
        this.hidden();
      })
        .catch((error) => {
          let message = error.errorMsg;
          if (error.errorCode && error.errorCode === 1008) {
            message = i18next.t('发布失败，公告内容包含非法内容(如色情，恐怖，政治等)');
          }
          window.showToast(message);
          this.inUpdateTask = false;
        });
    },

    deviceDetectHandler(state) {
      this.isDeviceDetect = state;
      if (!state && this.visible) {
        this.show();
      }
    },

    show(editMode) {
      if (this.canEdit && typeof editMode === 'boolean') {
        // 指定了editMode
        this.isEditMode = editMode;
      } else {
        this.isEditMode = this.canEdit;
      }
      // console.log(`[NoticeDialog] show, canEdit ${this.canEdit}, isEditMode ${this.isEditMode}`);
      this.visible = true;
      this.updateTbmTarget();
      if (this.canEdit) {
        this.$nextTick(() => window.tbm.select('notice', 'content'));
      };
    },
    inputTextChanged() {
      if (this.keyboardHeight > 0) {
        // 触发marginTop的计算
        this.keyboardHeight = this.keyboardHeight + 0.00001;
      }
    },
    hidden() {
      // console.log(`[NoticeDialog] hide, canEdit ${this.canEdit}, isEditMode ${this.isEditMode}`);
      this.visible = false;
      this.notice = this.storeInfo.lastNotice;
      this.saveStoreInfo();
      this.updateTbmTarget();
    },
    handleContentTextClick(event) {
      if (event.target.tagName.toLowerCase() === 'span' && event.target.className === 'select') {
        const url = event.target.innerText;
        if (url && url.length > 0) {
          TCIC.SDK.instance.openBrowser(url);
        }
      }
    },
    updateTbmTarget() {
      if (this.visible) {
        this.$nextTick(() => {
          window.tbm.updateTarget('notice', [
            window.tbm.generateNode(this.$el.querySelector('div.header-close')),
          ], 'title');
          window.tbm.updateTarget('notice', [
            window.tbm.generateNode(this.$el.querySelector('textarea'), () => {
              const textarea = this.$el.querySelector('textarea');
              if (document.activeElement === textarea) {  // 交给系统处理
                return true;
              }
              textarea.focus();
            }),
          ], 'content');
          if (this.canEdit) {
            window.tbm.updateTarget(
              'notice',
              Array.from(this.$el.querySelectorAll('.el-button'))
                .map(item => window.tbm.generateNode(item)),
              'button',
            );
          }
          window.tbm.lockTarget('notice', 'button');
        });
      } else {
        window.tbm.unlockTarget('notice');
        window.tbm.clearTarget('notice');
      }
    },
  },
};
</script>
<style lang="less">
.notice-drawer {
  .el-textarea {
    margin-top: 8px;
    .el-textarea__inner {
      border: none;
      border-radius: 0;
      background: #1B1E26;
      color: #FFF;
    }

    .el-input__count {
      background: #1B1E26;
      color: #969FB4;
    }
  }

  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .no-notice {
    text-align: center;
    padding: 8px 0;
    color: #999;
  }
}

.notice-dialog {
  background: #1C2131 !important;
  width: 600px !important;
  max-width: 60%;

  // 覆盖样式
  .el-dialog__body {
    padding: 0 16px 24px 16px !important;
    .el-textarea__inner {
      border: 0px;
      padding: 0px;
      background-color: transparent;
      color: rgb(255, 255, 255);
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      white-space:pre-wrap;
      white-space:-moz-pre-wrap;
      white-space:-pre-wrap;
      white-space:-o-pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      .select {
        cursor: default;
        user-select: text;
        -webkit-user-select: text;
      }
      .bluetext {
        color: #006EFF;
      }
    }
    .textarea-small-screen {
      max-height: 68px;
      overflow-y: auto;
      resize: none;
    }

    .textarea-student {
      max-height: 192px;
      overflow-y: auto;
      resize: none;
    }

    .el-input__count {
      background: transparent;
      bottom: -15px;
    }
  }
  // 覆盖样式
  .el-dialog__header {
    text-align: left;
    padding: 24px 16px 16px 16px;
  }

  .el-dialog__footer {
    padding: 0 16px 24px 16px;
    .footer-button {
      padding: 5px 22px;
      background: #006EFF;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;

    }
    .footer-button__disabled {
      opacity: 0.5;
      border: 0;
    }
  }
  .no-notice {
    text-align: center;
    padding: 8px 0;
    color: #999;
  }
}
@media (max-width: 530px) {
  .notice-dialog {
    .el-dialog__header{
      padding: 8px;
      .component-header {
        height: 20px;
      }
      .header-title {
        font-size: 14px;
      }
    }
    .el-dialog__footer {
      padding: 0 10px 16px;
      .footer-button {
        padding: 3px 8px;
        font-size: 13px;
      }
    }
  }
}
</style>
