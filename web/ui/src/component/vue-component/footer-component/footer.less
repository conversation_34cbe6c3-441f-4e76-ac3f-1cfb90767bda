@--color-primary: #006EFF;
//@--color-public: #14181D;
//@--color-disable: #b9bbbc;
//@--color-back: #1C2131;

//分享下的footer
.footer-share-component {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  width: auto;
  height: 80px;
  padding: 10px 20px;
  background: linear-gradient(180deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
  box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
  border-radius: 4px;
  overflow: hidden;

  &.footer-fold {
    width: 80px;
  }

  // 底部展开/收起
  .footer-tool-fold {
    margin-left: 20px;

    &.up-show {
      margin-left: 0;
      i {
        transform: rotate(180deg);
      }
    }
    &.right-hidden {
      margin-left: 0;
      i {
        transform: rotate(270deg);
      }
    }
    &.left-show {
      margin-left: 0;
      i {
        transform: rotate(90deg);
      }
    }
    span {
      i {
        transform: rotate(0deg);
      }
    }
  }

}

//公开课下的footer
.footer-component {
  position: absolute;
  bottom: 0; // 产品说要贴下边
  //bottom: 10px;
  left: 10px;
  display: flex;
  align-items: center;
  width: calc(100% - 20px);
  height: 100%;
  padding: 0 20px;
  overflow: hidden;
  background: linear-gradient(180deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
  box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
  border-radius: 4px;
  animation: footer-fold-up 0.1s ease-in;
  &.short-mode {
    width: 400px;
  }
  &.screen-sharing-mode.live-class{
    // width: auto;
    .quick-im-component {
      width: 100%;
      .quick-im-component-input {
        width: 100%;
      }
    }
    // .footer-tool-fold {
    //   display: none;
    // }
  }
  &.short-mode.screen-sharing-mode.live-class{
    width: 380px;
  }
  &.footer-fold.screen-sharing-mode.live-class{
    width: 80px !important;
  }
  &.screen-sharing-mode.live-class.footer-component {
    width: auto;
  }
  &.footer-fold {
    width: 80px;
    right: 10px;
    padding-left: 0;
    //transition: all 0.1s;
    animation: footer-fold-down 0.1s ease-in;

    &.right-align {
      left: initial;
    }
  }
  // 小屏幕下的样式控制 ---- 整个footer集中
  &.small-screen {
    background: transparent;
    pointer-events: none;
    box-shadow: none;
    overflow: initial;
    &.is-none {
      display: none;
    }
    &.is-board-full {
      padding-right: 40px;
    }
    &.has-camera-foot {
      padding-right: 140px; // 120 + 20
    }
    &.is-board-full.has-camera-foot {
      padding-right: 150px; // 120 + 30
    }

    .filter-blur-bg {
      background: transparent;
      display: none;
    }

    // 举手
    .handup-icon__wrap {
      display: flex;
      align-items: center;
      min-width: 40px;
      height: 40px;
      padding: 0 10px;
      border-radius: 10px;
    }

    // 弹幕区
    .quick-im-component-tools {
      // 弹幕开关
      .quick-im-component-tools-toggle {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: rgba(28,33,49,.5);
        &.active, &.inactive {
          i {
            background-size: 20px 20px;
          }
        }
      }

      // 弹幕输入
      .quick-im-component-tools-input {
        width: 120px;
        min-width: 110px !important;
        height: 40px;
        margin-left: 8px;
        border-radius: 10px;
        box-shadow: none;

        .quick-im-component-input {
          margin-left: 6px;
          margin-right: 0;

          // 聊一聊英文下会换行so加宽宽度
          .im-component-input {
            width: 78px;
          }

          input {
            font-size: 14px;
          }

          &::after {
            min-width: 42px;
          }
        }

        .quick-im-component-meme {
          position: relative;
          z-index: 9;
          width: 26px;
          height: 26px;
          margin-left: 10px;
          background-size: 26px 26px;
        }
      }
    }

    .quick-im-component-msg-list {
      padding-bottom: 1px;
    }

    .quick-im-component-popover {
      padding: 0px !important;
    }

    // 小屏幕下的连麦
    .student-stage {
      height: 40px;
      margin-top: 16px;
      margin-left: 10px;
      padding: 0 10px 0 10px;
      pointer-events: auto;
      background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
      opacity: 0.5;
      label {
        font-size: 12px;
      }
      .ic-s-stage {
        width: 26px;
        height: 26px;
      }
      .ic-s-stage-dynamic {
        &.disabled {
          .ic-s-stage {
            height: 22px;
            background: url("./assets/ic_mobile_stage_connecting.svg") center no-repeat;
            background-size: 98%;
          }
          .s-stage-loading {
            span {
              background: #20c77a;
            }
          }
        }
        .ic-s-stage {
          height: 22px;
        }
      }
    }

    .footer-tool-fold {
      display: none;
    }

    .video-icon-list {
      pointer-events: auto;
    }
  }
  // 连麦
  .student-stage {
    position: relative;
    display: flex;
    align-items: center;
    height: 56px;
    margin-left: 12px;
    padding: 0 24px 0 20px;
    border-radius: 10px;
    z-index: 999;
    pointer-events: auto;
    label {
      margin-left: 6px;
      font-size: 18px;
      color: #fff;
    }
    .ic-s-stage {
      display: flex;
      width: 40px;
      height: 40px;
      background: url("./assets/ic_mobile_stage.svg") center no-repeat;
      background-size: 98%;
      &.approve {
        background: url("./assets/ic_mobile_stage_refuse.svg") center no-repeat;
        background-size: 98%;
      }
    }
    .ic-s-stage-dynamic {
      .ic-s-stage {
        height: 34px;
      }
      .s-stage-loading {
        display: flex;
        justify-content: space-around;
        padding: 2px 5px 0 5px;
        span {
          display: inline-block;
          width: 2px;
          height: 2px;
          margin: 0 1px;
          border-radius: 50%;
          background: #11C53C;
          animation: stageLoad 1s ease infinite;
          &:nth-child(1) {
            animation-delay: 0.25s;
          }
          &:nth-child(2) {
            width: 3px;
            height: 3px;
            animation-delay: 0.5s;
          }
          &:nth-child(3) {
            width: 4px;
            height: 4px;
            animation-delay: 0.75s;
          }
        }
      }
    }
  }

  .quick-im-component {
    //width: auto;
    position: relative;
    z-index: 999;
  }

  .video-icon-list.quick-im {
    z-index: 999;
    pointer-events: auto;
  }

  // 工具区
  .footer-tool {
    height: 100%;
    display: flex;
    align-items: center;
  }
}

// 底部展开/收起  分享和公开课共用
.footer-tool-fold {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  //margin-top: 12px;
  margin-left: auto;
  cursor: pointer;

  &.right-hidden {
    i {
      transform: rotate(0deg);
    }
  }

  &.up-show {
    i {
      transform: rotate(90deg);
      transition: all .5s;
    }
  }

  &.left-show {
    i {
      transform: rotate(180deg);
      transition: all .5s;
    }
  }

  &:hover {
    span {
      background-color: @--color-primary;
      i {
        opacity: 1;
      }
    }
  }
  span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 4px;

    i {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url("./assets/icon_pack.svg") no-repeat center;
      transform: rotate(-90deg);
      transition: all .5s;
      opacity: 0.4;
    }
  }
}

.board-footer-component {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 连麦中动态效果
@keyframes stageLoad {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}

@keyframes footer-fold-up {
  0% {
    height: 0;
  }
  100% {
    height: 80px;
  }
}

@keyframes footer-fold-down {
  100% {
    height: 0;
  }
  0% {
    height: 80px;
  }
}


@media screen and(min-width: 1600px) and (max-width: 1919px) {
  html {
    font-size: 23px !important;
  }
}

@media screen and(min-width: 1400px) and (max-width: 1599px) {
  html {
    font-size: 22px !important;
  }
}

@media screen and(min-width: 1360px) and (max-width: 1439px) {
  html {
    font-size: 21px !important;
  }
}

@media screen and(max-width: 1359px) {
  html {
    font-size: 21px !important;
  }
}

@media screen and (max-width: 375px) {
  html {
    font-size: 21px !important;
  }
}
