apply plugin: 'com.android.application'
apply from: 'tools.gradle'

android {

    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        applicationId rootProject.ext.android.applicationId
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionName rootProject.ext.android.versionName
        versionCode rootProject.ext.android.versionCode as int
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }


    signingConfigs {
        release {
            storeFile file("$System.env.KEYSTORE_PATH")
            storePassword "$System.env.STOREPASS"
            keyAlias "$System.env.ALIAS"
            keyPassword "$System.env.KEYPASS"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    lintOptions {
        abortOnError false
    }

}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(':TCICSDK')

    implementation 'com.tencent.bugly:nativecrashreport:3.7.1'
    implementation 'com.tencent.bugly:crashreport_upgrade:1.4.5'

    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'org.jetbrains:annotations:15.0'

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
}
